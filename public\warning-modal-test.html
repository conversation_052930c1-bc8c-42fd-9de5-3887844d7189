<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Warning Modal Test</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <style>
        body {
            font-family: system-ui, -apple-system, sans-serif;
            margin: 0;
            padding: 2rem;
            background: #f3f4f6;
        }
        
        .test-button {
            background: #1e3a8a;
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 0.5rem;
            font-size: 1rem;
            cursor: pointer;
            transition: background-color 0.2s;
            margin-bottom: 1rem;
        }
        
        .test-button:hover {
            background: #1e40af;
        }

        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        h1 {
            color: #1e3a8a;
            margin-bottom: 1.5rem;
        }

        .button-group {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            margin-bottom: 2rem;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Warning Modal Demo</h1>
        <p class="mb-6">This page demonstrates the custom warning modal component with different configurations.</p>
        
        <h2 class="text-lg font-semibold mb-3">Basic Warning Modals</h2>
        <div class="button-group">
            <button class="test-button" onclick="showWarningModal('warning')">
                Warning Modal
            </button>
            <button class="test-button" onclick="showWarningModal('error')">
                Error Modal
            </button>
            <button class="test-button" onclick="showWarningModal('info')">
                Info Modal
            </button>
        </div>

        <h2 class="text-lg font-semibold mb-3">Subscription Cancellation</h2>
        <div class="button-group">
            <button class="test-button" onclick="showSubscriptionCancellationModal()">
                Cancel Subscription
            </button>
        </div>

        <h2 class="text-lg font-semibold mb-3">Account Deletion</h2>
        <div class="button-group">
            <button class="test-button" onclick="showAccountDeletionModal()">
                Delete Account
            </button>
        </div>
    </div>

    <script src="warning-modal.js"></script>
    <script>
        function showWarningModal(type) {
            let options = {
                title: 'Warning',
                message: 'This is a standard warning message.',
                confirmText: 'Confirm',
                cancelText: 'Cancel',
                icon: 'warning',
                confirmButtonStyle: 'danger'
            };

            if (type === 'error') {
                options.title = 'Error';
                options.message = 'This action cannot be completed due to an error.';
                options.icon = 'error';
            } else if (type === 'info') {
                options.title = 'Information';
                options.message = 'This is an informational message.';
                options.icon = 'info';
                options.confirmButtonStyle = 'primary';
                options.confirmText = 'Got it';
            }

            WarningModal.show(options).then(result => {
                if (result) {
                    alert('You confirmed the action!');
                } else {
                    console.log('Action cancelled');
                }
            });
        }

        function showSubscriptionCancellationModal() {
            WarningModal.show({
                title: 'Cancel Subscription',
                message: 'Are you sure you want to cancel your subscription? You will still have access until the end of your current billing period.',
                confirmText: 'Yes, Cancel',
                cancelText: 'No, Keep Subscription',
                icon: 'warning',
                confirmButtonStyle: 'danger'
            }).then(result => {
                if (result) {
                    alert('Subscription cancelled successfully!');
                }
            });
        }

        function showAccountDeletionModal() {
            // Create a custom input field for DELETE confirmation
            const customInputField = document.createElement('div');
            customInputField.innerHTML = `
                <div style="margin-top: 16px; margin-bottom: 8px;">
                    <input
                        type="text"
                        id="delete-confirmation-input"
                        style="width: 100%; padding: 8px; border: 1px solid #D1D5DB; border-radius: 6px; font-size: 14px; text-align: center;"
                        placeholder="Type DELETE to confirm"
                    />
                </div>
            `;

            // Create and show the modal
            const warningModal = document.createElement('div');
            warningModal.className = 'warning-modal-overlay';
            warningModal.innerHTML = createDeleteConfirmationHTML({
                title: 'Delete Account',
                message: 'This action cannot be undone. It will permanently delete your account and remove all associated data.\n\nPlease type DELETE in the field below to confirm.',
                confirmText: 'Delete Account',
                cancelText: 'Cancel',
                icon: 'error',
                confirmButtonStyle: 'danger',
                customContent: customInputField.innerHTML
            });
            document.body.appendChild(warningModal);

            // Get references to elements
            const modalOverlay = warningModal;
            const modalContent = warningModal.querySelector('.warning-modal-content');
            const confirmInput = warningModal.querySelector('#delete-confirmation-input');
            const confirmButton = warningModal.querySelector('.warning-modal-confirm-button');
            const cancelButton = warningModal.querySelector('.warning-modal-cancel-button');
            const closeButton = warningModal.querySelector('.warning-modal-close');

            // Disable confirm button initially
            confirmButton.disabled = true;
            confirmButton.classList.add('disabled');

            // Show modal with animation
            setTimeout(() => {
                modalOverlay.style.opacity = '1';
                modalContent.style.opacity = '1';
                modalContent.style.transform = 'scale(1)';
            }, 10);

            // Add event listeners
            confirmInput.addEventListener('input', (e) => {
                const isValid = e.target.value === 'DELETE';
                confirmButton.disabled = !isValid;
                if (isValid) {
                    confirmButton.classList.remove('disabled');
                } else {
                    confirmButton.classList.add('disabled');
                }
            });

            // Close modal function
            const closeModal = () => {
                modalOverlay.style.opacity = '0';
                modalContent.style.opacity = '0';
                modalContent.style.transform = 'scale(0.95)';
                setTimeout(() => modalOverlay.remove(), 300);
            };

            // Add event listeners for closing
            cancelButton.addEventListener('click', closeModal);
            closeButton.addEventListener('click', closeModal);
            modalOverlay.addEventListener('click', (event) => {
                if (event.target === modalOverlay) {
                    closeModal();
                }
            });

            // Handle delete confirmation
            confirmButton.addEventListener('click', () => {
                closeModal();
                alert('Account deleted successfully!');
            });
        }

        // Helper function to create delete confirmation HTML
        function createDeleteConfirmationHTML(options) {
            const {
                title = 'Warning',
                message = 'Are you sure you want to proceed?',
                confirmText = 'Confirm',
                cancelText = 'Cancel',
                icon = 'warning',
                confirmButtonStyle = 'danger',
                customContent = ''
            } = options;

            // Determine icon SVG
            let iconSvg = '';
            
            if (icon === 'warning') {
                iconSvg = `
                    <svg xmlns="http://www.w3.org/2000/svg" class="warning-modal-icon warning" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
                        <line x1="12" y1="9" x2="12" y2="13"></line>
                        <line x1="12" y1="17" x2="12.01" y2="17"></line>
                    </svg>
                `;
            } else if (icon === 'error') {
                iconSvg = `
                    <svg xmlns="http://www.w3.org/2000/svg" class="warning-modal-icon error" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="12" cy="12" r="10"></circle>
                        <line x1="15" y1="9" x2="9" y2="15"></line>
                        <line x1="9" y1="9" x2="15" y2="15"></line>
                    </svg>
                `;
            }

            // Determine confirm button class
            let confirmButtonClass = 'warning-modal-confirm-button';
            if (confirmButtonStyle === 'danger') {
                confirmButtonClass += ' danger';
            }

            return `
                <style>
                    .warning-modal-overlay {
                        position: fixed;
                        top: 0;
                        left: 0;
                        right: 0;
                        bottom: 0;
                        background-color: rgba(18, 28, 65, 0.3);
                        backdrop-filter: blur(3px);
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        z-index: 3000;
                        opacity: 0;
                        transition: opacity 0.3s ease;
                    }

                    .warning-modal-content {
                        background: #fff;
                        border-radius: 0.75rem;
                        width: 90%;
                        max-width: 450px;
                        box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
                        transform: scale(0.95);
                        opacity: 0;
                        transition: all 0.3s ease;
                        overflow: hidden;
                    }

                    .warning-modal-header {
                        display: flex;
                        align-items: center;
                        padding: 1.25rem 1.5rem 0.75rem;
                        position: relative;
                    }

                    .warning-modal-icon {
                        width: 2rem;
                        height: 2rem;
                        margin-right: 0.75rem;
                    }

                    .warning-modal-icon.warning {
                        color: #F59E0B;
                    }

                    .warning-modal-icon.error {
                        color: #EF4444;
                    }

                    .warning-modal-title {
                        font-size: 1.25rem;
                        font-weight: 600;
                        color: #111827;
                        margin: 0;
                        flex-grow: 1;
                    }

                    .warning-modal-close {
                        background: none;
                        border: none;
                        color: #6B7280;
                        cursor: pointer;
                        padding: 0.25rem;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        border-radius: 0.375rem;
                        transition: background-color 0.2s, color 0.2s;
                        position: absolute;
                        top: 1rem;
                        right: 1rem;
                    }

                    .warning-modal-close:hover {
                        background-color: #F3F4F6;
                        color: #374151;
                    }

                    .warning-modal-body {
                        padding: 0.75rem 1.5rem 1.25rem;
                    }

                    .warning-modal-message {
                        margin: 0;
                        color: #4B5563;
                        font-size: 1rem;
                        line-height: 1.5;
                        white-space: pre-line;
                    }

                    .warning-modal-footer {
                        display: flex;
                        justify-content: flex-end;
                        padding: 1rem 1.5rem 1.5rem;
                        gap: 0.75rem;
                    }

                    .warning-modal-cancel-button {
                        padding: 0.5rem 1rem;
                        background-color: #F3F4F6;
                        color: #374151;
                        border: none;
                        border-radius: 0.375rem;
                        font-weight: 500;
                        font-size: 0.875rem;
                        cursor: pointer;
                        transition: background-color 0.2s;
                    }

                    .warning-modal-cancel-button:hover {
                        background-color: #E5E7EB;
                    }

                    .warning-modal-confirm-button {
                        padding: 0.5rem 1rem;
                        border: none;
                        border-radius: 0.375rem;
                        font-weight: 500;
                        font-size: 0.875rem;
                        cursor: pointer;
                        transition: background-color 0.2s;
                    }

                    .warning-modal-confirm-button.danger {
                        background-color: #EF4444;
                        color: white;
                    }

                    .warning-modal-confirm-button.danger:hover {
                        background-color: #DC2626;
                    }

                    .warning-modal-confirm-button.disabled {
                        opacity: 0.5;
                        cursor: not-allowed;
                    }

                    @media (max-width: 640px) {
                        .warning-modal-content {
                            width: 95%;
                            max-width: none;
                        }
                        
                        .warning-modal-footer {
                            flex-direction: column-reverse;
                        }
                        
                        .warning-modal-cancel-button,
                        .warning-modal-confirm-button {
                            width: 100%;
                            padding: 0.75rem 1rem;
                        }
                    }
                </style>
                <div class="warning-modal-content">
                    <div class="warning-modal-header">
                        ${iconSvg}
                        <h2 class="warning-modal-title">${title}</h2>
                        <button class="warning-modal-close">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </button>
                    </div>
                    <div class="warning-modal-body">
                        <p class="warning-modal-message">${message}</p>
                        ${customContent}
                    </div>
                    <div class="warning-modal-footer">
                        <button class="warning-modal-cancel-button">${cancelText}</button>
                        <button class="${confirmButtonClass}">${confirmText}</button>
                    </div>
                </div>
            `;
        }
    </script>
</body>
</html>
