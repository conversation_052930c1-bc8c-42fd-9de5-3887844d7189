<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Extend Free Trial Feature</title>
    <link rel="stylesheet" href="super-admin-dashboard.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f9fafb;
            padding: 2rem;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 0.75rem;
            padding: 2rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .test-section {
            margin-bottom: 2rem;
            padding: 1rem;
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
        }
        .test-button {
            background-color: #1547bb;
            color: white;
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 0.375rem;
            cursor: pointer;
            margin: 0.5rem;
        }
        .test-button:hover {
            background-color: #1e40af;
        }
        .mock-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }
        .mock-table th,
        .mock-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }
        .mock-table th {
            background-color: #f9fafb;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Test Extend Free Trial Feature</h1>
        <p>This page tests the new "Extend Free Trial" functionality for the Super Admin Dashboard.</p>

        <div class="test-section">
            <h2>Mock Admin Accounts Table</h2>
            <p>Click the clock icon to test the extend trial modal:</p>
            
            <table class="mock-table">
                <thead>
                    <tr>
                        <th>Admin</th>
                        <th>Company</th>
                        <th>Credits</th>
                        <th>Subscription</th>
                        <th>Trial Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <tr class="admin-row" data-admin-id="test-admin-1">
                        <td>
                            <div class="admin-info">
                                <div class="admin-name">John Doe</div>
                                <div class="admin-email"><EMAIL></div>
                                <div class="admin-id">test-admin-1</div>
                            </div>
                        </td>
                        <td>
                            <div class="company-name">Test Company</div>
                        </td>
                        <td>
                            <div class="credits-info">
                                <span class="credits-count">5</span>
                                <span class="credits-label">credits</span>
                            </div>
                        </td>
                        <td><span class="subscription-badge trial">Free Trial</span></td>
                        <td><span class="status-badge trial">7 days left</span></td>
                        <td>
                            <div class="action-buttons">
                                <button class="action-btn view-btn" onclick="alert('View details clicked')">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 616 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                    </svg>
                                </button>
                                <button class="action-btn extend-trial-btn" onclick="showExtendTrialModal('test-admin-1', 'John Doe')" title="Extend Free Trial">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <tr class="admin-row" data-admin-id="test-admin-2">
                        <td>
                            <div class="admin-info">
                                <div class="admin-name">Jane Smith</div>
                                <div class="admin-email"><EMAIL></div>
                                <div class="admin-id">test-admin-2</div>
                            </div>
                        </td>
                        <td>
                            <div class="company-name">Another Company</div>
                        </td>
                        <td>
                            <div class="credits-info">
                                <span class="credits-count">0</span>
                                <span class="credits-label">credits</span>
                            </div>
                        </td>
                        <td><span class="subscription-badge expired">Free Trial</span></td>
                        <td><span class="status-badge expired">Trial Expired</span></td>
                        <td>
                            <div class="action-buttons">
                                <button class="action-btn view-btn" onclick="alert('View details clicked')">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 616 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                    </svg>
                                </button>
                                <button class="action-btn extend-trial-btn" onclick="showExtendTrialModal('test-admin-2', 'Jane Smith')" title="Extend Free Trial">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </button>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="test-section">
            <h2>Test Controls</h2>
            <button class="test-button" onclick="testExtendTrialModal()">Test Extend Trial Modal</button>
            <button class="test-button" onclick="testValidation()">Test Form Validation</button>
            <button class="test-button" onclick="testLoadingStates()">Test Loading States</button>
        </div>
    </div>

    <!-- Include Firebase and dashboard scripts -->
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-firestore-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-auth-compat.js"></script>
    
    <script>
        // Mock Firebase configuration for testing
        const firebaseConfig = {
            apiKey: "test-key",
            authDomain: "test.firebaseapp.com",
            projectId: "test-project",
            storageBucket: "test.appspot.com",
            messagingSenderId: "123456789",
            appId: "test-app-id"
        };

        // Initialize Firebase (mock)
        if (!firebase.apps.length) {
            firebase.initializeApp(firebaseConfig);
        }
        const db = firebase.firestore();

        // Mock dashboard data for testing
        const dashboardData = {
            admins: [
                {
                    id: 'test-admin-1',
                    email: '<EMAIL>',
                    firstname: 'John',
                    lastname: 'Doe',
                    company: 'Test Company',
                    subscriptionType: 'freeTrial',
                    subscriptionActive: true,
                    subscriptionEndDate: {
                        toDate: () => new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days from now
                    },
                    trialDaysRemaining: 7,
                    isTrialExpired: false
                },
                {
                    id: 'test-admin-2',
                    email: '<EMAIL>',
                    firstname: 'Jane',
                    lastname: 'Smith',
                    company: 'Another Company',
                    subscriptionType: 'freeTrial',
                    subscriptionActive: false,
                    subscriptionEndDate: {
                        toDate: () => new Date(Date.now() - 2 * 24 * 60 * 60 * 1000) // 2 days ago
                    },
                    trialDaysRemaining: 0,
                    isTrialExpired: true
                }
            ]
        };

        // Mock functions for testing
        function showNotification(message, type) {
            alert(`${type.toUpperCase()}: ${message}`);
        }

        function logActivity(action, data) {
            console.log('Activity logged:', action, data);
        }

        function getTrialStatus(admin) {
            if (admin.isTrialExpired) {
                return '<span class="status-badge expired">Trial Expired</span>';
            }
            return `<span class="status-badge trial">${admin.trialDaysRemaining} days left</span>`;
        }

        function getSubscriptionBadge(admin) {
            return '<span class="subscription-badge trial">Free Trial</span>';
        }

        // Test functions
        function testExtendTrialModal() {
            showExtendTrialModal('test-admin-1', 'John Doe');
        }

        function testValidation() {
            // This will be tested through the modal
            alert('Open the extend trial modal and try entering invalid values (negative numbers, text, etc.)');
        }

        function testLoadingStates() {
            alert('Open the extend trial modal and click "Extend Trial" to see the loading state');
        }

        // Mock the close modal function
        function closeModal(button) {
            const modal = button.closest('.modal-overlay');
            if (modal) {
                modal.style.opacity = '0';
                modal.querySelector('.modal-content').style.transform = 'scale(0.95)';
                setTimeout(() => {
                    document.body.removeChild(modal);
                }, 300);
            }
        }
    </script>

    <!-- Include the dashboard script with our extend trial functionality -->
    <script src="super-admin-dashboard.js"></script>
</body>
</html>
