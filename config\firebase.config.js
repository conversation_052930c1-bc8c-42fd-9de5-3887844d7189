const admin = require('firebase-admin');
require('dotenv').config();

const initializeFirebase = () => {
  try {
    const serviceAccount = require('../serviceAccountKey.json');
    
    admin.initializeApp({
      credential: admin.credential.cert(serviceAccount),
      databaseURL: process.env.FIREBASE_DATABASE_URL
    });
    
    console.log('Firebase initialized successfully');
    return admin;
  } catch (error) {
    console.error('Error initializing Firebase:', error);
    throw error;
  }
};

module.exports = initializeFirebase;
