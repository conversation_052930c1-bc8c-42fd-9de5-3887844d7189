<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Login Tracking</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .result { margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 3px; font-family: monospace; white-space: pre-wrap; max-height: 400px; overflow-y: auto; }
        button { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        input { padding: 8px; margin: 5px; width: 300px; }
        .success { background-color: #d4edda; }
        .error { background-color: #f8d7da; }
    </style>
</head>
<body>
    <h2>Test Login Tracking Data Storage</h2>
    
    <div class="section">
        <h3>Check Admin Document Data</h3>
        <input type="email" id="admin-email" placeholder="Enter admin email" value="<EMAIL>">
        <button onclick="checkAdminData()">Check Admin Data</button>
        <div id="admin-result" class="result"></div>
    </div>

    <div class="section">
        <h3>Test Manual Tracking</h3>
        <button onclick="testManualTracking()">Test Manual Tracking</button>
        <div id="manual-result" class="result"></div>
    </div>

    <!-- Firebase Scripts -->
    <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-firestore.js"></script>

    <script>
        // Firebase config
        const firebaseConfig = {
            apiKey: "AIzaSyDMUQCWXAprahuhrogEYFDEiMxwKALPXxc",
            authDomain: "barefoot-elearning-app.firebaseapp.com",
            projectId: "barefoot-elearning-app",
            databaseURL: "https://barefoot-elearning-app-default-rtdb.firebaseio.com/",
            storageBucket: "barefoot-elearning-app.appspot.com",
            messagingSenderId: "170819735788",
            appId: "1:170819735788:web:223af318437eb5d947d5c9"
        };

        firebase.initializeApp(firebaseConfig);
        const db = firebase.firestore();

        function generateSessionId() {
            return 'test_session_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
        }

        async function checkAdminData() {
            const email = document.getElementById('admin-email').value;
            const resultDiv = document.getElementById('admin-result');
            
            if (!email) {
                resultDiv.textContent = 'Please enter an email address';
                return;
            }

            resultDiv.textContent = 'Checking admin data...';

            try {
                const adminRef = db.collection('Admins').doc(email);
                const adminDoc = await adminRef.get();

                if (adminDoc.exists) {
                    const data = adminDoc.data();
                    const trackingData = {
                        email: email,
                        lastLoginTime: data.lastLoginTime ? (data.lastLoginTime.toDate ? data.lastLoginTime.toDate().toISOString() : data.lastLoginTime) : 'None',
                        lastLoginTimestamp: data.lastLoginTimestamp ? (data.lastLoginTimestamp.toDate ? data.lastLoginTimestamp.toDate().toISOString() : data.lastLoginTimestamp) : 'None',
                        totalLogins: data.totalLogins || 0,
                        loginSessionsCount: data.loginSessions ? data.loginSessions.length : 0,
                        loginHistoryCount: data.loginHistory ? data.loginHistory.length : 0,
                        recentSessions: data.loginSessions ? data.loginSessions.slice(0, 3).map(session => ({
                            loginTime: session.loginTime ? (session.loginTime.toDate ? session.loginTime.toDate().toISOString() : session.loginTime) : 'None',
                            sessionId: session.sessionId,
                            userAgent: session.userAgent ? session.userAgent.substring(0, 50) + '...' : 'None',
                            loginMethod: session.loginMethod
                        })) : [],
                        recentHistory: data.loginHistory ? data.loginHistory.slice(0, 3).map(session => ({
                            loginTime: session.loginTime ? (session.loginTime.toDate ? session.loginTime.toDate().toISOString() : session.loginTime) : 'None',
                            sessionId: session.sessionId,
                            userAgent: session.userAgent ? session.userAgent.substring(0, 50) + '...' : 'None',
                            loginMethod: session.loginMethod
                        })) : []
                    };
                    resultDiv.textContent = JSON.stringify(trackingData, null, 2);
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.textContent = `Admin document not found for: ${email}`;
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.textContent = `Error: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        async function testManualTracking() {
            const email = document.getElementById('admin-email').value;
            const resultDiv = document.getElementById('manual-result');
            
            if (!email) {
                resultDiv.textContent = 'Please enter an email address first';
                return;
            }

            resultDiv.textContent = 'Testing manual tracking...';

            try {
                const sessionId = generateSessionId();
                const loginTimestamp = firebase.firestore.FieldValue.serverTimestamp();
                const currentTime = new Date(); // Use regular Date for arrays

                // Create session data for tracking (use regular Date for arrays)
                const sessionData = {
                    loginTime: currentTime,
                    timestamp: currentTime,
                    userAgent: navigator.userAgent,
                    sessionId: sessionId,
                    loginMethod: 'manual_test',
                    loginPage: 'test-tracking.html'
                };

                // Update admin document with all login tracking data
                const adminRef = db.collection('Admins').doc(email);
                await db.runTransaction(async (transaction) => {
                    const adminDoc = await transaction.get(adminRef);

                    if (adminDoc.exists) {
                        const adminData = adminDoc.data();
                        const updates = {
                            lastLoginTime: loginTimestamp,
                            lastLoginTimestamp: loginTimestamp,
                            lastModified: loginTimestamp
                        };

                        // Add to login sessions array
                        let loginSessions = adminData.loginSessions || [];
                        loginSessions.unshift(sessionData);

                        if (loginSessions.length > 100) {
                            loginSessions = loginSessions.slice(0, 100);
                        }

                        updates.loginSessions = loginSessions;
                        updates.totalLogins = (adminData.totalLogins || 0) + 1;
                        
                        // Also store login history
                        let loginHistory = adminData.loginHistory || [];
                        loginHistory.unshift(sessionData);
                        
                        if (loginHistory.length > 100) {
                            loginHistory = loginHistory.slice(0, 100);
                        }
                        
                        updates.loginHistory = loginHistory;
                        
                        transaction.update(adminRef, updates);
                        
                        resultDiv.textContent = `✅ Manual tracking test successful!\nSession ID: ${sessionId}\nTotal logins will be: ${updates.totalLogins}\nLogin sessions count: ${loginSessions.length}\nLogin history count: ${loginHistory.length}`;
                        resultDiv.className = 'result success';
                    } else {
                        resultDiv.textContent = `❌ Admin document not found for: ${email}`;
                        resultDiv.className = 'result error';
                    }
                });

            } catch (error) {
                resultDiv.textContent = `❌ Manual tracking test failed: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }
    </script>
</body>
</html>
