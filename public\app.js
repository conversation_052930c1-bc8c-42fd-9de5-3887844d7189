function showLoadingOverlay() {
    if (window.LoadingOverlay) {
        window.LoadingOverlay.show();
    } else {
        const loadingOverlay = document.getElementById('loading-overlay');
        if (loadingOverlay) loadingOverlay.style.display = 'flex';
    }
}

function hideLoadingOverlay() {
    if (window.LoadingOverlay) {
        window.LoadingOverlay.hide();
    } else {
        const loadingOverlay = document.getElementById('loading-overlay');
        if (loadingOverlay) loadingOverlay.style.display = 'none';
    }
}

// When loading the assessments page
function loadAssessmentsPage(targetElement) {
    showLoadingOverlay();
    
    fetch('assessments.html')
        .then(response => response.text())
        .then(html => {
            targetElement.innerHTML = html;
            loadScript('assessments.js')
                .then(() => {
                    if (typeof initializeAssessments === 'function') {
                        initializeAssessments(userCompany);
                    }
                })
                .catch(error => console.error('Error loading assessments script:', error))
                .finally(() => {
                    hideLoadingOverlay();
                });
        })
        .catch(error => {
            console.error('Error loading assessments page:', error);
            hideLoadingOverlay();
        });
}

