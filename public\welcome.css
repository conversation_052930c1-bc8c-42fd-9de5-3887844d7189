w-body {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 65vh;
    margin: 0;
}

.welcome-container {
    background-color: rgba(255, 255, 255, 0.137);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    padding: 40px;
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    text-align: center;
    max-width: 600px;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

@media (max-width: 768px) {
    .welcome-container {
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
    }
}



@media (max-width: 768px) {
    .icon-placeholder {
        width: px;
        height: 160px;
    }
}

.icon-placeholder img {
    width: 100px;
    
}

h1 {
    color: #1a73e8;
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 10px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

@media (max-width: 768px) {
    h1 {
        font-size: 24px;
    }
}

p {
    color: #333333;
    font-size: 13px;
    margin-bottom: 20px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.message {
    color: #666666;
    font-size: 13px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    margin-top: 30px;
}