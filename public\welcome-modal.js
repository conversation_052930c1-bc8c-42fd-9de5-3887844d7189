/**
 * Welcome Modal Component
 * A comprehensive onboarding modal for new users
 */

(function(global) {
  'use strict';

  let isModalInitialized = false;
  let isClosing = false;
  let modalOverlay = null;
  let modalContent = null;
  let currentPromiseResolve = null;

  /**
   * Creates the welcome modal HTML structure
   * @param {Object} options - Configuration options
   * @returns {string} HTML string for the modal
   */
  function createWelcomeModalHTML(options) {
    const {
      userName = 'there',
      companyName = 'your organization'
    } = options;

    return `
      <div class="welcome-modal-overlay">
        <div class="welcome-modal-content">
          <div class="welcome-modal-header">
            <div class="welcome-icon">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h1 class="welcome-title">Welcome to Skills Assess!</h1>
            <p class="welcome-subtitle">Let's get ${companyName} set up for success</p>
          </div>
          
          <div class="welcome-modal-body">
            <div class="welcome-step">
              <div class="step-icon">
                <span class="step-number">1</span>
              </div>
              <div class="step-content">
                <h3>What is Skills Assess?</h3>
                <p>Skills Assess is a comprehensive platform that helps organizations identify skill gaps and create personalized learning paths for their employees. We make workforce development simple and effective.</p>
              </div>
            </div>

            <div class="welcome-step">
              <div class="step-icon">
                <span class="step-number">2</span>
              </div>
              <div class="step-content">
                <h3>Why invite your employees?</h3>
                <p>To create meaningful insights, we need your team members to complete skills assessments. These assessments help us understand current capabilities and identify areas for improvement across your organization.</p>
              </div>
            </div>

            <div class="welcome-step">
              <div class="step-icon">
                <span class="step-number">3</span>
              </div>
              <div class="step-content">
                <h3>What happens next?</h3>
                <p>Once your employees complete their assessments, you'll unlock powerful dashboard insights, detailed reports, and personalized learning recommendations to drive your organization forward.</p>
              </div>
            </div>

            <div class="welcome-trial-info">
              <div class="trial-badge">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M16.5 18.75h-9m9 0a3 3 0 013 3h-15a3 3 0 013-3m9 0v-3.375c0-.621-.503-1.125-1.125-1.125h-.871M7.5 18.75v-3.375c0-.621.504-1.125 1.125-1.125h.871m0 0L9 15.75m0 0l.621-2.757M9 15.75l.621-2.757m0 0a3 3 0 012.758 0M12 6.75a3 3 0 00-3 3v2.25a3 3 0 003 3V9a3 3 0 00-3-3z" />
                </svg>
                <span>Free 14-Day Trial</span>
              </div>
              <p>You have <strong>5 free assessment credits</strong> to get started. No payment required during your trial period!</p>
            </div>
          </div>

          <div class="welcome-modal-footer">
            <button class="welcome-secondary-button" onclick="window.WelcomeModal.startTutorial()">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M4.26 10.147a60.436 60.436 0 00-.491 6.347A48.627 48.627 0 0112 20.904a48.627 48.627 0 018.232-4.41 60.46 60.46 0 00-.491-6.347m-15.482 0a50.57 50.57 0 00-2.658-.813A59.905 59.905 0 0112 3.493a59.902 59.902 0 0110.399 5.84c-.896.248-1.783.52-2.658.814m-15.482 0A50.697 50.697 0 0112 13.489a50.702 50.702 0 017.74-3.342M6.75 15a.75.75 0 100-1.5.75.75 0 000 1.5zm0 0v-3.675A55.378 55.378 0 0112 8.443a55.381 55.381 0 015.25 2.882V15" />
              </svg>
              Take Tutorial
            </button>
            <button class="welcome-primary-button" onclick="window.WelcomeModal.getStarted()">
              Get Started
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M13.5 4.5L21 12m0 0l-7.5 7.5M21 12H3" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * Injects the required CSS styles
   */
  function injectCSS() {
    if (document.getElementById('welcome-modal-styles')) return;

    const style = document.createElement('style');
    style.id = 'welcome-modal-styles';
    style.textContent = `
      .welcome-modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(18, 28, 65, 0.4);
        backdrop-filter: blur(4px);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 3000;
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      .welcome-modal-content {
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        border-radius: 1rem;
        width: 90%;
        max-width: 600px;
        max-height: 90vh;
        overflow-y: auto;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        transform: scale(0.95);
        opacity: 0;
        transition: all 0.3s ease;
      }

      .welcome-modal-header {
        text-align: center;
        padding: 2rem 2rem 1rem;
        border-bottom: 1px solid #e2e8f0;
      }

      .welcome-icon {
        width: 4rem;
        height: 4rem;
        margin: 0 auto 1rem;
        background: linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
      }

      .welcome-icon svg {
        width: 2rem;
        height: 2rem;
      }

      .welcome-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: #1e293b;
        margin: 0 0 0.5rem;
      }

      .welcome-subtitle {
        font-size: 1.125rem;
        color: #64748b;
        margin: 0;
      }

      .welcome-modal-body {
        padding: 2rem;
      }

      .welcome-step {
        display: flex;
        align-items: flex-start;
        margin-bottom: 2rem;
      }

      .welcome-step:last-of-type {
        margin-bottom: 2.5rem;
      }

      .step-icon {
        flex-shrink: 0;
        margin-right: 1rem;
      }

      .step-number {
        width: 2.5rem;
        height: 2.5rem;
        background: linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%);
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 1rem;
      }

      .step-content h3 {
        font-size: 1.25rem;
        font-weight: 600;
        color: #1e293b;
        margin: 0 0 0.5rem;
      }

      .step-content p {
        color: #475569;
        line-height: 1.6;
        margin: 0;
      }

      .welcome-trial-info {
        background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
        border: 1px solid #0ea5e9;
        border-radius: 0.75rem;
        padding: 1.5rem;
        text-align: center;
      }

      .trial-badge {
        display: inline-flex;
        align-items: center;
        background: #0ea5e9;
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 2rem;
        font-weight: 600;
        font-size: 0.875rem;
        margin-bottom: 1rem;
      }

      .trial-badge svg {
        width: 1.25rem;
        height: 1.25rem;
        margin-right: 0.5rem;
      }

      .welcome-trial-info p {
        color: #0c4a6e;
        margin: 0;
        font-size: 1rem;
      }

      .welcome-modal-footer {
        display: flex;
        justify-content: center;
        gap: 1rem;
        padding: 1.5rem 2rem 2rem;
        border-top: 1px solid #e2e8f0;
      }

      .welcome-secondary-button,
      .welcome-primary-button {
        display: inline-flex;
        align-items: center;
        padding: 0.75rem 1.5rem;
        border-radius: 0.5rem;
        font-weight: 600;
        font-size: 1rem;
        cursor: pointer;
        transition: all 0.2s;
        border: none;
        gap: 0.5rem;
      }

      .welcome-secondary-button {
        background-color: #f1f5f9;
        color: #475569;
        border: 1px solid #cbd5e1;
      }

      .welcome-secondary-button:hover {
        background-color: #e2e8f0;
        color: #334155;
      }

      .welcome-primary-button {
        background: linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%);
        color: white;
      }

      .welcome-primary-button:hover {
        background: linear-gradient(135deg, #2563EB 0%, #1E40AF 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
      }

      .welcome-secondary-button svg,
      .welcome-primary-button svg {
        width: 1.25rem;
        height: 1.25rem;
      }

      @media (max-width: 640px) {
        .welcome-modal-content {
          width: 95%;
          margin: 1rem;
        }
        
        .welcome-modal-header,
        .welcome-modal-body,
        .welcome-modal-footer {
          padding-left: 1.5rem;
          padding-right: 1.5rem;
        }
        
        .welcome-modal-footer {
          flex-direction: column;
        }
        
        .welcome-secondary-button,
        .welcome-primary-button {
          width: 100%;
          justify-content: center;
        }

        .welcome-title {
          font-size: 1.5rem;
        }

        .welcome-step {
          margin-bottom: 1.5rem;
        }
      }
    `;
    document.head.appendChild(style);
  }

  /**
   * Shows the welcome modal
   * @param {Object} options - Configuration options
   * @returns {Promise<string>} - Resolves to 'tutorial' or 'getStarted'
   */
  function showWelcomeModal(options = {}) {
    console.log('WelcomeModal.show() called with options:', options);
    isClosing = false;

    // Remove existing modal if it exists
    const existingModal = document.querySelector('.welcome-modal-overlay');
    if (existingModal) {
      console.log('Removing existing welcome modal');
      existingModal.parentNode.removeChild(existingModal);
      isModalInitialized = false;
    }

    // Create modal
    console.log('Creating welcome modal HTML and injecting CSS');
    injectCSS();
    const modalHTML = createWelcomeModalHTML(options);
    const modalContainer = document.createElement('div');
    modalContainer.innerHTML = modalHTML;

    // Add to body
    console.log('Adding welcome modal to document body');
    document.body.appendChild(modalContainer.firstElementChild);

    modalOverlay = document.querySelector('.welcome-modal-overlay');
    modalContent = document.querySelector('.welcome-modal-content');

    if (!modalOverlay || !modalContent) {
      console.error('Failed to find modal elements after adding to DOM');
      return Promise.reject(new Error('Modal elements not found'));
    }

    console.log('Welcome modal elements found, initializing');
    isModalInitialized = true;

    // Show modal with animation
    setTimeout(() => {
      if (isClosing) return;

      console.log('Showing welcome modal with animation');
      modalOverlay.style.opacity = '1';
      modalContent.style.opacity = '1';
      modalContent.style.transform = 'scale(1)';
    }, 10);

    return new Promise(resolve => {
      console.log('Welcome modal promise created, waiting for user interaction');
      currentPromiseResolve = resolve;
    });
  }

  /**
   * Hides the welcome modal
   * @param {string} result - The result of the modal ('tutorial' or 'getStarted')
   */
  function hideWelcomeModal(result) {
    isClosing = true;

    // Animate closing
    if (modalOverlay) {
      modalOverlay.style.opacity = '0';
      modalContent.style.opacity = '0';
      modalContent.style.transform = 'scale(0.95)';

      // Remove after animation completes
      setTimeout(() => {
        if (modalOverlay && modalOverlay.parentNode) {
          modalOverlay.parentNode.removeChild(modalOverlay);
        }
        isModalInitialized = false;

        // Resolve promise if it exists
        if (currentPromiseResolve) {
          currentPromiseResolve(result);
          currentPromiseResolve = null;
        }
      }, 300);
    }
  }

  /**
   * Handle tutorial button click
   */
  function startTutorial() {
    console.log('Welcome modal: Tutorial button clicked');
    hideWelcomeModal('tutorial');
  }

  /**
   * Handle get started button click
   */
  function getStarted() {
    console.log('Welcome modal: Get Started button clicked');
    hideWelcomeModal('getStarted');
  }

  // Public API
  global.WelcomeModal = {
    show: showWelcomeModal,
    hide: hideWelcomeModal,
    startTutorial: startTutorial,
    getStarted: getStarted
  };

  console.log('WelcomeModal loaded and available');
})(typeof window !== 'undefined' ? window : global);
