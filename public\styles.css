/* General Styles */
body {
    background-image: url('BG.png');
    background-size: cover;
    font-family: 'Montserrat', sans-serif;
    background-position: center;
    background-repeat: no-repeat;
    background-attachment: fixed;
    background-color: rgba(255, 255, 255, 0.8);
}

/* Media query for smaller devices - keeping the same background image for all devices */
@media (max-width: 768px) {
    /* Background image is now the same as desktop */
}

#main-content {
    transition: opacity 0.6s ease-in-out;
}


/* Header */
header {
    background-color: rgba(255, 255, 255, 0.95);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

header .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 4rem;
    padding: 0 1rem;
    z-index: 1;
}

/* Navigation */
nav {
    display: flex;
    align-items: center;
}

.nav-link {
    font-size: 0.75rem;
    font-weight: 500;
    color: #4a5568;
    transition: color 0.2s ease-in-out, background-color 0.2s ease-in-out;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    display: flex;
    align-items: center;
    margin-right: 1rem;
}

.nav-link:hover, .nav-link.active {
    color: #2c4da5f3;
    ;
}

.nav-link img {
    width: 18px;
    height: 18px;
    margin-right: 0.5rem;
}

/* Desktop Navigation */
@media (min-width: 768px) {
    header nav {
        display: flex;
        gap: 1rem;
        margin-left: 2rem;
    }

    header nav .nav-link {
        position: relative;
    }

    header nav .nav-link::after {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 0;
        width: 100%;
        height: 2px;
        background-color: #3b82f6;
        transform: scaleX(0);
        transition: transform 0.2s ease-in-out;
    }

    header nav .nav-link:hover::after, header nav .nav-link.active::after {
        transform: scaleX(1);
    }
}

/* Mobile Navigation Drawer */
#navigation-drawer {
    background-color: #f7fafcb2;
    color: #2d3748;
    font-family: 'Montserrat', sans-serif;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    width: 18rem;
}

#navigation-drawer nav {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

#navigation-drawer .nav-link {
    padding: 1rem 2rem;
    border-radius: 0.5rem;
    transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out, transform 0.2s ease-in-out;
}

#navigation-drawer .nav-link:hover, #navigation-drawer .nav-link.active {
    background-color: #4796f01c;
    color: #2c5282;
    transform: translateX(5px);
}

/* Navigation Drawer Logo */
#navigation-drawer .logo {
    height: 2rem; /* Reduced from 2.5rem */
    width: auto;
    margin-top: 1.5rem; /* Reduced from 2rem */
}

/* Right-side items */
.flex.items-center > button,
.flex.items-center > div {
    margin-left: 1rem;
}

/* Button */
button svg {
    width: 1.25rem;
    height: 1.25rem;
    color: #a0aec0;
}

button svg.dark {
    color: #a0aec0;
}

/* Notification button */
.rounded-full {
    transition: background-color 0.2s ease-in-out;
}

.rounded-full:hover {
    background-color: rgba(59, 130, 246, 0.1);
}

/* Modern User Menu Styles */
#user-menu {
    background-color: #ffffff;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08);
    width: 240px;
    overflow: hidden;
    transition: all 0.3s ease;
}

#user-menu .px-4.py-3 {
    background-color: #f7fafc;
    padding: 1.25rem;
}

#user-menu .flex.items-center {
    gap: 1rem;
}

#user-menu img.w-10.h-10 {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    object-fit: cover;
    border: none;
    box-shadow: none;
}

#user-menu p.text-sm.font-semibold {
    font-size: 1rem;
    color: #2d3748;
    margin-bottom: 0.25rem;
}

#user-menu p.text-xs {
    font-size: 0.875rem;
    color: #718096;
}

#user-menu .mt-2.flex.items-center {
    margin-top: 0.75rem;
}

#user-menu .inline-block.w-2.h-2 {
    width: 0.5rem;
    height: 0.5rem;
}

#user-menu .text-xs.text-gray-500 {
    font-size: 0.75rem;
    color: #718096;
}

#user-menu a.block.px-4.py-2 {
    padding: 0.75rem 1.25rem;
    color: #4a5568;
    transition: background-color 0.2s ease;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
}

#user-menu a.block.px-4.py-2:hover {
    background-color: #edf2f7;
    color: #2d3748;
}

#user-menu a.block.px-4.py-2:last-child {
    border-top: 1px solid #edf2f7;
    color: #e53e3e;
}

/* New styles for logout icon */
#user-menu a.block.px-4.py-2 img {
    width: 16px;
    height: 16px;
    margin-right: 0.5rem;
}

/* Dark mode styles */
.dark #user-menu {
    background-color: #2d3748;
}

.dark #user-menu .px-4.py-3 {
    background-color: #2c3e50;
}

.dark #user-menu p.text-sm.font-semibold {
    color: #f7fafc;
}

.dark #user-menu p.text-xs,
.dark #user-menu .text-xs.text-gray-500 {
    color: #a0aec0;
}

.dark #user-menu a.block.px-4.py-2 {
    color: #e2e8f0;
}

.dark #user-menu a.block.px-4.py-2:hover {
    background-color: #4a5568;
    color: #f7fafc;
}

.dark #user-menu a.block.px-4.py-2:last-child {
    border-top: 1px solid #4a5568;
    color: #fc8181;
}

/* Card Styles */
.card {
    background-color: rgba(255, 255, 255, 0.925); /* Adjust the opacity as needed */
    backdrop-filter: blur(10px); /* Adjust the blur value as needed */
    -webkit-backdrop-filter: blur(10px); /* For Safari support */
    padding: 1.5rem;
    border-radius: 0.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    z-index: 10;
}
.card.dark {
    background-color: #2d3748; /* dark:bg-gray-800 */
}

.enroll-button {
    position: absolute;
    bottom: 0rem;
    right: 0rem;
    background-color: #1547bb; /* Changed from #FF7F50 to #1547bb */
    color: white;
    border: none;
    border-radius: 20px;
    padding: 0.5rem 1rem;
    font-size: 0.55rem;
    font-weight: 600;
    text-transform: uppercase;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(21, 71, 187, 0.3); /* Updated shadow color */
}

.enroll-button:hover {
    background-color: #0f3699; /* Darker shade for hover */
    transform: translateY(-2px);
    box-shadow: 0 3px 10px rgba(21, 71, 187, 0.3); /* Updated shadow color */
}

.enroll-button:active {
    transform: translateY(1px);
    box-shadow: 0 2px 5px rgba(21, 71, 187, 0.3); /* Updated shadow color */
}

.enroll-button::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background-color: #1547bb; /* Updated glow color */
    z-index: -1;
    filter: blur(4px);
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 25px;
}

.enroll-button:hover::after {
    opacity: 0.3; /* Reduced glow effect */
}
.card h2 {
    color: #1e3a8a; /* Secondary color for headings */
    font-size: 1.2rem;
    font-weight: 600;
}

/* Table Styles */
table {
    width: 100%;
    background-color: rgb(255, 255, 255);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}
table.dark {
    background-color: #2d3748; /* dark:bg-gray-800 */
}
table th,
table td {
    padding: 0.5rem 1rem; /* px-4 py-2 */
    text-align: left;
    font-size: 0.875rem; /* text-sm */
    font-weight: 600; /* font-semibold */
}
table th.dark {
    color: #e2e8f0; /* dark:text-gray-200 */
}

table td.dark {
    color: #e2e8f0; /* dark:text-gray-200 */
}
table td {
    color: #4a5568; /* text-gray-700 */
}

/* Navigation Drawer */
/* Navigation Drawer */
#navigation-drawer {
    background-color: #f7fafcb2; /* bg-gray-100 */
    color: #2d3748; /* text-gray-800 */
    font-family: 'Montserrat', sans-serif;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

#navigation-drawer nav a {
    color: #2d3748;
    font-weight: 600;
    padding: 1rem 2rem; /* Increase padding */
    border-radius: 0.5rem;
    transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out;
    display: flex; /* Add flex display */
    align-items: center; /* Align items vertically */
    gap: 0.75rem; /* Add gap between icon and text */
}

#navigation-drawer nav a:hover {
    background-color: #4796f01c;
    color: #2c5282;
    transform: translateX(5px); /* Add a subtle translation effect */
    transition: all 0.3s ease-in-out; /* Add transition for smooth animation */
}

#navigation-drawer nav a.active {
    background-color: #3182ce; /* bg-blue-600 */
    color: #f7fafc; /* text-gray-100 */
}

#navigation-drawer {
    width: 18rem; /* w-64 */
}

.logo {
    height: 2.5rem; /* Adjust as necessary */
}

#navigation-drawer .logo {
    height: 2rem; /* Reduced from 2.5rem */
    width: auto;
    margin-top: 1.5rem; /* Reduced from 2rem */
}

.absolute {
    position: absolute;
    right: 0;
    mt: 0.5rem;
    w-48;
    bg-white dark:bg-gray-800;
    rounded-md;
    shadow-lg;
    py-1;
    z-index: 100; /* Add this line */
}

.hidden {
    display: none;
}

.empty {
    background-color: #ffffff4d; /* Light gray background */
    color:#4a5568;/* Dark gray text color */
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    border-radius: 8px; /* Add rounded corners */
    padding: 1rem; /* Add padding for better spacing */
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); /* Add subtle shadow for depth */
}

.empty img {
    max-width: 90px;
    max-height: 30px;
    margin-bottom: 1rem; /* Spacing between image and text */
}

.empty p {
    font-size: 0.775rem; /* text-sm */
    color: #4a5568; /* text-gray-500 */
}

.empty h2 {
    font-size: 1rem; /* text-xl */
    font-weight: 600; /* font-semibold */
    color: #2d3748c0; /* text-gray-800 */
    margin-bottom: 12px;
}

.empty .text-4xl,
.empty span {
    display: none; /* Hide the employee count and percentage */
}

.card:not(.empty) .text-4xl {
    display: flex;
    align-items: baseline;
    gap: 0.5rem;
}

.card:not(.empty) .text-4xl::after {
    content: "Employee(s)";
    font-size: 0.875rem; /* text-sm */
    font-weight: 500; /* font-medium */
    color: #4a5568; /* text-gray-500 */
}

.card:not(.empty) .text-4xl::after.dark {
    color: #a0aec0; /* dark:text-gray-400 */
}

.card.empty .text-4xl::after {
    display: none;
}

/* Card Hover Effect */
.grid .card:not(.empty) {
    position: relative;
    overflow: hidden;
    transition: transform 0.3s ease-in-out, box-shadow 0.3s ease-in-out;
}

.grid .card:not(.empty):before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(59, 130, 246, 0.1), rgba(59, 131, 246, 0.192));
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
    z-index: 1;
}

.grid .card:not(.empty):hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06);
}

.grid .card:not(.empty):hover:before {
    opacity: 1;
}

.grid .card-content {
    position: relative;
    z-index: 2;
}

/* User Menu styles are now in user-menu.css */

/* Media Queries */
@media (max-width: 768px) {
    .grid {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }

    .card {
        padding: 1rem;
    }

    table {
        font-size: 0.75rem;
    }
}

@media (max-width: 640px) {
    .grid {
        grid-template-columns: repeat(1, minmax(0, 1fr));
    }

    table {
        font-size: 0.65rem;
    }
}

/* Center the main content */
main {
    display: flex;
    flex-direction: column;
    align-items: center;
}

#main-content {
    max-width: 150%;
    width: 100%;
}

/* Loading Overlay Styles */
#loading-overlay {
    display: none;
    position: fixed;
    z-index: 9999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(5px);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 1rem;
}

.loading-text {
    color: #fff;
    margin-top: 0.75rem;
    font-size: 0.65rem; /* Reduced size for more minimalistic look */
    letter-spacing: 1px;
    opacity: 0.9;
    font-weight: 400;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(0);
        opacity: 1;
    }
    50% {
        transform: scale(1);
        opacity: 0.5;
    }
}

@keyframes fadeInOut {
    0%, 100% { opacity: 0; }
    50% { opacity: 1; }
}


.skeleton-row {
    animation: skeleton-loading 1s linear infinite alternate;
}

.skeleton-text {
    width: 100%;
    height: 16px;
    background-color: #e0e0e0;
    animation: skeleton-loading 1s linear infinite alternate;
}

@keyframes skeleton-loading {
    0% {
        background-color: #e0e0e08f;
    }
    100% {
        background-color: #f5f5f577;
    }
}


/* Existing styles */
#search-input {
    width: 300px;
    transition: width 0.3s ease-in-out;
}

#search-input:focus {
    width: 400px;
}

/* Updated responsive styles */
@media (max-width: 768px) {
    .flex.flex-col.sm\:flex-row.items-start.sm\:items-center.space-y-4.sm\:space-y-0.sm\:space-x-4.w-full.sm\:w-auto {
        flex-direction: column;
        align-items: stretch;
    }

    .relative.w-full.sm\:w-auto {
        width: 100%;
        margin-bottom: 10px;
    }

    #search-input,
    #search-input:focus,
    .relative.w-full.sm\:w-auto select {
        width: 100%;
        max-width: none;
        padding: 10px 12px;
    }

    /* Adjust filter and rows per page dropdowns */
    .flex.space-x-2.w-full.sm\:w-auto {
        flex-direction: row;
        justify-content: space-between;
        margin-top: 10px;
    }

    .flex.space-x-2.w-full.sm\:w-auto .relative.inline-block.flex-grow.sm\:flex-grow-0,
    .flex.space-x-2.w-full.sm\:w-auto .relative.w-full.sm\:w-auto {
        width: 48%; /* Adjust as needed */
    }
}

@media (max-width: 640px) {
    .flex.items-center > button,
    .flex.items-center > div {
        margin-left: 0.5rem;
    }

    /* Further adjustments for very small screens */
    .flex.space-x-2.w-full.sm\:w-auto {
        flex-direction: column;
    }

    .flex.space-x-2.w-full.sm\:w-auto .relative.inline-block.flex-grow.sm\:flex-grow-0,
    .flex.space-x-2.w-full.sm\:w-auto .relative.w-full.sm\:w-auto {
        width: 100%;
        margin-bottom: 10px;
    }
}
/* Filter and Sort Buttons */
#filter-button,
#sort-button {
    transition: transform 0.2s ease-in-out;
}

#filter-button:hover,
#sort-button:hover {
    transform: scale(1.05);
}

#filter-button:focus,
#sort-button:focus {
    transform: scale(1.05);
}

table {
    background-color: #ffffffcb;
    border-collapse: separate;
    border-spacing: 0;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    overflow: hidden;
}

table th {
    background-color: #f7fafc;
    font-weight: 600;
    text-transform: uppercase;
    border-bottom: 1px solid #e2e8f0;
}

table td {
    border-bottom: 1px solid #e2e8f0;
}

table tr:last-child td {
    border-bottom: none;
}

table tr:hover {
    background-color: #f7fafc;
}

/* Login Success Overlay */
#login-success-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
}

#login-success-overlay.visible {
    opacity: 1;
}

.login-success-content {
    text-align: center;
}

#lottie-container {
    width: 45px;
    height: 45px;
    margin: 0 auto;
}

.login-success-content p {
    margin-top: 20px;
    font-family: 'Montserrat', sans-serif;
    font-size: 15px;
    font-weight: 500;
    color: #333;
}

/* For dark mode */
@media (prefers-color-scheme: dark) {
    #login-success-overlay {
        background-color: rgba(17, 24, 39, 0.9);
    }

    .login-success-content p {
        color: #e5e7eb;
    }
}

@media (max-width: 768px) {
    nav {
        /* Add specific styles for mobile nav if needed */
        display: flex;
        flex-direction: column;
    }

    /* Hide reports/metrics link on mobile */
    .nav-link[data-content="reports"],
    header nav a[data-content="reports"],
    #navigation-drawer nav a[data-content="reports"] {
        display: none !important;
    }

    /* ... other mobile styles ... */
}

/* Notification Window Styles */
.notification-window {
    position: fixed;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08);
    padding: 16px;
    z-index: 1000;
    transition: opacity 0.2s ease-in-out, transform 0.2s ease-in-out;
    opacity: 1;
    transform: translateY(0);
}

.notification-window.hidden {
    opacity: 0;
    transform: translateY(-5px); /* Reduced from -10px to -5px for a subtler effect */
    pointer-events: none;
}

.notification-content {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
}

.notification-icon {
    width: 25px;
    height: 25px;
    margin-bottom: 8px;
}

.notification-window p {
    margin: 0;
    font-size: 11px;
    font-weight: 500;
    color: #3182cea4;
}

#main-content {
    transition: opacity 0.3s ease-in-out;
}

#dashboard-content {
    transition: opacity 0.3s ease-in-out;
}

.score-header {
    display: flex;
    align-items: center;
}

.card-content {
    position: relative;
}

.info-icon {
    display: inline-block;
    width: 16px;
    height: 16px;
    cursor: pointer;
    position: relative;
    background-image: url('info.png');
    background-size: cover;
}

.info-icon::after {
    content: attr(data-tooltip);
    visibility: hidden;
    position: absolute;
    z-index: 1000;
    top: 125%;
    left: 50%;
    transform: translateX(-50%);
    background-color: #5c72f1de;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 5px 10px;
    font-size: 10px;
    line-height: 1.2;
    opacity: 0;
    transition: opacity 0.3s, visibility 0.3s;
    width: 300px;
    max-width: 400px;
    white-space: normal;
    text-transform: none; /* Override uppercase text transform */
    font-weight: normal; /* Reset font weight if needed */
}

.info-icon::before {
    content: "";
    visibility: hidden;
    position: absolute;
    z-index: 1000;
    bottom: -5px;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-bottom-color: #5c72f1de;
    opacity: 0;
    transition: opacity 0.3s, visibility 0.3s;
}

.info-icon:hover::after,
.info-icon:hover::before {
    visibility: visible;
    opacity: 1;
}

.overflow-x-auto {
    overflow-x: auto;
    position: relative;
}

th {
    position: relative;
}
.tooltip {
    visibility: hidden;
    position: absolute;
    z-index: 1000;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: #5c72f1de;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 5px 10px;
    opacity: 0;
    transition: opacity 0.3s;
    white-space: nowrap;
    font-size: 12px;
    line-height: 1.4;
    z-index: 999;
}

.tooltip::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: #333 transparent transparent transparent;
}

td:hover .tooltip {
    visibility: visible;
    opacity: 1;
    z-index: 999;
}


.button-container {
    display: flex;
    justify-content: center;
    margin-bottom: 20px; /* Added px unit */
}

.all-assessments-btn {
    display: flex;
    align-items: center;
    background-color: #3b82f6;
    color: white;
    padding: 10px 20px;
    border-radius: 25px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: background-color 0.3s, transform 0.3s;
    font-size: 0.7rem;
    font-weight: 500;
}

.all-assessments-btn:hover {
    background-color: #2c5282;
    transform: translateY(-2px);
}

.all-assessments-btn svg {
    margin-left: 5px;
    width: 16px;
    height: 16px;
}

.floating-button {
    position: fixed;
    bottom: 5px;
    right: 20px;
    z-index: 1000;
    opacity: 0;
    transform: scale(0.9);
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.floating-button.visible {
    opacity: 1;
    transform: scale(1);
    animation: smoothGrow 3s ease-in-out infinite alternate;
}

/* New smooth grow animation */
@keyframes smoothGrow {
    0% {
        transform: scale(1);
    }
    100% {
        transform: scale(1.05);
    }
}

/* Hover effect to pause the animation */
.floating-button:hover {
    animation-play-state: paused;
}

.feedback-overlay {
    position: fixed;
    inset: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.feedback-content {
    background-color: white;
    padding: 2rem;
    border-radius: 0.5rem;
    max-width: 28rem;
    width: 100%;
}

.toast {
    position: fixed;
    bottom: 1rem;
    right: 1rem;
    background-color: #48bb78;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.disabled-nav-link {
    opacity: 0.5;
    cursor: not-allowed !important;
    pointer-events: none;
}

.feature-locked-badge {
    font-size: 10px;
    background-color: #EF4444;
    color: white;
    padding: 2px 6px;
    border-radius: 9999px;
    margin-left: 8px;
    pointer-events: none;
}

.animate-fade-in {
    animation: fadeIn 0.3s ease-in;
}

.animate-fade-out {
    animation: fadeOut 0.3s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeOut {
    from { opacity: 1; transform: translateY(0); }
    to { opacity: 0; transform: translateY(20px); }
}

.notification {
    position: fixed;
    top: 70px;
    left: 50%;
    transform: translateX(-50%);
    padding: 12px 24px;
    border-radius: 6px;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 9999;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    font-weight: 500;
}

.notification.success {
    background-color: #48bb78;
    color: white;
}

.notification.error {
    background-color: #FEE2E2;
    color: #991B1B;
    border: 1px solid #FCA5A5;
}

.notification.warning {
    background-color: #FEF3C7;
    color: #92400E;
    border: 1px solid #FCD34D;
}

/* Modal Overlay */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
}

/* Modal Content */
.modal-content {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 10px 15px rgba(0,0,0,0.1);
    max-width: 800px;
    width: 100%;
    margin: 0 20px;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.95);
    opacity: 0;
    transition: all 0.3s ease-in-out;
}

/* Modal Header */
.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.modal-title {
    font-size: 24px;
    font-weight: 600;
    color: #1E3A8A; /* Blue color */
}

.close-modal-button {
    background: none;
    border: none;
    color: #6B7280;
    cursor: pointer;
    transition: color 0.2s;
}

.close-modal-button:hover {
    color: #374151;
}

/* Packages Grid */
.packages-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

@media(min-width: 768px) {
    .packages-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media(min-width: 1024px) {
    .packages-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* Credit Package */
.credit-package {
    border: 2px solid #E5E7EB;
    border-radius: 8px;
    padding: 15px;
    position: relative;
    transition: all 0.3s ease-in-out;
}

.credit-package:hover {
    box-shadow: 0 10px 15px rgba(0,0,0,0.1);
}

.credit-package.selected {
    border-color: #1E3A8A; /* Blue color */
    background: #EFF6FF;
}

.credit-package .package-title {
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 5px;
}

.credit-package .package-highlight {
    color: #1E3A8A; /* Blue color */
    font-weight: 500;
    margin-bottom: 5px;
    font-size: 14px;
}

.credit-package .package-description {
    color: #4B5563;
    font-size: 12px;
    margin-bottom: 10px;
}

.credit-package .package-price {
    margin-bottom: 10px;
}

.credit-package .package-price span {
    display: inline-block;
}

.credit-package .package-price .total-price {
    font-size: 20px;
    font-weight: 600;
}

.credit-package .package-price .price-per-credit {
    color: #6B7280;
    font-size: 12px;
    margin-left: 5px;
}

/* Select Button */
.select-package-btn {
    width: 100%;
    padding: 10px 15px;
    font-size: 16px;
    border-radius: 6px;
    background-color: #FFFFFF;
    color: #1F2937;
    border: 1px solid #93C5FD;
    cursor: pointer;
    transition: background-color 0.2s ease-in-out, border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.select-package-btn:hover {
    background-color: #F3F4F6;
}

.select-package-btn.selected {
    background-color: #2563EB;
    color: #FFFFFF;
    border-color: #2563EB;
}

.select-package-btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.4); /* Blue focus ring */
}


/* Popular Badge */
.popular-badge {
    position: absolute;
    top: -10px;
    right: 15px;
    background:  #FFA500;
    color: #fff;
    padding: 3px 6px;
    border-radius: 9999px;
    font-size: 12px;
    font-weight: 500;
}

/* PayPal Button Wrapper */
#paypal-button-wrapper {
    display: none;
    margin-top: 20px;
    width: 100%;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    padding: 0 20px;
}

/* Payment Notification */
.payment-notification {
    position: fixed;
    top: 80px;
    left: 50%;
    transform: translateX(-50%);
    padding: 15px 20px;
    border-radius: 8px;
    z-index: 1001;
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
}

.payment-notification.show {
    opacity: 1;
}

.payment-notification.success {
    background: #10B981;
    color: #fff;
}

.payment-notification.error {
    background: #EF4444;
    color: #fff;
}

.payment-notification.warning {
    background: #F59E0B;
    color: #fff;
}

.payment-notification.info {
    background: #3B82F6;
    color: #fff;
}

/* Utility Classes */
.hidden {
    display: none;
}

.text-center {
    text-align: center;
}

.mt-4 {
    margin-top: 20px;
}

.mb-2 {
    margin-bottom: 10px;
}

.py-6 {
    padding-top: 30px;
    padding-bottom: 30px;
}

.btn-retry {
    padding: 10px 20px;
    background: #1E3A8A; /* Blue color */
    color: #fff;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.btn-retry:hover {
    background: #1D4ED8;
}



@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.table-row-hover {
    transition: background-color 0.2s ease;
}

.table-row-hover:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.dark .table-row-hover:hover {
    background-color: rgba(255, 255, 255, 0.05);
}
.skills-gap-button:hover {
    background-color: rgba(59, 130, 246, 0.1);
}


.chartjs-matrix-cell {
    transition: all 0.2s ease;
    cursor: pointer;
}

.chartjs-matrix-cell:hover {
    filter: brightness(1.1);
    transform: scale(1.05);
}

/* Add these new styles for the chart modal */
}
#chartModal {
    opacity: 0;
    transition: opacity 0.3s ease-out;
}

#chartModal .modal-content {
    transform: scale(0.95);
    opacity: 0;
    transition: transform 0.3s ease-out, opacity 0.3s ease-out;
}

#chartModal.show {
    opacity: 1;
}

#chartModal.show .modal-content {
    transform: scale(1);
    opacity: 1;
}

/* Chart container styles */
.chart-container {
    position: relative;
    transition: transform 0.3s ease;
    cursor: pointer;
}

.chart-container:hover {
    transform: translateY(-2px);
}

/* Expanded chart styles */
#expandedChart {
    max-height: 80vh !important;
}



#skillGapChart {
    min-height: 300px;
}

/* Add these styles after your existing chart container styles */
.chart-container:not(#roleDistributionChart-container) {
    cursor: pointer;
    transition: transform 0.3s ease;
}

.chart-container:not(#roleDistributionChart-container):hover {
    transform: translateY(-2px);
}

#roleDistributionChart-container {
    cursor: default;
}

.skills-progress {
    background: #1547bb; /* Changed from #f59e0b to #1547bb */
    border-radius: 9999px;
    height: 100%;
    transition: width 0.3s ease;
}

/* Add Button Styles */
#addButton {
    background-color: #1547bb;
    color: white;
    transition: all 0.3s ease;
}

#addButton:hover {
    background-color: #0f3699;
    transform: translateY(-2px);
    box-shadow: 0 3px 10px rgba(21, 71, 187, 0.3);
}

#addButton:active {
    transform: translateY(1px);
    box-shadow: 0 2px 5px rgba(21, 71, 187, 0.3);
}

/* Send Invitations Button Styles */
#sendInvitationsButton {
    background-color: #1547bb;
    color: white;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(21, 71, 187, 0.3);
}

#sendInvitationsButton:hover {
    background-color: #0f3699;
    transform: translateY(-2px);
    box-shadow: 0 3px 10px rgba(21, 71, 187, 0.3);
}

#sendInvitationsButton:active {
    transform: translateY(1px);
    box-shadow: 0 2px 5px rgba(21, 71, 187, 0.3);
}

#sendInvitationsButton:disabled {
    background-color: rgba(21, 71, 187, 0.5);
    transform: none;
    box-shadow: none;
    cursor: not-allowed;
}