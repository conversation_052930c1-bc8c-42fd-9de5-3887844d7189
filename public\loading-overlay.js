/**
 * Enhanced Loading Overlay Utility
 * Provides consistent methods for showing and hiding the loading overlay with context-specific messages
 */

// Default loading messages for different contexts
const DEFAULT_MESSAGES = {
    default: 'Loading...',
    dashboard: 'Gathering insights...',
    assessments: 'Loading assessments...',
    reports: 'Generating reports...',
    account: 'Loading account details...',
    subscription: 'Processing subscription...',
    delete: 'Deleting account...',
    save: 'Saving changes...',
    login: 'Signing in...',
    signup: 'Creating account...',
    invite: 'Sending invitation...',
    export: 'Preparing export...',
    import: 'Processing data...',
    upload: 'Uploading file...'
};

// Create loading overlay if it doesn't exist
function ensureLoadingOverlayExists() {
    let loadingOverlay = document.getElementById('loading-overlay');

    if (!loadingOverlay) {
        loadingOverlay = document.createElement('div');
        loadingOverlay.id = 'loading-overlay';
        loadingOverlay.className = 'fixed inset-0 bg-black bg-opacity-50 flex flex-col items-center justify-center z-50';
        loadingOverlay.style.display = 'none';

        // Create animation container
        const animationContainer = document.createElement('div');
        animationContainer.id = 'loading-animation';
        animationContainer.style.width = '45px';
        animationContainer.style.height = '45px';

        // Create text element
        const textElement = document.createElement('div');
        textElement.className = 'loading-text';

        loadingOverlay.appendChild(animationContainer);
        loadingOverlay.appendChild(textElement);
        document.body.appendChild(loadingOverlay);

        // Initialize animation if lottie is available
        if (window.lottie) {
            window.loadingAnimation = lottie.loadAnimation({
                container: animationContainer,
                renderer: 'svg',
                loop: true,
                autoplay: false,
                path: 'assess_loading.json',
                initialSegment: [0, 60]
            });

            window.loadingAnimation.setSpeed(0.5);
        }
    }

    return loadingOverlay;
}

/**
 * Show loading overlay with context-specific message
 * @param {string} context - The context for the loading message (e.g., 'dashboard', 'account', etc.)
 * @param {string} customMessage - Optional custom message to override the default
 */
function showLoadingOverlay(context = 'default', customMessage = null) {
    const overlay = ensureLoadingOverlayExists();
    const textElement = overlay.querySelector('.loading-text');

    // Set the appropriate message
    const message = customMessage || DEFAULT_MESSAGES[context] || DEFAULT_MESSAGES.default;
    if (textElement) {
        textElement.textContent = message;
    }

    // Show the overlay
    overlay.style.opacity = '0';
    overlay.style.display = 'flex';

    // Start animation if available
    if (window.loadingAnimation) {
        window.loadingAnimation.play();
    }

    // Force browser reflow
    void overlay.offsetWidth;

    // Fade in
    overlay.style.opacity = '1';
}

/**
 * Hide loading overlay
 */
function hideLoadingOverlay() {
    const overlay = document.getElementById('loading-overlay');
    if (overlay) {
        overlay.style.opacity = '0';

        // Stop animation if available
        if (window.loadingAnimation) {
            window.loadingAnimation.stop();
        }

        setTimeout(() => {
            overlay.style.display = 'none';
        }, 300); // Match transition duration
    }
}

/**
 * Initialize the loading overlay with CSS transitions and styles
 */
function initializeLoadingOverlay() {
    const style = document.createElement('style');
    style.textContent = `
        #loading-overlay {
            transition: opacity 0.3s ease;
        }

        .loading-text {
            color: #fff;
            margin-top: 0.75rem;
            font-size: 0.65rem; /* Reduced size for more minimalistic look */
            letter-spacing: 1px;
            opacity: 0.9;
            font-weight: 400;
        }
    `;
    document.head.appendChild(style);

    ensureLoadingOverlayExists();
}

// Add to window to make available globally
window.LoadingOverlay = {
    show: showLoadingOverlay,
    hide: hideLoadingOverlay,
    initialize: initializeLoadingOverlay
};

// Initialize automatically when script loads
document.addEventListener('DOMContentLoaded', initializeLoadingOverlay);
