<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Skills Gap Modal Test</title>
    <style>
        body {
            font-family: system-ui, -apple-system, sans-serif;
            margin: 0;
            padding: 2rem;
            background: #f3f4f6;
        }
        
        .test-button {
            background: #1e3a8a;
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 0.5rem;
            font-size: 1rem;
            cursor: pointer;
            transition: background-color 0.2s;
            margin-bottom: 1rem;
        }
        
        .test-button:hover {
            background: #1e40af;
        }

        .test-container {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }
    </style>
</head>
<body>
    <h1>Skills Gap Modal Test</h1>
    <div class="test-container">
        <button class="test-button" onclick="showTestModal('complete')">
            Test with Complete Data
        </button>
        <button class="test-button" onclick="showTestModal('strengths-only')">
            Test with Strengths Only
        </button>
        <button class="test-button" onclick="showTestModal('gaps-only')">
            Test with Gaps Only
        </button>
        <button class="test-button" onclick="showTestModal('no-data')">
            Test with No Data
        </button>
    </div>

    <script>
        // Mock loading overlay functions
        window.showLoadingOverlay = function() {
            console.log('Loading overlay shown');
        };
        
        window.hideLoadingOverlay = function() {
            console.log('Loading overlay hidden');
        };

        // Test data for different scenarios
        function getTestData(scenario) {
            // Base test data structure
            const baseData = {
                digitalSkills: {
                    report: {
                        employeeName: "Test User",
                        role: "Developer",
                        summary: "This is a test summary for the skills gap analysis report.",
                        competencyAnalysis: {
                            "JavaScript": {
                                proficiencyLevel: "75%",
                                strengthAreas: [],
                                gapAreas: []
                            },
                            "HTML/CSS": {
                                proficiencyLevel: "85%",
                                strengthAreas: [],
                                gapAreas: []
                            },
                            "React": {
                                proficiencyLevel: "60%",
                                strengthAreas: [],
                                gapAreas: []
                            },
                            "Node.js": {
                                proficiencyLevel: "50%",
                                strengthAreas: [],
                                gapAreas: []
                            }
                        }
                    },
                    recommendations: [
                        {
                            course: "Advanced JavaScript",
                            reason: "To improve your JavaScript skills further."
                        },
                        {
                            course: "React Fundamentals",
                            reason: "To strengthen your React knowledge."
                        }
                    ]
                }
            };

            // Modify the data based on the scenario
            switch(scenario) {
                case 'complete':
                    baseData.digitalSkills.report.competencyAnalysis["JavaScript"].strengthAreas = ["ES6 Features", "Promises"];
                    baseData.digitalSkills.report.competencyAnalysis["JavaScript"].gapAreas = ["Async/Await", "TypeScript"];
                    
                    baseData.digitalSkills.report.competencyAnalysis["HTML/CSS"].strengthAreas = ["Responsive Design", "Flexbox"];
                    baseData.digitalSkills.report.competencyAnalysis["HTML/CSS"].gapAreas = ["CSS Grid", "Animations"];
                    
                    baseData.digitalSkills.report.competencyAnalysis["React"].strengthAreas = ["Component Design"];
                    baseData.digitalSkills.report.competencyAnalysis["React"].gapAreas = ["Hooks", "Context API"];
                    
                    baseData.digitalSkills.report.competencyAnalysis["Node.js"].strengthAreas = ["Express"];
                    baseData.digitalSkills.report.competencyAnalysis["Node.js"].gapAreas = ["Authentication", "Database Integration"];
                    break;
                
                case 'strengths-only':
                    baseData.digitalSkills.report.competencyAnalysis["JavaScript"].strengthAreas = ["ES6 Features", "Promises"];
                    baseData.digitalSkills.report.competencyAnalysis["HTML/CSS"].strengthAreas = ["Responsive Design", "Flexbox"];
                    baseData.digitalSkills.report.competencyAnalysis["React"].strengthAreas = ["Component Design"];
                    baseData.digitalSkills.report.competencyAnalysis["Node.js"].strengthAreas = ["Express"];
                    break;
                
                case 'gaps-only':
                    baseData.digitalSkills.report.competencyAnalysis["JavaScript"].gapAreas = ["Async/Await", "TypeScript"];
                    baseData.digitalSkills.report.competencyAnalysis["HTML/CSS"].gapAreas = ["CSS Grid", "Animations"];
                    baseData.digitalSkills.report.competencyAnalysis["React"].gapAreas = ["Hooks", "Context API"];
                    baseData.digitalSkills.report.competencyAnalysis["Node.js"].gapAreas = ["Authentication", "Database Integration"];
                    break;
                
                case 'no-data':
                    // No changes needed, the base data already has empty arrays
                    break;
            }

            return baseData;
        }

        // Function to show the test modal
        function showTestModal(scenario) {
            const testData = getTestData(scenario);
            if (typeof showSkillsGapAnalysis === 'function') {
                showSkillsGapAnalysis(testData);
            } else {
                alert('Skills gap modal script not loaded properly');
            }
        }
    </script>

    <!-- Include Chart.js for the radar chart -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.7.0/chart.min.js"></script>
    
    <!-- Include jsPDF for the export functionality -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>

    <!-- Include the skills gap modal script -->
    <script src="skills-gap-modal.js"></script>
</body>
</html>
