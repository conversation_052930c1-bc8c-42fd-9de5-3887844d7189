// Initialize loading animation
const loadingAnimation = lottie.loadAnimation({
    container: document.getElementById('loading-animation'),
    renderer: 'svg',
    loop: true,
    autoplay: false,
    path: 'assess_loading.json'
});

const loadingOverlay = document.getElementById('loading-overlay');
const startTrialBtn = document.getElementById('startTrialBtn');

// Show loading overlay
function showLoadingOverlay() {
    loadingOverlay.style.display = 'flex';
    loadingAnimation.play();
}

// Hide loading overlay
function hideLoadingOverlay() {
    loadingOverlay.style.display = 'none';
    loadingAnimation.stop();
}

// No longer require user to come from the first landing page
document.addEventListener('DOMContentLoaded', () => {
    // Initialize page without redirection
    console.log('Demo video page loaded directly');
});

// Handle start trial button click
startTrialBtn.addEventListener('click', () => {
    showLoadingOverlay();

    // Get user data from session storage if it exists (user came from landing page)
    const userData = sessionStorage.getItem('demoUser') ? JSON.parse(sessionStorage.getItem('demoUser')) : null;

    if (userData) {
        // Create URL with query parameters for the signup page
        const params = new URLSearchParams({
            firstname: userData.firstname,
            lastname: userData.lastname,
            email: userData.email,
            company: userData.company,
            role: userData.role,
            source: 'April_expo'
        });

        // Redirect to signup page with user data
        window.location.href = `signup.html?${params.toString()}`;
    } else {
        // If no user data (direct access to demo page), redirect to regular signup
        window.location.href = 'signup.html';
    }
});
