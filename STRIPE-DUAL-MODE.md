# Managing Stripe Test and Live Environments

This document explains how to manage both Stripe test and live environments in the Skills Assess application.

## Configuration

The application now supports both Stripe test and live environments simultaneously. The environment variables in your `.env` file have been updated to include both sets of keys:

```
# Test Environment
STRIPE_TEST_PUBLISHABLE_KEY=pk_test_...
STRIPE_TEST_SECRET_KEY=sk_test_...
STRIPE_TEST_WEBHOOK_SECRET=whsec_...

# Live Environment
STRIPE_LIVE_PUBLISHABLE_KEY=pk_live_...
STRIPE_LIVE_SECRET_KEY=sk_live_...
STRIPE_LIVE_WEBHOOK_SECRET=whsec_...

# Active keys (set by the switch-stripe-mode.js script)
STRIPE_PUBLISHABLE_KEY=${STRIPE_TEST_PUBLISHABLE_KEY}
STRIPE_SECRET_KEY=${STRIPE_TEST_SECRET_KEY}
STRIPE_WEBHOOK_SECRET=${STRIPE_TEST_WEBHOOK_SECRET}

# Set to 'test' or 'live' to determine which environment to use
STRIPE_MODE=test
```

## Switching Between Environments

You can switch between test and live environments using the provided utility script:

```bash
# Switch to test mode
node switch-stripe-mode.js test

# Switch to live mode
node switch-stripe-mode.js live
```

After switching modes, you need to restart your server for the changes to take effect.

## How It Works

1. The `STRIPE_MODE` environment variable determines which set of keys to use.
2. When the server starts, it initializes Stripe with either the test or live secret key based on the mode.
3. The appropriate publishable key is written to the `stripe-config.js` file, which is loaded by the client.
4. Webhooks use the appropriate webhook secret based on the mode.

## Important Considerations

### Price IDs

Stripe price IDs are different between test and live environments. The application now automatically selects the appropriate price IDs based on the current mode. The price IDs are defined in:

- `server.js` - For server-side functions like `getPlanNameFromPriceId`
- `public/stripe-price-config.js` - For client-side scripts

#### Test Mode Price IDs

```javascript
// Subscription plans
assess100: 'price_1RKCu7PqOZsaOO5km47XZ6AN',
assess250: 'price_1RKCuAPqOZsaOO5k6ygc4E9W',
assess500: 'price_1RKCuDPqOZsaOO5kam84LCD4',
freeTrial: 'price_1REmsePqOZsaOO5kgjo38qgp',

// Top-up packages
topup100: 'price_1RKCuHPqOZsaOO5kKFzMN8tW',
topup250: 'price_1RKCuKPqOZsaOO5ksiWIMAUZ',
topup500: 'price_1RKCuNPqOZsaOO5kqVmyPYyy'
```

#### Live Mode Price IDs

```javascript
// Subscription plans
assess100: 'price_1RMjI6L8F65CEkirK8t2V3DG',
assess250: 'price_1RMjI4L8F65CEkirnaFfwST4',
assess500: 'price_1RMjI2L8F65CEkirqOJxuD42',

// Top-up packages
topup100: 'price_1RMjI0L8F65CEkirmgGw2jDw',
topup250: 'price_1RMjHuL8F65CEkirXNMZ4UnJ',
topup500: 'price_1RMjHpL8F65CEkirTkzlPtEB'
```

### Webhooks

You need to set up webhooks for both environments:

1. Test environment: Create a webhook in the Stripe dashboard for your test environment and update `STRIPE_TEST_WEBHOOK_SECRET` in your `.env` file.
2. Live environment: Create a webhook in the Stripe dashboard for your live environment and update `STRIPE_LIVE_WEBHOOK_SECRET` in your `.env` file.

### Testing

Always test your payment flows in the test environment before switching to the live environment. Use Stripe's test cards for testing:

- Test successful payment: `4242 4242 4242 4242`
- Test failed payment: `4000 0000 0000 0002`

## Deployment Considerations

In production, you'll typically want to use the live environment. Make sure to set `STRIPE_MODE=live` in your production environment variables.

## Troubleshooting

If you encounter issues with payments:

1. Check the server logs to see which mode is being used: `Using Stripe in TEST/LIVE mode`
2. Verify that the correct webhook secret is being used
3. Check that the price IDs match the environment you're using
4. Ensure that the Stripe account has the necessary products and prices set up

For webhook testing locally, you can use the Stripe CLI:

```bash
stripe listen --forward-to localhost:3000/webhook
```

This will provide a webhook secret that you can use for local testing.
