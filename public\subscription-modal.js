(function(global) {
  // Stripe product definitions matching the pricing tiers
  const stripeProducts = {
    freeTrial: {
      id: 'free_trial',
      priceId: 'price_1REmsePqOZsaOO5kgjo38qgp',
      name: 'Free Trial',
      credits: 5,
      period: null,
      price: 0,
      billedAnnually: null,
      features: [
        'Includes 5 assessments',
        'Downloadable Learner reporting',
        'Graphical analysis reports*',
        'Recommended courses report',
        'Access to different assessment topics',
        'Full access for 14 days'
      ],
      cta: 'Start the trial'
    },
    assess1: {
      id: 'assess1',
      // Price ID will be set dynamically based on mode
      priceId: window.STRIPE_MODE === 'live'
        ? 'price_1RNo7DL8F65CEkir2BjRHcAh'  // Live mode price ID
        : 'price_1RNpW7PqOZsaOO5knOoIbBb0',  // Test mode price ID
      name: 'Assess1',
      credits: 1,
      period: 'year',
      price: 1,
      perPersonPrice: 1,
      annualPaymentInfo: 'Based on an annual payment of £1 in advance',
      features: [
        'Includes 1 assessment',
        '12 month access',
        'Downloadable Learner reporting',
        'Graphical analysis reports*',
        'Recommended courses report',
        'Access to different assessment topics',
        'For testing purposes only'
      ],
      cta: 'Sign up'
    },
    assess100: {
      id: 'assess100',
      // Price ID will be set dynamically based on mode
      priceId: window.STRIPE_MODE === 'live'
        ? 'price_1RMjI6L8F65CEkirK8t2V3DG'  // Live mode price ID
        : 'price_1RMP2WPqOZsaOO5kEXSQnBS0',  // Test mode price ID
      name: 'Assess100',
      credits: 100,
      period: 'year',
      price: 999,
      perPersonPrice: 9.99,
      annualPaymentInfo: 'Based on an annual payment of £999 in advance',
      popular: true,
      features: [
        'Includes 100 assessments',
        '12 month access',
        'Downloadable Learner reporting',
        'Graphical analysis reports*',
        'Recommended courses report',
        'Access to different assessment topics'
      ],
      cta: 'Sign up'
    },
    assess250: {
      id: 'assess250',
      // Price ID will be set dynamically based on mode
      priceId: window.STRIPE_MODE === 'live'
        ? 'price_1RMjI4L8F65CEkirnaFfwST4'  // Live mode price ID
        : 'price_1RMP2ZPqOZsaOO5kpAcmM2Ur',  // Test mode price ID
      name: 'Assess250',
      credits: 250,
      period: 'year',
      price: 1999,
      perPersonPrice: 7.99,
      annualPaymentInfo: 'Based on an annual payment of £1,999 in advance',
      features: [
        'Includes 250 assessments',
        '12 month access',
        'Downloadable Learner reporting',
        'Graphical analysis reports*',
        'Recommended courses report',
        'Access to different assessment topics'
      ],
      cta: 'Sign up'
    },
    assess500: {
      id: 'assess500',
      // Price ID will be set dynamically based on mode
      priceId: window.STRIPE_MODE === 'live'
        ? 'price_1RMjI2L8F65CEkirqOJxuD42'  // Live mode price ID
        : 'price_1RMP2cPqOZsaOO5kOzAGzqc7',  // Test mode price ID
      name: 'Assess500',
      credits: 500,
      period: 'year',
      price: 2999,
      perPersonPrice: 5.99,
      annualPaymentInfo: 'Based on an annual payment of £2,999 in advance',
      features: [
        'Includes 500 assessments',
        '12 month access',
        'Downloadable Learner reporting',
        'Graphical analysis reports*',
        'Recommended courses report',
        'Access to different assessment topics'
      ],
      cta: 'Sign up'
    }
  };

  // Make the stripe products available globally
  window.stripeProducts = stripeProducts;

  // State management
  let isModalInitialized = false;
  let currentPlan = null;
  let isClosing = false;
  let stripeInstance = null;
  let isYearlyBilling = true; // Always use yearly billing
  let isRequiredModal = false; // Flag to indicate if the modal is required (can't be dismissed)

  // DOM Elements
  let modalOverlay;
  let modalContent;

  async function loadStripeJs() {
    if (window.Stripe) {
      return window.Stripe;
    }

    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = 'https://js.stripe.com/v3/';
      script.onload = () => {
        if (window.Stripe) {
          resolve(window.Stripe);
        } else {
          reject(new Error('Stripe.js failed to load'));
        }
      };
      script.onerror = () => reject(new Error('Failed to load Stripe.js'));
      document.head.appendChild(script);
    });
  }

  async function initializeStripe() {
    if (stripeInstance) {
      return stripeInstance;
    }

    try {
      const Stripe = await loadStripeJs();
      stripeInstance = Stripe(window.STRIPE_PUBLISHABLE_KEY);
      return stripeInstance;
    } catch (error) {
      console.error('Failed to initialize Stripe:', error);
      throw error;
    }
  }

  // Check user's current subscription
  async function getUserSubscription() {
    const user = firebase.auth().currentUser;
    if (!user) return null;

    try {
      const doc = await firebase.firestore().collection('Admins').doc(user.email).get();
      if (!doc.exists) return null;

      const data = doc.data();

      // Determine which plan the user has based on subscriptionType
      const subscriptionType = data.subscriptionType;

      if (!data.paid || subscriptionType === 'freeTrial') {
        return stripeProducts.freeTrial;
      }

      // Map subscription type to plan
      if (subscriptionType === 'Assess1') return stripeProducts.assess1;
      if (subscriptionType === 'Assess100') return stripeProducts.assess100;
      if (subscriptionType === 'Assess250') return stripeProducts.assess250;
      if (subscriptionType === 'Assess500') return stripeProducts.assess500;

      // Fallback to mapping by credits if subscription type doesn't match
      if (data.credits === 1) return stripeProducts.assess1;
      if (data.credits === 100) return stripeProducts.assess100;
      if (data.credits === 250) return stripeProducts.assess250;
      if (data.credits === 500) return stripeProducts.assess500;

      return null;
    } catch (error) {
      console.error('Error getting user subscription:', error);
      return null;
    }
  }

  // Function to cancel a pending subscription change
  async function cancelPendingSubscriptionChange() {
    const user = firebase.auth().currentUser;
    if (!user) {
      showNotification('You must be logged in to cancel a pending change.', 'error');
      return;
    }

    try {
      // Get user data to check current subscription
      const doc = await firebase.firestore().collection('Admins').doc(user.email).get();
      if (!doc.exists) {
        showNotification('User data not found.', 'error');
        return;
      }

      const userData = doc.data();

      // Check if there's a pending change
      if (!userData.pendingSubscriptionChange) {
        showNotification('No pending subscription change found.', 'error');
        return;
      }

      // Confirm cancellation with the user
      const confirmed = await WarningModal.show({
        title: 'Cancel Pending Change',
        message: `Are you sure you want to cancel your pending ${userData.pendingSubscriptionChange.type} to ${userData.pendingSubscriptionChange.toPlan}?`,
        confirmText: 'Yes, Cancel Change',
        cancelText: 'No, Keep Change',
        icon: 'warning'
      });

      if (!confirmed) {
        return;
      }

      showNotification('Cancelling pending subscription change...', 'info');

      // Call the server endpoint to cancel the change
      const response = await fetch('/cancel-subscription-change', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: user.email,
          subscriptionId: userData.subscriptionId
        })
      });

      const result = await response.json();

      if (result.error) {
        throw new Error(result.error);
      }

      // Hide modal and show success message
      hideSubscriptionModal();
      showNotification('Pending subscription change cancelled successfully.', 'success');

      // Reload the page after a delay to refresh subscription status
      setTimeout(() => {
        window.location.reload();
      }, 3000);

    } catch (error) {
      console.error('Error cancelling pending subscription change:', error);
      showNotification('There was an error cancelling your pending subscription change. Please try again later.', 'error');
    }
  }

  // Create modal HTML
  function createModalHTML(isManagementMode = false) {
    const title = isManagementMode ? 'Manage Subscription' : 'Choose Your Plan';
    const subtitle = isManagementMode ?
      'Your current plan is highlighted below. Your existing credits will be preserved when changing plans.' :
      'Get started with our free 14-day no-commitment trial';

    // Determine which plans to show based on current plan, management mode, and demo mode
    let plansToShow = Object.values(stripeProducts);

    // Filter out Assess1 if not in demo mode
    if (!window.isDemoMode) {
      plansToShow = plansToShow.filter(plan => plan.id !== 'assess1');
    }

    // Check if there's a pending subscription change
    let pendingChangeHTML = '';
    if (isManagementMode && firebase.auth().currentUser) {
      // Get user data to check for pending changes
      firebase.firestore().collection('Admins').doc(firebase.auth().currentUser.email).get()
        .then(doc => {
          if (doc.exists) {
            const userData = doc.data();
            if (userData.pendingSubscriptionChange) {
              const pendingChange = userData.pendingSubscriptionChange;

              // Handle both Timestamp and ISO string date formats
              let effectiveDate;
              if (pendingChange.scheduledDate && pendingChange.scheduledDate.seconds) {
                // Handle Firestore Timestamp
                effectiveDate = new Date(pendingChange.scheduledDate.seconds * 1000).toLocaleDateString();
              } else if (pendingChange.scheduledDateIso) {
                // Handle ISO string date
                effectiveDate = new Date(pendingChange.scheduledDateIso).toLocaleDateString();
              } else {
                // Fallback
                effectiveDate = "next billing cycle";
              }

              // Update the pending change notice if it exists
              const pendingChangeNotice = document.getElementById('pending-subscription-change');
              if (pendingChangeNotice) {
                pendingChangeNotice.innerHTML = `
                  <div class="pending-change-info">
                    <p>You have a pending ${pendingChange.type} from ${pendingChange.fromPlan} to ${pendingChange.toPlan} scheduled for ${effectiveDate}.</p>
                  </div>
                  <button id="cancel-pending-change" class="cancel-pending-button">Cancel Change</button>
                `;

                // Add event listener to the cancel button
                const cancelButton = document.getElementById('cancel-pending-change');
                if (cancelButton) {
                  cancelButton.addEventListener('click', (e) => {
                    e.preventDefault();
                    cancelPendingSubscriptionChange();
                  });
                }
              }
            }
          }
        })
        .catch(error => {
          console.error('Error checking for pending changes:', error);
        });

      // Add a placeholder for the pending change notice
      pendingChangeHTML = `
        <div id="pending-subscription-change" class="pending-subscription-change hidden">
          <div class="pending-change-info">
            <p>Checking for pending changes...</p>
          </div>
        </div>
      `;
    }

    // Generate plan HTML based on management mode and current plan
    const plansHTML = plansToShow.map(plan => {
      // Determine button text based on management mode and relationship to current plan
      let buttonText = plan.cta;
      let buttonClass = 'plan-cta-button';
      let isDisabled = false;

      if (isManagementMode && currentPlan) {
        if (plan.id === currentPlan.id) {
          if (plan.id === 'free_trial') {
            buttonText = 'Active';
            buttonClass += ' disabled';
            isDisabled = true;
          } else {
            buttonText = 'Cancel Plan';
            buttonClass += ' cancel-button';
          }
        } else if (plan.id === 'free_trial') {
          buttonText = 'Not Available';
          buttonClass += ' disabled';
          isDisabled = true;
        } else {
          // Determine plan hierarchy for upgrade/downgrade
          const planHierarchy = {
            'free_trial': 0,
            'assess1': 1,
            'assess100': 2,
            'assess250': 3,
            'assess500': 4
          };

          const currentPlanRank = planHierarchy[currentPlan.id] || 0;
          const thisPlanRank = planHierarchy[plan.id] || 0;

          if (thisPlanRank > currentPlanRank) {
            buttonText = 'Upgrade';
            buttonClass += ' upgrade-button';
          } else if (thisPlanRank < currentPlanRank) {
            buttonText = 'Downgrade';
            buttonClass += ' downgrade-button';
          }
        }
      }

      // Determine if this is the current plan for badge display
      const isCurrentPlan = isManagementMode && currentPlan && plan.id === currentPlan.id;

      // Get price (always yearly)
      const displayPrice = plan.id === 'free_trial' ? 0 : plan.price;

      // Format the price to show whole numbers if no decimal part
      const formattedPrice = Number.isInteger(displayPrice) ? displayPrice : displayPrice.toFixed(2);

      return `
        <div class="subscription-plan ${plan.popular ? 'popular' : ''} ${isCurrentPlan ? 'current-plan' : ''}" data-plan-id="${plan.id}">
          ${(plan.popular && !isCurrentPlan) ? '<div class="popular-badge">Popular</div>' : ''}
          <div class="plan-header">
            <h3 class="plan-name">${plan.name}</h3>
            ${plan.perPersonPrice ?
              `<div class="plan-price">
                <span class="currency">£</span>
                <span class="amount">${plan.perPersonPrice}</span>
                <span class="period">per person*</span>
              </div>
              <div class="annual-payment-info">${plan.annualPaymentInfo}</div>`
              :
              `<div class="plan-price">
                <span class="currency">£</span>
                <span class="amount">${formattedPrice}</span>
                ${plan.period ? `<span class="period">per year</span>` : ''}
              </div>
              ${plan.period === 'year' ?
                `<div class="monthly-equivalent">(equivalent to £${Math.round(plan.price / 12)} per month)</div>` :
                ''}`
            }
          </div>
          <ul class="plan-features">
            ${plan.features.map(feature => `
              <li>
                <svg class="feature-check" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M13.3334 4L6.00008 11.3333L2.66675 8" stroke="#1547BB" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                ${feature}
              </li>
            `).join('')}
          </ul>
          <button class="${buttonClass}" ${isDisabled ? 'disabled' : ''}>${buttonText}</button>
        </div>
      `;
    }).join('');

    return `
      <div class="subscription-modal-overlay">
        <div class="subscription-modal-content">
          <div class="subscription-modal-header">
            <div class="subscription-modal-title">
              <h2>${title}</h2>
              <p class="subscription-modal-subtitle">${subtitle}</p>
            </div>
            <button class="subscription-modal-close">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </button>
          </div>

          ${pendingChangeHTML}

          <div class="subscription-plans-grid">
            ${plansHTML}
          </div>

          <div class="subscription-footer">
            <p class="subscription-note">*Per person pricing is based on the total number of assessments divided by the number of users. Includes charts and heat maps showcasing skills gaps at an organizational or department level.</p>
            <p class="subscription-contact">
              If you need more than 500 assessments or would like to explore personalising SkillsAssess, please
              <a href="#" class="contact-link">get in touch</a>.
            </p>
          </div>
        </div>
      </div>
    `;
  }

  // Initialize CSS
  function injectCSS() {
    if (document.getElementById('subscription-modal-styles')) return;

    const styleEl = document.createElement('style');
    styleEl.id = 'subscription-modal-styles';
    styleEl.textContent = `
      .subscription-modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(18, 28, 65, 0.8);
        z-index: 1000;
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      .subscription-modal-content {
        background-color: #fff;
        border-radius: 12px;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        width: 95%;
        max-width: 1200px;
        max-height: 90vh;
        overflow-y: auto;
        padding: 32px;
        opacity: 0;
        transform: scale(0.95);
        transition: transform 0.3s ease, opacity 0.3s ease;
      }

      .subscription-modal-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 32px;
      }

      .subscription-modal-title h2 {
        font-size: 24px; /* Reduced from 32px */
        font-weight: 600; /* Slightly reduced weight */
        color: #121C41; /* Dark blue as requested */
        margin: 0 0 8px 0;
      }

      .subscription-modal-subtitle {
        font-size: 16px;
        color: #6B7280;
        margin: 0;
      }

      .subscription-modal-close {
        background: none;
        border: none;
        color: #6B7280;
        cursor: pointer;
        padding: 4px;
        transition: color 0.2s;
      }

      .subscription-modal-close:hover {
        color: #121C41;
      }

      .subscription-plans-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 24px;
        margin-bottom: 32px;
      }

      .subscription-plan {
        border: 2px solid #E5E7EB;
        border-radius: 12px;
        padding: 24px;
        display: flex;
        flex-direction: column;
        position: relative;
        transition: transform 0.2s, box-shadow 0.2s, border-color 0.2s;
      }

      .subscription-plan:hover {
        transform: translateY(-4px);
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        border-color: #D1D5DB;
      }

      .subscription-plan.selected {
        border-color: #1547BB;
        background-color: #F0F7FF;
      }

      .subscription-plan.current-plan {
        border-color: #1547BB;
      }

      /* Fixed positioning for current plan badge */
      .subscription-plan.current-plan::before {
        content: 'Current Plan';
        position: absolute;
        top: -10px;
        left: 24px;
        background-color: #1547BB;
        color: white;
        font-size: 12px;
        font-weight: 600;
        padding: 4px 12px;
        border-radius: 20px;
        z-index: 10;
      }

      /* Moved popular badge to right side to avoid overlap */
      .popular-badge {
        position: absolute;
        top: -10px;
        right: 24px;
        background-color: #1547BB;
        color: white;
        font-size: 12px;
        font-weight: 600;
        padding: 4px 12px;
        border-radius: 20px;
        z-index: 5;
      }

      .plan-header {
        text-align: center;
        margin-bottom: 24px;
        padding-bottom: 16px;
        border-bottom: 1px solid #E5E7EB;
      }

      .plan-name {
        font-size: 22px; /* Slightly reduced */
        font-weight: 600;
        color: #121C41;
        margin: 0 0 16px 0;
      }

      .plan-price {
        display: flex;
        align-items: baseline;
        justify-content: center;
        margin-bottom: 8px;
      }

      .currency {
        font-size: 24px;
        font-weight: 600;
        color: #121C41;
      }

      .amount {
        font-size: 42px; /* Slightly reduced */
        font-weight: 700;
        color: #121C41;
        line-height: 1;
      }

      .period {
        font-size: 16px;
        color: #6B7280;
        margin-left: 4px;
      }

      .billed-annually {
        font-size: 12px;
        color: #6B7280;
      }

      .monthly-equivalent {
        font-size: 14px;
        color: #6B7280;
        margin-top: 4px;
        font-style: italic;
      }

      .annual-payment-info {
        font-size: 13px;
        color: #6B7280;
        margin-top: 4px;
        font-style: italic;
        line-height: 1.3;
      }

      .plan-features {
        list-style: none;
        padding: 0;
        margin: 0 0 24px 0;
        flex-grow: 1;
      }

      .plan-features li {
        display: flex;
        align-items: flex-start; /* Changed from center for better alignment */
        margin-bottom: 12px;
        font-size: 14px;
        color: #4B5563;
      }

      .feature-check {
        margin-right: 8px;
        flex-shrink: 0;
        margin-top: 2px; /* Better alignment with text */
      }

      .plan-cta-button {
        background-color: #1547BB;
        color: white;
        border: none;
        border-radius: 6px;
        padding: 12px 24px;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        transition: background-color 0.2s;
        width: 100%;
      }

      .plan-cta-button:hover {
        background-color: #0D2F87;
      }

      .subscription-plan.selected .plan-cta-button {
        background-color: #0D2F87;
      }

      /* New button styles for subscription management */
      .plan-cta-button.cancel-button {
        background-color: #EF4444;
      }

      .plan-cta-button.cancel-button:hover {
        background-color: #DC2626;
      }

      .plan-cta-button.upgrade-button {
        background-color: #10B981;
      }

      .plan-cta-button.upgrade-button:hover {
        background-color: #059669;
      }

      .plan-cta-button.downgrade-button {
        background-color: #F59E0B;
      }

      .plan-cta-button.downgrade-button:hover {
        background-color: #D97706;
      }

      .plan-cta-button.disabled {
        background-color: #9CA3AF;
        cursor: not-allowed;
        opacity: 0.7;
      }

      /* Special styling for the Active button on free trial */
      .subscription-plan[data-plan-id="free_trial"].current-plan .plan-cta-button.disabled {
        background-color: #4F46E5;
        opacity: 0.8;
      }

      .subscription-footer {
        border-top: 1px solid #E5E7EB;
        padding-top: 24px;
        text-align: center;
      }

      .subscription-note {
        font-size: 14px;
        color: #6B7280;
        margin-bottom: 16px;
      }

      .subscription-contact {
        font-size: 16px;
        color: #121C41;
      }

      .contact-link {
        color: #1547BB;
        text-decoration: none;
        font-weight: 500;
      }

      .contact-link:hover {
        text-decoration: underline;
      }

      /* Yearly billing info - no toggle needed */

      /* Pending subscription change notice */
      .pending-subscription-change {
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
      }

      .pending-subscription-change.hidden {
        display: none;
      }

      .pending-subscription-change:not(.hidden) {
        display: flex;
      }

      .pending-change-info {
        flex: 1;
      }

      .pending-change-info p {
        margin: 0;
        font-size: 14px;
        color: #495057;
      }

      .cancel-pending-button {
        background-color: #dc3545;
        color: white;
        border: none;
        border-radius: 4px;
        padding: 8px 12px;
        font-size: 14px;
        cursor: pointer;
        transition: background-color 0.2s;
        margin-left: 10px;
      }

      .cancel-pending-button:hover {
        background-color: #c82333;
      }

      /* Responsive adjustments */
      @media (max-width: 768px) {
        .subscription-modal-content {
          padding: 24px 16px;
        }

        .subscription-plans-grid {
          grid-template-columns: 1fr;
        }

        .subscription-modal-title h2 {
          font-size: 22px;
        }

        .amount {
          font-size: 36px;
        }

        .annual-payment-info {
          font-size: 12px;
          margin-top: 2px;
        }

        /* Handle badge positioning on mobile */
        .subscription-plan.current-plan::before,
        .popular-badge {
          font-size: 10px;
          padding: 3px 10px;
        }

        /* Responsive pending change notice */
        .pending-subscription-change {
          flex-direction: column;
          align-items: flex-start;
        }

        .cancel-pending-button {
          margin-left: 0;
          margin-top: 10px;
          align-self: flex-start;
        }
      }
    `;

    document.head.appendChild(styleEl);
  }

  // Show modal
  async function showSubscriptionModal(isManagementMode = false, required = false, allowDismiss = false) {
    isClosing = false;
    isRequiredModal = required && !allowDismiss;

    // Check for user's current subscription
    currentPlan = await getUserSubscription();
    console.log('Current plan:', currentPlan, 'Required modal:', isRequiredModal, 'Allow dismiss:', allowDismiss);

    // Remove existing modal if it exists
    const existingModal = document.querySelector('.subscription-modal-overlay');
    if (existingModal) {
      existingModal.parentNode.removeChild(existingModal);
      isModalInitialized = false;
    }

    // Create modal
    injectCSS();
    const modalHTML = createModalHTML(isManagementMode);
    const modalContainer = document.createElement('div');
    modalContainer.innerHTML = modalHTML;

    // Add to body
    document.body.appendChild(modalContainer.firstElementChild);

    modalOverlay = document.querySelector('.subscription-modal-overlay');
    modalContent = document.querySelector('.subscription-modal-content');

    // Initialize event listeners
    initializeEventListeners(allowDismiss);

    isModalInitialized = true;

    // Show modal with enhanced smooth animation
    // First set initial state
    modalOverlay.style.opacity = '0';
    modalContent.style.opacity = '0';
    modalContent.style.transform = 'scale(0.95)';

    // Force a reflow to ensure the initial state is applied
    void modalContent.offsetWidth;

    // Then trigger the animation with a slight delay for smoother appearance
    setTimeout(() => {
      if (isClosing) return;

      // Fade in the overlay first
      modalOverlay.style.opacity = '1';

      // Then animate in the content with a slight delay for a more polished effect
      setTimeout(() => {
        modalContent.style.opacity = '1';
        modalContent.style.transform = 'scale(1)';
      }, 50);
    }, 10);

    return new Promise(resolve => {
      // This will be resolved when the user makes a selection or closes the modal
      window.subscriptionModalResult = resolve;
    });
  }

  // Initialize event listeners
  function initializeEventListeners(allowDismiss = false) {
    // Close button
    const closeButton = document.querySelector('.subscription-modal-close');
    closeButton.addEventListener('click', () => {
      if (isRequiredModal && !allowDismiss) {
        // Show warning modal if this is a required modal and dismissal is not allowed
        showSubscriptionRequiredWarning();
      } else {
        hideSubscriptionModal();
      }
    });

    // Click outside to close
    modalOverlay.addEventListener('click', (event) => {
      if (event.target === modalOverlay) {
        if (isRequiredModal && !allowDismiss) {
          // Show warning modal if this is a required modal and dismissal is not allowed
          showSubscriptionRequiredWarning();
        } else {
          hideSubscriptionModal();
        }
      }
    });

    // No billing toggle needed - always yearly

    // Plan selection
    const plans = document.querySelectorAll('.subscription-plan');
    plans.forEach(plan => {
      const planId = plan.getAttribute('data-plan-id');
      const ctaButton = plan.querySelector('button');

      if (ctaButton.disabled) {
        return; // Skip disabled buttons
      }

      ctaButton.addEventListener('click', () => {
        // Check if this is a cancel button
        if (ctaButton.classList.contains('cancel-button')) {
          handleCancelSubscription();
        } else if (!ctaButton.disabled) {
          // Only handle plan selection if the button is not disabled
          handlePlanSelection(plan, planId);
        }
      });

      // Also make the whole card clickable, but only for non-management mode
      // or for plans that aren't the current plan
      if (!ctaButton.classList.contains('cancel-button') &&
          !ctaButton.classList.contains('disabled')) {
        plan.addEventListener('click', (event) => {
          // Only trigger if the click wasn't on the button (as that has its own handler)
          if (!event.target.closest('button')) {
            handlePlanSelection(plan, planId);
          }
        });
      }
    });

    // Contact link
    const contactLink = document.querySelector('.contact-link');
    contactLink.addEventListener('click', (event) => {
      event.preventDefault();
      hideSubscriptionModal();
      // Open email client with pre-filled subject
      window.location.href = 'mailto:<EMAIL>?subject=Custom%20SkillsAssess%20Inquiry';
    });
  }

  // Handle subscription cancellation
  async function handleCancelSubscription() {
    const user = firebase.auth().currentUser;
    if (!user) {
      showNotification('You must be logged in to cancel your subscription.', 'error');
      return;
    }

    // Get the user's subscription details
    try {
      const doc = await firebase.firestore().collection('Admins').doc(user.email).get();
      if (!doc.exists) {
        showNotification('User data not found.', 'error');
        return;
      }

      const userData = doc.data();
      if (!userData.subscriptionId) {
        showNotification('No active subscription found.', 'error');
        return;
      }

      // Use the custom warning modal instead of the browser's confirm dialog
      const confirmed = await WarningModal.show({
        title: 'Cancel Subscription',
        message: 'Are you sure you want to cancel your subscription? You will still have access until the end of your current billing period.',
        confirmText: 'Yes, Cancel',
        cancelText: 'No, Keep Subscription',
        icon: 'warning',
        confirmButtonStyle: 'danger'
      });

      if (!confirmed) {
        return;
      }

      showNotification('Processing cancellation...', 'info');

      // Call the server endpoint to cancel the subscription
      const response = await fetch('/cancel-subscription', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: user.email,
          subscriptionId: userData.subscriptionId
        })
      });

      const result = await response.json();

      if (result.error) {
        throw new Error(result.error);
      }

      // Hide modal and show success message
      hideSubscriptionModal();
      showNotification(`Subscription successfully cancelled. You will have access until ${new Date(result.cancelDate).toLocaleDateString()}.`, 'success');

      // No need to reload the page since we've added a real-time listener in user-menu.js
      // The UI will update automatically when the subscription changes

    } catch (error) {
      console.error('Error cancelling subscription:', error);
      showNotification('There was an error cancelling your subscription. Please try again later.', 'error');
    }
  }

  // Handle plan selection
  async function handlePlanSelection(planElement, planId) {
    // Update UI to show selection
    document.querySelectorAll('.subscription-plan').forEach(plan => {
      plan.classList.remove('selected');
    });
    planElement.classList.add('selected');

    // Process the selection
    try {
      if (planId === 'free_trial') {
        await processFreeTrialSelection();
      } else {
        await processStripeCheckout(planId);
      }
    } catch (error) {
      console.error('Error processing plan selection:', error);
      showNotification('There was an error processing your selection. Please try again.', 'error');
    }
  }

  // Process free trial selection
  async function processFreeTrialSelection() {
    const user = firebase.auth().currentUser;
    if (!user) {
      showNotification('You must be logged in to select a plan.', 'error');
      return;
    }

    try {
      // First check if the user is eligible for a free trial
      const eligibilityResponse = await fetch(`/check-free-trial-eligibility/${user.email}`);
      const eligibilityData = await eligibilityResponse.json();

      if (!eligibilityData.eligible) {
        showNotification('You have already used your free trial. Please select a paid plan.', 'error');
        return;
      }

      // Call the server endpoint to activate free trial
      const response = await fetch('/create-checkout-session', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          priceId: 'price_1REmsePqOZsaOO5kgjo38qgp',
          userId: user.email,
          credits: 5
        })
      });

      const data = await response.json();

      if (data.error) {
        throw new Error(data.message || data.error);
      }

      if (data.isFree) {
        // Hide modal and resolve promise
        hideSubscriptionModal();
        if (window.subscriptionModalResult) {
          window.subscriptionModalResult({
            success: true,
            plan: 'free_trial',
            credits: 5
          });
        }

        showNotification('Free trial activated successfully!', 'success');

        // Redirect to success page if provided
        if (data.redirectUrl) {
          window.location.href = data.redirectUrl;
        }
      } else {
        throw new Error('Invalid response from server');
      }
    } catch (error) {
      console.error('Error processing free trial:', error);
      showNotification(error.message || 'There was an error activating your free trial. Please try again.', 'error');
    }
  }

  // Process Stripe checkout or scheduled change
  async function processStripeCheckout(planId) {
    const user = firebase.auth().currentUser;
    if (!user) {
      showNotification('You must be logged in to select a plan.', 'error');
      return;
    }

    const plan = stripeProducts[planId];
    if (!plan) {
      showNotification('Invalid plan selected.', 'error');
      return;
    }

    try {
      // Get user data to check current subscription
      const doc = await firebase.firestore().collection('Admins').doc(user.email).get();
      if (!doc.exists) {
        showNotification('User data not found.', 'error');
        return;
      }

      const userData = doc.data();

      // Check if user already has an active subscription
      if (userData.subscriptionActive && userData.subscriptionId) {
        // This is an upgrade or downgrade scenario
        return await handleSubscriptionChange(userData, plan);
      }

      // For new subscriptions, proceed with Stripe Checkout
      // Initialize Stripe if not already done
      await initializeStripe();

      showNotification('Preparing checkout...', 'info');

      // Always use yearly price ID
      const priceId = plan.priceId;

      // Call the server endpoint
      const response = await fetch('/create-checkout-session', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          priceId: priceId,
          userId: user.email,
          credits: plan.credits
        })
      });

      const session = await response.json();

      if (session.error) {
        throw new Error(session.error);
      }

      // Redirect to Stripe Checkout
      const result = await stripeInstance.redirectToCheckout({
        sessionId: session.id
      });

      if (result.error) {
        throw new Error(result.error.message);
      }
    } catch (error) {
      console.error('Error processing plan selection:', error);
      showNotification('Could not process your request. Please try again later.', 'error');
    }
  }

  // Handle subscription change (upgrade or downgrade)
  async function handleSubscriptionChange(userData, newPlan) {
    const user = firebase.auth().currentUser;
    if (!user) {
      showNotification('You must be logged in to change your subscription.', 'error');
      return;
    }

    // Determine if this is an upgrade or downgrade
    const planHierarchy = {
      'free_trial': 0,
      'assess1': 1,
      'assess100': 2,
      'assess250': 3,
      'assess500': 4
    };

    const currentPlanId =
      userData.subscriptionType === 'Assess1' ? 'assess1' :
      userData.subscriptionType === 'Assess100' ? 'assess100' :
      userData.subscriptionType === 'Assess250' ? 'assess250' :
      userData.subscriptionType === 'Assess500' ? 'assess500' :
      userData.subscriptionType === 'freeTrial' || userData.subscriptionType === 'free trial' || userData.subscriptionType === 'Free Trial' ? 'free_trial' : 'unknown';

    const currentPlanRank = planHierarchy[currentPlanId] || 0;
    const newPlanRank = planHierarchy[newPlan.id] || 0;

    const isUpgrade = newPlanRank > currentPlanRank;
    const changeType = isUpgrade ? 'upgrade' : 'downgrade';

    // Special handling for free trial upgrades
    const isFromFreeTrial = currentPlanId === 'free_trial';

    // If upgrading from free trial, process immediately instead of scheduling
    if (isFromFreeTrial && isUpgrade) {
      return await processImmediateUpgradeFromFreeTrial(userData, newPlan);
    }

    // Check if there's already a pending change
    if (userData.pendingSubscriptionChange) {
      // Get the effective date for the pending change
      let effectiveDate;
      const pendingChange = userData.pendingSubscriptionChange;
      if (pendingChange.scheduledDate && pendingChange.scheduledDate.seconds) {
        // Handle Firestore Timestamp
        effectiveDate = new Date(pendingChange.scheduledDate.seconds * 1000).toLocaleDateString();
      } else if (pendingChange.scheduledDateIso) {
        // Handle ISO string date
        effectiveDate = new Date(pendingChange.scheduledDateIso).toLocaleDateString();
      } else {
        // Fallback
        effectiveDate = "next billing cycle";
      }

      // Ask user if they want to replace the existing pending change
      const confirmed = await WarningModal.show({
        title: 'Replace Pending Change',
        message: `You already have a pending ${userData.pendingSubscriptionChange.type} to ${userData.pendingSubscriptionChange.toPlan} scheduled for ${effectiveDate}. Do you want to replace it with this ${changeType}?`,
        confirmText: 'Yes, Replace',
        cancelText: 'No, Keep Current Change',
        icon: 'warning'
      });

      if (!confirmed) {
        return;
      }
    }

    // Confirm the change with the user
    let confirmMessage;
    if (isUpgrade) {
      confirmMessage = `You're about to schedule an upgrade from ${userData.subscriptionType} to ${newPlan.name}. This change will take effect at your next billing cycle. Do you want to proceed?`;
    } else {
      confirmMessage = `You're about to schedule a downgrade from ${userData.subscriptionType} to ${newPlan.name}. This change will take effect at your next billing cycle. Do you want to proceed?`;
    }

    const confirmed = await WarningModal.show({
      title: `Confirm ${isUpgrade ? 'Upgrade' : 'Downgrade'}`,
      message: confirmMessage,
      confirmText: `Yes, Schedule ${isUpgrade ? 'Upgrade' : 'Downgrade'}`,
      cancelText: 'Cancel',
      icon: 'info'
    });

    if (!confirmed) {
      return;
    }

    try {
      showNotification(`Scheduling subscription ${changeType}...`, 'info');

      // Call the server endpoint to schedule the change
      const response = await fetch('/schedule-subscription-change', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: user.email,
          subscriptionId: userData.subscriptionId,
          newPriceId: newPlan.priceId,
          isUpgrade: isUpgrade
        })
      });

      const result = await response.json();

      if (result.error) {
        throw new Error(result.error);
      }

      // Hide modal and show success message
      hideSubscriptionModal();

      const effectiveDate = new Date(result.effectiveDate).toLocaleDateString();
      showNotification(`Your subscription ${changeType} to ${newPlan.name} has been scheduled for ${effectiveDate}.`, 'success');

      // No need to reload the page since we've added a real-time listener in user-menu.js
      // The UI will update automatically when the subscription changes

    } catch (error) {
      console.error(`Error scheduling subscription ${changeType}:`, error);
      showNotification(`There was an error scheduling your subscription ${changeType}. Please try again later.`, 'error');
    }
  }

  // New function to process immediate upgrades from free trial
  async function processImmediateUpgradeFromFreeTrial(userData, newPlan) {
    const user = firebase.auth().currentUser;
    if (!user) {
      showNotification('You must be logged in to upgrade your subscription.', 'error');
      return;
    }

    // Confirm the immediate upgrade with the user
    const confirmed = await WarningModal.show({
      title: 'Confirm Immediate Upgrade',
      message: `You're about to upgrade from Free Trial to ${newPlan.name}. This upgrade will take effect immediately. Do you want to proceed?`,
      confirmText: 'Yes, Upgrade Now',
      cancelText: 'Cancel',
      icon: 'info'
    });

    if (!confirmed) {
      return;
    }

    try {
      showNotification('Processing your upgrade...', 'info');

      // Initialize Stripe if not already done
      await initializeStripe();

      // Call the server endpoint for immediate upgrade
      const response = await fetch('/upgrade-from-free-trial', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: user.email,
          priceId: newPlan.priceId,
          credits: newPlan.credits
        })
      });

      const session = await response.json();

      if (session.error) {
        throw new Error(session.error);
      }

      // Hide subscription modal
      hideSubscriptionModal();

      // Redirect to Stripe Checkout for the new subscription
      const result = await stripeInstance.redirectToCheckout({
        sessionId: session.id
      });

      if (result.error) {
        throw new Error(result.error.message);
      }
    } catch (error) {
      console.error('Error processing immediate upgrade:', error);
      showNotification('Could not process your upgrade. Please try again later.', 'error');
    }
  }

  // Hide modal with enhanced smooth animation
  async function hideSubscriptionModal(result = { cancelled: true }) {
    isClosing = true;

    // Animate closing with a smoother sequence
    if (modalOverlay) {
      // First fade out the content
      modalContent.style.opacity = '0';
      modalContent.style.transform = 'scale(0.95)';

      // Then fade out the overlay with a slight delay
      setTimeout(() => {
        modalOverlay.style.opacity = '0';
      }, 50);

      // Remove after animation completes
      setTimeout(() => {
        if (modalOverlay && modalOverlay.parentNode) {
          modalOverlay.parentNode.removeChild(modalOverlay);
        }
        isModalInitialized = false;

        // Resolve promise if it exists
        if (window.subscriptionModalResult) {
          window.subscriptionModalResult(result);
          window.subscriptionModalResult = null;
        }
      }, 350); // Slightly longer to account for the delayed overlay fade
    }
  }

  // Notification
  function showNotification(message, type = 'success') {
    const existingNotifications = document.querySelectorAll('.subscription-notification');
    existingNotifications.forEach(notification => notification.remove());

    const notification = document.createElement('div');
    notification.className = `subscription-notification ${type}`;
    notification.textContent = message;

    // Add styles if not already present
    if (!document.getElementById('subscription-notification-styles')) {
      const style = document.createElement('style');
      style.id = 'subscription-notification-styles';
      style.textContent = `
        .subscription-notification {
          position: fixed;
          top: 20px;
          right: 20px;
          padding: 16px 24px;
          border-radius: 8px;
          font-size: 14px;
          font-weight: 500;
          z-index: 1010;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
          transform: translateX(120%);
          transition: transform 0.3s ease;
          max-width: 350px;
        }

        .subscription-notification.show {
          transform: translateX(0);
        }

        .subscription-notification.success {
          background-color: #1547BB;
          color: white;
        }

        .subscription-notification.error {
          background-color: #FEE2E2;
          color: #991B1B;
          border: 1px solid #FCA5A5;
        }

        .subscription-notification.info {
          background-color: #E0F2FE;
          color: #0C4A6E;
          border: 1px solid #BAE6FD;
        }
      `;
      document.head.appendChild(style);
    }

    document.body.appendChild(notification);

    // Trigger animation
    setTimeout(() => {
      notification.classList.add('show');
    }, 10);

    // Remove after delay
    setTimeout(() => {
      notification.classList.remove('show');
      setTimeout(() => {
        if (document.body.contains(notification)) {
          notification.remove();
        }
      }, 300);
    }, 4000);
  }

  // Show warning when user tries to dismiss a required subscription modal
  function showSubscriptionRequiredWarning() {
    // Check if WarningModal is available
    if (window.WarningModal) {
      window.WarningModal.show({
        title: 'Subscription Required',
        message: 'A subscription is required to access all features. You can select a plan now or later when accessing specific features.',
        confirmText: 'OK',
        cancelText: null, // No cancel button
        icon: 'info'
      });
    } else {
      // Fallback to alert if WarningModal is not available
      alert('A subscription is required to access all features. You can select a plan now or later when accessing specific features.');
    }
  }

  // Public API
  global.SubscriptionModal = {
    show: showSubscriptionModal,
    hide: hideSubscriptionModal,
    getCurrentPlan: getUserSubscription,
    cancelSubscription: handleCancelSubscription,
    checkFreeTrialEligibility: async (userId) => {
      try {
        const response = await fetch(`/check-free-trial-eligibility/${userId}`);
        return await response.json();
      } catch (error) {
        console.error('Error checking free trial eligibility:', error);
        return { eligible: false, error: error.message };
      }
    }
  };
})(typeof window !== 'undefined' ? window : global);
