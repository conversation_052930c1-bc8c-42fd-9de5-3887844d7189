<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Choose Your Learning Pathway</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#f97316',
                        secondary: '#1e3a8a',
                    }
                }
            }
        }
    </script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');
        
        body, html {
            margin: 0;
            padding: 0;
        }

        .glass-container {
            background: rgba(255, 255, 255, 0.705);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.18);
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
            width: 100%;
            max-width: 1024px;
            margin: 0 auto;
        }

        .card {
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(5px);
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }

        .icon {
            transition: all 0.3s ease;
        }

        .card:hover .icon {
            transform: scale(1.1);
        }
        .text-lg.text-gray-700 {
            font-size: 0.9rem;
            color: #4a5568 !important; 
        }
        .text-secondary {
            color: #2c4da5f3 !important;
        }
        .text-gray-600 {
            color: #4b5563 !important;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="glass-container p-8 space-y-8">
        <div class="text-center">
            <h1 class="text-2xl font-semibold text-secondary mb-3">Upskill Your Team: Explore Learning Pathways</h1>
            <p class="text-lg text-gray-700">Find the perfect fit for your team's needs. We offer a range of curated learning tracks designed to empower your employees at all levels.</p>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="card rounded-xl p-6 flex flex-col items-center text-center cursor-pointer" data-path="essentials">
                <div class="icon bg-blue-100 text-blue-600 rounded-full p-3 mb-4">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                    </svg>
                </div>
                <h2 class="text-xl font-semibold mb-2 text-secondary">Essentials</h2>
                <p class="text-sm text-gray-600">Core digital skills for office productivity and basic cybersecurity.</p>
            </div>
            <div class="card rounded-xl p-6 flex flex-col items-center text-center cursor-pointer" data-path="intermediate">
                <div class="icon bg-green-100 text-green-600 rounded-full p-3 mb-4">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                    </svg>
                </div>
                <h2 class="text-xl font-semibold mb-2 text-secondary">Intermediate</h2>
                <p class="text-sm text-gray-600">Advanced office techniques and enhanced collaboration skills.</p>
            </div>
            <div class="card rounded-xl p-6 flex flex-col items-center text-center cursor-pointer" data-path="advanced">
                <div class="icon bg-purple-100 text-purple-600 rounded-full p-3 mb-4">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                    </svg>
                </div>
                <h2 class="text-xl font-semibold mb-2 text-secondary">Advanced</h2>
                <p class="text-sm text-gray-600">Expert-level proficiency in Microsoft tools and cybersecurity.</p>
            </div>
            <div class="card rounded-xl p-6 flex flex-col items-center text-center cursor-pointer" data-path="champions">
                <div class="icon bg-yellow-100 text-yellow-600 rounded-full p-3 mb-4">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                    </svg>
                </div>
                <h2 class="text-xl font-semibold mb-2 text-secondary">Champions</h2>
                <p class="text-sm text-gray-600">Cutting-edge AI, collaboration, and security for top innovators.</p>
            </div>
        </div>
    </div>
    <script src="learningpathui.js"></script>
</body>
</html>