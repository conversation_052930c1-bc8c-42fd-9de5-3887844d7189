#!/bin/bash
# This script sets up Stripe products using the Stripe CLI
# You will need to install the Stripe CLI: https://stripe.com/docs/stripe-cli
# And jq for JSON parsing

# Check if stripe CLI is installed and authenticated
if ! command -v stripe &> /dev/null; then
    echo "Stripe CLI is not installed. Please install it first."
    exit 1
fi

# Check if jq is installed
if ! command -v jq &> /dev/null; then
    echo "jq is not installed. Please install it first."
    exit 1
fi

# 1. Free Trial
echo "Creating Free Trial product"
FREE_TRIAL_PRODUCT=$(stripe products create \
  --name="Free Trial" \
  --description="14-day free trial with 5 assessments")

FREE_TRIAL_PRODUCT_ID=$(echo "$FREE_TRIAL_PRODUCT" | jq -r '.id')
echo "Free Trial Product ID: $FREE_TRIAL_PRODUCT_ID"

echo "Creating Free Trial price"
FREE_TRIAL_PRICE=$(stripe prices create \
  --product="$FREE_TRIAL_PRODUCT_ID" \
  --unit-amount=0 \
  --currency=gbp \
  --nickname="free_trial" \
  -d "metadata[plan]=Free Trial" \
  -d "metadata[credits]=5")

FREE_TRIAL_PRICE_ID=$(echo "$FREE_TRIAL_PRICE" | jq -r '.id')
echo "Free Trial Price ID: $FREE_TRIAL_PRICE_ID"

# 2. Assess100
echo "Creating Assess100 product"
ASSESS100_PRODUCT=$(stripe products create \
  --name="Assess100" \
  --description="Includes 100 assessments, 12 month access")

ASSESS100_PRODUCT_ID=$(echo "$ASSESS100_PRODUCT" | jq -r '.id')
echo "Assess100 Product ID: $ASSESS100_PRODUCT_ID"

echo "Creating Assess100 monthly price"
ASSESS100_PRICE=$(stripe prices create \
  --product="$ASSESS100_PRODUCT_ID" \
  --unit-amount=9900 \
  --currency=gbp \
  --nickname="assess100_monthly" \
  -d "recurring[interval]=month" \
  -d "recurring[interval_count]=1" \
  -d "metadata[plan]=Assess100" \
  -d "metadata[credits]=100")

ASSESS100_PRICE_ID=$(echo "$ASSESS100_PRICE" | jq -r '.id')
echo "Assess100 Monthly Price ID: $ASSESS100_PRICE_ID"

echo "Creating Assess100 annual price"
ASSESS100_YEARLY_PRICE=$(stripe prices create \
  --product="$ASSESS100_PRODUCT_ID" \
  --unit-amount=118800 \
  --currency=gbp \
  --nickname="assess100_yearly" \
  -d "recurring[interval]=year" \
  -d "recurring[interval_count]=1" \
  -d "metadata[plan]=Assess100" \
  -d "metadata[credits]=100")

ASSESS100_YEARLY_PRICE_ID=$(echo "$ASSESS100_YEARLY_PRICE" | jq -r '.id')
echo "Assess100 Yearly Price ID: $ASSESS100_YEARLY_PRICE_ID"

# 3. Assess250
echo "Creating Assess250 product"
ASSESS250_PRODUCT=$(stripe products create \
  --name="Assess250" \
  --description="Includes 250 assessments, 12 month access")

ASSESS250_PRODUCT_ID=$(echo "$ASSESS250_PRODUCT" | jq -r '.id')
echo "Assess250 Product ID: $ASSESS250_PRODUCT_ID"

echo "Creating Assess250 monthly price"
ASSESS250_PRICE=$(stripe prices create \
  --product="$ASSESS250_PRODUCT_ID" \
  --unit-amount=19900 \
  --currency=gbp \
  --nickname="assess250_monthly" \
  -d "recurring[interval]=month" \
  -d "recurring[interval_count]=1" \
  -d "metadata[plan]=Assess250" \
  -d "metadata[credits]=250")

ASSESS250_PRICE_ID=$(echo "$ASSESS250_PRICE" | jq -r '.id')
echo "Assess250 Monthly Price ID: $ASSESS250_PRICE_ID"

echo "Creating Assess250 annual price"
ASSESS250_YEARLY_PRICE=$(stripe prices create \
  --product="$ASSESS250_PRODUCT_ID" \
  --unit-amount=238800 \
  --currency=gbp \
  --nickname="assess250_yearly" \
  -d "recurring[interval]=year" \
  -d "recurring[interval_count]=1" \
  -d "metadata[plan]=Assess250" \
  -d "metadata[credits]=250")

ASSESS250_YEARLY_PRICE_ID=$(echo "$ASSESS250_YEARLY_PRICE" | jq -r '.id')
echo "Assess250 Yearly Price ID: $ASSESS250_YEARLY_PRICE_ID"

# 4. Assess500
echo "Creating Assess500 product"
ASSESS500_PRODUCT=$(stripe products create \
  --name="Assess500" \
  --description="Includes 500 assessments, 12 month access")

ASSESS500_PRODUCT_ID=$(echo "$ASSESS500_PRODUCT" | jq -r '.id')
echo "Assess500 Product ID: $ASSESS500_PRODUCT_ID"

echo "Creating Assess500 monthly price"
ASSESS500_PRICE=$(stripe prices create \
  --product="$ASSESS500_PRODUCT_ID" \
  --unit-amount=29900 \
  --currency=gbp \
  --nickname="assess500_monthly" \
  -d "recurring[interval]=month" \
  -d "recurring[interval_count]=1" \
  -d "metadata[plan]=Assess500" \
  -d "metadata[credits]=500")

ASSESS500_PRICE_ID=$(echo "$ASSESS500_PRICE" | jq -r '.id')
echo "Assess500 Monthly Price ID: $ASSESS500_PRICE_ID"

echo "Creating Assess500 annual price"
ASSESS500_YEARLY_PRICE=$(stripe prices create \
  --product="$ASSESS500_PRODUCT_ID" \
  --unit-amount=349900 \
  --currency=gbp \
  --nickname="assess500_yearly" \
  -d "recurring[interval]=year" \
  -d "recurring[interval_count]=1" \
  -d "metadata[plan]=Assess500" \
  -d "metadata[credits]=500")

ASSESS500_YEARLY_PRICE_ID=$(echo "$ASSESS500_YEARLY_PRICE" | jq -r '.id')
echo "Assess500 Yearly Price ID: $ASSESS500_YEARLY_PRICE_ID"

echo "Stripe products and prices created successfully!"
echo ""
echo "Please update the following price IDs in your code:"
echo "Free Trial Price ID: $FREE_TRIAL_PRICE_ID"
echo "Assess100 Monthly Price ID: $ASSESS100_PRICE_ID"
echo "Assess100 Yearly Price ID: $ASSESS100_YEARLY_PRICE_ID"
echo "Assess250 Monthly Price ID: $ASSESS250_PRICE_ID"
echo "Assess250 Yearly Price ID: $ASSESS250_YEARLY_PRICE_ID"
echo "Assess500 Monthly Price ID: $ASSESS500_PRICE_ID"
echo "Assess500 Yearly Price ID: $ASSESS500_YEARLY_PRICE_ID"
echo ""
echo "Update these in subscription-modal.js and stripe-handler.js"
