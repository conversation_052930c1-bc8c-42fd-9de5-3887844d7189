#!/bin/bash

# This script creates new Stripe products and prices with updated pricing
# It requires the Stripe CLI to be installed and authenticated

echo "Creating new Stripe products and prices with updated pricing"
echo "============================================================"

# 1. Assess100
echo "Creating Assess100 product"
ASSESS100_PRODUCT=$(stripe products create \
  --name="Assess100" \
  --description="Includes 100 assessments, 12 month access")

ASSESS100_PRODUCT_ID=$(echo "$ASSESS100_PRODUCT" | jq -r '.id')
echo "Assess100 Product ID: $ASSESS100_PRODUCT_ID"

echo "Creating Assess100 annual price"
ASSESS100_YEARLY_PRICE=$(stripe prices create \
  --product="$ASSESS100_PRODUCT_ID" \
  --unit-amount=99900 \
  --currency=gbp \
  --nickname="assess100_yearly" \
  -d "recurring[interval]=year" \
  -d "recurring[interval_count]=1" \
  -d "metadata[plan]=Assess100" \
  -d "metadata[credits]=100")

ASSESS100_YEARLY_PRICE_ID=$(echo "$ASSESS100_YEARLY_PRICE" | jq -r '.id')
echo "Assess100 Yearly Price ID: $ASSESS100_YEARLY_PRICE_ID"

# 2. Assess250
echo "Creating Assess250 product"
ASSESS250_PRODUCT=$(stripe products create \
  --name="Assess250" \
  --description="Includes 250 assessments, 12 month access")

ASSESS250_PRODUCT_ID=$(echo "$ASSESS250_PRODUCT" | jq -r '.id')
echo "Assess250 Product ID: $ASSESS250_PRODUCT_ID"

echo "Creating Assess250 annual price"
ASSESS250_YEARLY_PRICE=$(stripe prices create \
  --product="$ASSESS250_PRODUCT_ID" \
  --unit-amount=199900 \
  --currency=gbp \
  --nickname="assess250_yearly" \
  -d "recurring[interval]=year" \
  -d "recurring[interval_count]=1" \
  -d "metadata[plan]=Assess250" \
  -d "metadata[credits]=250")

ASSESS250_YEARLY_PRICE_ID=$(echo "$ASSESS250_YEARLY_PRICE" | jq -r '.id')
echo "Assess250 Yearly Price ID: $ASSESS250_YEARLY_PRICE_ID"

# 3. Assess500
echo "Creating Assess500 product"
ASSESS500_PRODUCT=$(stripe products create \
  --name="Assess500" \
  --description="Includes 500 assessments, 12 month access")

ASSESS500_PRODUCT_ID=$(echo "$ASSESS500_PRODUCT" | jq -r '.id')
echo "Assess500 Product ID: $ASSESS500_PRODUCT_ID"

echo "Creating Assess500 annual price"
ASSESS500_YEARLY_PRICE=$(stripe prices create \
  --product="$ASSESS500_PRODUCT_ID" \
  --unit-amount=299900 \
  --currency=gbp \
  --nickname="assess500_yearly" \
  -d "recurring[interval]=year" \
  -d "recurring[interval_count]=1" \
  -d "metadata[plan]=Assess500" \
  -d "metadata[credits]=500")

ASSESS500_YEARLY_PRICE_ID=$(echo "$ASSESS500_YEARLY_PRICE" | jq -r '.id')
echo "Assess500 Yearly Price ID: $ASSESS500_YEARLY_PRICE_ID"

# 4. TopUp100
echo "Creating TopUp100 product"
TOPUP100_PRODUCT=$(stripe products create \
  --name="TopUp100" \
  --description="One-time purchase of 100 additional assessments")

TOPUP100_PRODUCT_ID=$(echo "$TOPUP100_PRODUCT" | jq -r '.id')
echo "TopUp100 Product ID: $TOPUP100_PRODUCT_ID"

echo "Creating TopUp100 price"
TOPUP100_PRICE=$(stripe prices create \
  --product="$TOPUP100_PRODUCT_ID" \
  --unit-amount=99900 \
  --currency=gbp \
  --nickname="topup100" \
  -d "metadata[plan]=TopUp100" \
  -d "metadata[credits]=100")

TOPUP100_PRICE_ID=$(echo "$TOPUP100_PRICE" | jq -r '.id')
echo "TopUp100 Price ID: $TOPUP100_PRICE_ID"

# 5. TopUp250
echo "Creating TopUp250 product"
TOPUP250_PRODUCT=$(stripe products create \
  --name="TopUp250" \
  --description="One-time purchase of 250 additional assessments")

TOPUP250_PRODUCT_ID=$(echo "$TOPUP250_PRODUCT" | jq -r '.id')
echo "TopUp250 Product ID: $TOPUP250_PRODUCT_ID"

echo "Creating TopUp250 price"
TOPUP250_PRICE=$(stripe prices create \
  --product="$TOPUP250_PRODUCT_ID" \
  --unit-amount=199900 \
  --currency=gbp \
  --nickname="topup250" \
  -d "metadata[plan]=TopUp250" \
  -d "metadata[credits]=250")

TOPUP250_PRICE_ID=$(echo "$TOPUP250_PRICE" | jq -r '.id')
echo "TopUp250 Price ID: $TOPUP250_PRICE_ID"

# 6. TopUp500
echo "Creating TopUp500 product"
TOPUP500_PRODUCT=$(stripe products create \
  --name="TopUp500" \
  --description="One-time purchase of 500 additional assessments")

TOPUP500_PRODUCT_ID=$(echo "$TOPUP500_PRODUCT" | jq -r '.id')
echo "TopUp500 Product ID: $TOPUP500_PRODUCT_ID"

echo "Creating TopUp500 price"
TOPUP500_PRICE=$(stripe prices create \
  --product="$TOPUP500_PRODUCT_ID" \
  --unit-amount=299900 \
  --currency=gbp \
  --nickname="topup500" \
  -d "metadata[plan]=TopUp500" \
  -d "metadata[credits]=500")

TOPUP500_PRICE_ID=$(echo "$TOPUP500_PRICE" | jq -r '.id')
echo "TopUp500 Price ID: $TOPUP500_PRICE_ID"

echo "============================================================"
echo "Stripe products and prices created successfully!"
echo ""
echo "Please update the following price IDs in your code:"
echo ""
echo "Subscription Products:"
echo "Assess100 Yearly Price ID: $ASSESS100_YEARLY_PRICE_ID"
echo "Assess250 Yearly Price ID: $ASSESS250_YEARLY_PRICE_ID"
echo "Assess500 Yearly Price ID: $ASSESS500_YEARLY_PRICE_ID"
echo ""
echo "Top-up Products:"
echo "TopUp100 Price ID: $TOPUP100_PRICE_ID"
echo "TopUp250 Price ID: $TOPUP250_PRICE_ID"
echo "TopUp500 Price ID: $TOPUP500_PRICE_ID"
echo ""
echo "Update these IDs in the following files:"
echo "- public/subscription-modal.js"
echo "- public/stripe-handler.js"
echo "- public/topup-modal.js"
echo "- server.js"
