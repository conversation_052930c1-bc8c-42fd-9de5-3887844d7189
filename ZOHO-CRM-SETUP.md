# Zoho CRM Integration Setup

This document provides instructions for setting up the Zoho CRM integration for the SkillsAssess dashboard. The integration sends user signup data to Zoho CRM as leads.

## Prerequisites

1. A Zoho CRM account
2. Access to the Zoho API Console
3. Node.js installed on your system

## Setup Steps

### 1. Create a Self Client in Zoho API Console

1. Log in to your Zoho API Console at https://api-console.zoho.com/
2. Click on the "Add Client" button and choose "Self Client"
3. Give your Self Client a name (e.g., "SkillsAssess Dashboard")
4. Add a description (e.g., "Integration for SkillsAssess dashboard user signups")
5. Under "Scopes", add `ZohoCRM.modules.all`
6. Choose a time duration for the token (e.g., 10 minutes)
7. Click on the "Create" button
8. Copy the "Client ID" and "Client Secret" values and save them for later

### 2. Generate a Grant Token

1. In the Zoho API Console, click on your Self Client
2. Click on the "Generate Code" button
3. Select the appropriate scope (`ZohoCRM.modules.all`)
4. Click "Generate Code"
5. Copy the generated code (this is your grant token)

### 3. Get the Refresh Token

Use Postman or a similar tool to exchange the grant token for a refresh token:

1. Create a new request in Postman
2. Set the request method to "POST"
3. Set the URL to `https://accounts.zoho.com/oauth/v2/token`
4. Set the header to `Content-Type: application/x-www-form-urlencoded`
5. In the "Body" section, add the following values:
   - `code`: The grant token generated in the previous step
   - `grant_type`: authorization_code
   - `client_id`: Your Client ID
   - `client_secret`: Your Client Secret
   - `redirect_uri`: http://localhost:3000 (or your application's URL)
6. Click "Send" to make the request
7. The response will contain a `refresh_token` value - copy this value

### 4. Update Environment Variables

Add the following variables to your `.env` file:

```
ZOHO_CLIENT_ID=your_client_id
ZOHO_CLIENT_SECRET=your_client_secret
ZOHO_REFRESH_TOKEN=your_refresh_token
```

Replace `your_client_id`, `your_client_secret`, and `your_refresh_token` with the values you obtained in the previous steps.

#### Optional: Configure API Domain

If you're using Zoho CRM in a region other than the US, you may need to specify the correct API domain. Add these optional environment variables:

```
# Optional: Specify the Zoho API domain based on your region
ZOHO_API_DOMAIN=https://www.zohoapis.com
# Optional: Override the accounts URL if needed
ZOHO_ACCOUNTS_URL=https://accounts.zoho.com/oauth/v2/token
```

Use the appropriate domain for your region:
- US: `https://www.zohoapis.com` (default)
- EU: `https://www.zohoapis.eu`
- IN: `https://www.zohoapis.in`
- AU: `https://www.zohoapis.com.au`
- JP: `https://www.zohoapis.jp`
- CN: `https://www.zohoapis.com.cn`

## Testing the Integration

### Local Testing

1. Start your server:
   ```
   npm start
   ```

2. Use the test endpoint to verify the integration:
   ```
   curl -X POST http://localhost:3000/test-zoho-crm -H "Content-Type: application/json" -d '{"firstname":"Test","lastname":"User","email":"<EMAIL>","company":"Test Company"}'
   ```

   Or use a tool like Postman to send a POST request to `http://localhost:3000/test-zoho-crm` with the following JSON body:
   ```json
   {
     "firstname": "Test",
     "lastname": "User",
     "email": "<EMAIL>",
     "company": "Test Company"
   }
   ```

3. Check the server logs for the response and verify that the test lead was added to your Zoho CRM account.

#### Testing with Different Regions

If you're using Zoho CRM in a region other than the US, you may need to specify the correct API domain in your `.env` file:

```
# For EU region
ZOHO_API_DOMAIN=https://www.zohoapis.eu
ZOHO_ACCOUNTS_URL=https://accounts.zoho.eu/oauth/v2/token

# For India region
ZOHO_API_DOMAIN=https://www.zohoapis.in
ZOHO_ACCOUNTS_URL=https://accounts.zoho.in/oauth/v2/token

# For Australia region
ZOHO_API_DOMAIN=https://www.zohoapis.com.au
ZOHO_ACCOUNTS_URL=https://accounts.zoho.com.au/oauth/v2/token
```

After updating your `.env` file, restart your server and run the test again.

### Production Deployment

1. Set up the environment variables in your production environment:
   - `ZOHO_CLIENT_ID`
   - `ZOHO_CLIENT_SECRET`
   - `ZOHO_REFRESH_TOKEN`

2. Deploy your application to your production server.

3. Test the integration by signing up a test user on your production site.

4. Verify that the user data is sent to Zoho CRM by checking your Zoho CRM account.

## Troubleshooting

### Common Issues

1. **Invalid Refresh Token**: If you see an error like "Invalid refresh token" or "invalid oauth token", your refresh token may have expired or be invalid. Generate a new grant token and refresh token following steps 2 and 3 above.

2. **Authentication Failed (401 Unauthorized)**: This is the most common issue and can be caused by:
   - Incorrect Client ID or Client Secret
   - Expired or invalid refresh token
   - Using the wrong API domain for your region
   - Insufficient permissions in your Zoho CRM account
   - Your Zoho CRM account is inactive or has billing issues

3. **Network Error**: Ensure that your server can connect to the Zoho CRM API endpoints. Check your firewall settings and network connectivity.

4. **Missing Required Fields**: Make sure all required fields (firstname, lastname, email, company) are being sent in the request.

5. **Wrong API Domain**: If you're using Zoho CRM in a region other than the US, make sure to set the correct `ZOHO_API_DOMAIN` environment variable.

6. **Rate Limiting**: Zoho CRM has API rate limits. If you're making too many requests in a short period, you may see errors like "You have made too many requests continuously. Please try again after some time." The integration now includes automatic handling for rate limiting by:
   - Enforcing a minimum interval between token requests
   - Automatically waiting and retrying when rate limit errors are detected
   - Using token caching to minimize API calls

   If you still encounter rate limiting issues, wait for 15-30 minutes before trying again.

### Debugging

The integration includes detailed logging. Check your server logs for messages with the prefix `[Zoho CRM]` to diagnose issues.

#### Testing the Authentication Separately

You can test just the authentication part using a tool like Postman:

1. Create a POST request to `https://accounts.zoho.com/oauth/v2/token` (or your region's equivalent)
2. Set the Content-Type header to `application/x-www-form-urlencoded`
3. Add the following parameters:
   - `refresh_token`: Your refresh token
   - `client_id`: Your client ID
   - `client_secret`: Your client secret
   - `grant_type`: refresh_token
4. Send the request and check if you receive an access token in the response

#### Verifying API Access

You can test direct API access using the access token:

1. Get an access token using the method above
2. Create a GET request to `https://www.zohoapis.com/crm/v2/Leads` (or your region's equivalent)
3. Add the Authorization header: `Zoho-oauthtoken YOUR_ACCESS_TOKEN`
4. Send the request and check if you can retrieve leads from your CRM

## Customizing the Integration

### Adding Additional Fields

To add additional fields to the lead data sent to Zoho CRM, modify the `addLead` function in `integrations/zoho-crm.js`:

```javascript
// Prepare the lead data
const leadData = {
  data: [{
    Company: userData.company,
    Last_Name: userData.lastname,
    First_Name: userData.firstname,
    Email: userData.email,
    // Add additional fields here
    Source: 'Website Signup',
    // Example: Add a custom field
    // Custom_Field: userData.customField,
  }],
  // ...
};
```

Make sure to update the corresponding code in `server.js` and `signup.js` to include the new fields.

## Maintenance

### Refresh Token Expiry

Zoho refresh tokens do not expire unless explicitly revoked. However, if you need to generate a new refresh token, follow steps 2 and 3 in the Setup Steps section.

### Access Token Caching

The integration caches access tokens to minimize API calls. The cache expires 5 minutes before the actual token expiration to ensure smooth operation.

## Support

If you encounter any issues with the Zoho CRM integration, please contact the development team or refer to the [Zoho CRM API documentation](https://www.zoho.com/crm/developer/docs/api/v2/overview.html).
