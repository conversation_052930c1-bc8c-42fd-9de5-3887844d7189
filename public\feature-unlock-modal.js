/**
 * Feature Unlock Modal Component
 * A user-friendly modal that appears when users try to access locked features
 * Provides options to invite users or try demo mode
 */

(function(global) {
  'use strict';

  let isModalInitialized = false;
  let isClosing = false;
  let modalOverlay = null;
  let modalContent = null;
  let currentPromiseResolve = null;

  /**
   * Creates the feature unlock modal HTML structure
   * @param {Object} options - Configuration options
   * @returns {string} HTML string for the modal
   */
  function createFeatureUnlockModalHTML(options) {
    const {
      featureName = 'Dashboard',
      employeeCount = 0
    } = options;

    return `
      <div class="feature-unlock-modal-overlay">
        <div class="feature-unlock-modal-content">
          <div class="feature-unlock-modal-header">
            <div class="unlock-icon">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M13.5 10.5V6.75a4.5 4.5 0 119 0v3.75M3.75 21.75h16.5c.621 0 1.125-.504 1.125-1.125v-9.75c0-.621-.504-1.125-1.125-1.125H3.75c-.621 0-1.125.504-1.125 1.125v9.75c0 .621.504 1.125 1.125 1.125z" />
              </svg>
            </div>
            <h1 class="unlock-title">Unlock ${featureName}</h1>
            <p class="unlock-subtitle">Get started with your team to access this feature</p>
          </div>
          
          <div class="feature-unlock-modal-body">
            <div class="unlock-explanation">
              <div class="explanation-icon">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M18 18.72a9.094 9.094 0 003.741-.479 3 3 0 00-4.682-2.72m.94 3.198l.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0112 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 016 18.719m12 0a5.971 5.971 0 00-.941-3.197m0 0A5.995 5.995 0 0012 12.75a5.995 5.995 0 00-5.058 2.772m0 0a3 3 0 00-4.681 2.72 8.986 8.986 0 003.74.477m.94-3.197a5.971 5.971 0 00-.94 3.197M15 6.75a3 3 0 11-6 0 3 3 0 016 0zm6 3a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0zm-13.5 0a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z" />
                </svg>
              </div>
              <div class="explanation-content">
                <h3>Why is this feature locked?</h3>
                <p>The ${featureName.toLowerCase()} becomes available once at least one team member completes an assessment. This ensures you have meaningful data to view and analyze.</p>
              </div>
            </div>

            <div class="unlock-progress">
              <div class="progress-info">
                <span class="progress-label">Team members with assessments:</span>
                <span class="progress-count">${employeeCount}/1</span>
              </div>
              <div class="progress-bar">
                <div class="progress-fill" style="width: ${Math.min(employeeCount * 100, 100)}%"></div>
              </div>
            </div>

            <div class="unlock-options">
              <div class="option-card primary-option">
                <div class="option-icon">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M21.75 6.75v10.5a2.25 2.25 0 01-2.25 2.25h-15a2.25 2.25 0 01-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0019.5 4.5h-15a2.25 2.25 0 00-2.25 2.25m19.5 0v.243a2.25 2.25 0 01-1.07 1.916l-7.5 4.615a2.25 2.25 0 01-2.36 0L3.32 8.91a2.25 2.25 0 01-1.07-1.916V6.75" />
                  </svg>
                </div>
                <div class="option-content">
                  <h4>Invite Your Team</h4>
                  <p>Send assessment invitations to your team members to unlock all features</p>
                </div>
              </div>

              <div class="option-card secondary-option">
                <div class="option-icon">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" />
                    <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                </div>
                <div class="option-content">
                  <h4>Preview in Demo Mode</h4>
                  <p>Explore the ${featureName.toLowerCase()} with sample data to see what's possible</p>
                </div>
              </div>
            </div>
          </div>

          <div class="feature-unlock-modal-footer">
            <button class="unlock-cancel-button" onclick="window.FeatureUnlockModal.cancel()">
              Maybe Later
            </button>
            <button class="unlock-demo-button" onclick="window.FeatureUnlockModal.tryDemo()">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" />
                <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
              Try Demo
            </button>
            <button class="unlock-primary-button" onclick="window.FeatureUnlockModal.goToInvitations()">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M21.75 6.75v10.5a2.25 2.25 0 01-2.25 2.25h-15a2.25 2.25 0 01-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0019.5 4.5h-15a2.25 2.25 0 00-2.25 2.25m19.5 0v.243a2.25 2.25 0 01-1.07 1.916l-7.5 4.615a2.25 2.25 0 01-2.36 0L3.32 8.91a2.25 2.25 0 01-1.07-1.916V6.75" />
              </svg>
              Invite Team
            </button>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * Injects the required CSS styles
   */
  function injectCSS() {
    if (document.getElementById('feature-unlock-modal-styles')) return;

    const style = document.createElement('style');
    style.id = 'feature-unlock-modal-styles';
    style.textContent = `
      .feature-unlock-modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(18, 28, 65, 0.4);
        backdrop-filter: blur(4px);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 3000;
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      .feature-unlock-modal-content {
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        border-radius: 1rem;
        width: 90%;
        max-width: 500px;
        max-height: 90vh;
        overflow-y: auto;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        transform: scale(0.95);
        opacity: 0;
        transition: all 0.3s ease;
      }

      .feature-unlock-modal-header {
        text-align: center;
        padding: 2rem 2rem 1rem;
        border-bottom: 1px solid #e2e8f0;
      }

      .unlock-icon {
        width: 3.5rem;
        height: 3.5rem;
        margin: 0 auto 1rem;
        background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
      }

      .unlock-icon svg {
        width: 1.75rem;
        height: 1.75rem;
      }

      .unlock-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: #1e293b;
        margin: 0 0 0.5rem;
      }

      .unlock-subtitle {
        font-size: 1rem;
        color: #64748b;
        margin: 0;
      }

      .feature-unlock-modal-body {
        padding: 1.5rem 2rem;
      }

      .unlock-explanation {
        display: flex;
        align-items: flex-start;
        margin-bottom: 1.5rem;
        padding: 1rem;
        background-color: #f8fafc;
        border-radius: 0.5rem;
        border-left: 4px solid #3B82F6;
      }

      .explanation-icon {
        flex-shrink: 0;
        margin-right: 0.75rem;
        color: #3B82F6;
      }

      .explanation-icon svg {
        width: 1.25rem;
        height: 1.25rem;
      }

      .explanation-content h3 {
        font-size: 0.875rem;
        font-weight: 600;
        color: #1e293b;
        margin: 0 0 0.25rem;
      }

      .explanation-content p {
        font-size: 0.875rem;
        color: #475569;
        line-height: 1.5;
        margin: 0;
      }

      .unlock-progress {
        margin-bottom: 1.5rem;
      }

      .progress-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.5rem;
      }

      .progress-label {
        font-size: 0.875rem;
        color: #475569;
      }

      .progress-count {
        font-size: 0.875rem;
        font-weight: 600;
        color: #1e293b;
      }

      .progress-bar {
        width: 100%;
        height: 0.5rem;
        background-color: #e2e8f0;
        border-radius: 0.25rem;
        overflow: hidden;
      }

      .progress-fill {
        height: 100%;
        background: linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%);
        transition: width 0.3s ease;
      }

      .unlock-options {
        display: grid;
        gap: 1rem;
        margin-bottom: 1rem;
      }

      .option-card {
        display: flex;
        align-items: center;
        padding: 1rem;
        border-radius: 0.5rem;
        border: 2px solid transparent;
        cursor: pointer;
        transition: all 0.2s ease;
      }

      .primary-option {
        background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
        border-color: #3B82F6;
      }

      .secondary-option {
        background: linear-gradient(135deg, #fefce8 0%, #fef3c7 100%);
        border-color: #F59E0B;
      }

      .option-card:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      .option-icon {
        flex-shrink: 0;
        margin-right: 0.75rem;
      }

      .primary-option .option-icon {
        color: #3B82F6;
      }

      .secondary-option .option-icon {
        color: #F59E0B;
      }

      .option-icon svg {
        width: 1.25rem;
        height: 1.25rem;
      }

      .option-content h4 {
        font-size: 0.875rem;
        font-weight: 600;
        color: #1e293b;
        margin: 0 0 0.25rem;
      }

      .option-content p {
        font-size: 0.75rem;
        color: #475569;
        line-height: 1.4;
        margin: 0;
      }

      .feature-unlock-modal-footer {
        display: flex;
        justify-content: center;
        gap: 0.75rem;
        padding: 1rem 2rem 2rem;
        border-top: 1px solid #e2e8f0;
      }

      .unlock-cancel-button,
      .unlock-demo-button,
      .unlock-primary-button {
        display: inline-flex;
        align-items: center;
        padding: 0.625rem 1.25rem;
        border-radius: 0.5rem;
        font-weight: 600;
        font-size: 0.875rem;
        cursor: pointer;
        transition: all 0.2s;
        border: none;
        gap: 0.5rem;
      }

      .unlock-cancel-button {
        background-color: #f1f5f9;
        color: #475569;
        border: 1px solid #cbd5e1;
      }

      .unlock-cancel-button:hover {
        background-color: #e2e8f0;
        color: #334155;
      }

      .unlock-demo-button {
        background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
        color: white;
      }

      .unlock-demo-button:hover {
        background: linear-gradient(135deg, #D97706 0%, #B45309 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(245, 158, 11, 0.4);
      }

      .unlock-primary-button {
        background: linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%);
        color: white;
      }

      .unlock-primary-button:hover {
        background: linear-gradient(135deg, #2563EB 0%, #1E40AF 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
      }

      .unlock-cancel-button svg,
      .unlock-demo-button svg,
      .unlock-primary-button svg {
        width: 1rem;
        height: 1rem;
      }

      @media (max-width: 640px) {
        .feature-unlock-modal-content {
          width: 95%;
          margin: 1rem;
        }
        
        .feature-unlock-modal-header,
        .feature-unlock-modal-body,
        .feature-unlock-modal-footer {
          padding-left: 1.5rem;
          padding-right: 1.5rem;
        }
        
        .feature-unlock-modal-footer {
          flex-direction: column;
        }
        
        .unlock-cancel-button,
        .unlock-demo-button,
        .unlock-primary-button {
          width: 100%;
          justify-content: center;
        }

        .unlock-title {
          font-size: 1.25rem;
        }

        .unlock-options {
          grid-template-columns: 1fr;
        }
      }
    `;
    document.head.appendChild(style);
  }

  /**
   * Shows the feature unlock modal
   * @param {Object} options - Configuration options
   * @returns {Promise<string>} - Resolves to 'invitations', 'demo', or 'cancel'
   */
  function showFeatureUnlockModal(options = {}) {
    console.log('FeatureUnlockModal.show() called with options:', options);
    isClosing = false;

    // Remove existing modal if it exists
    const existingModal = document.querySelector('.feature-unlock-modal-overlay');
    if (existingModal) {
      console.log('Removing existing feature unlock modal');
      existingModal.parentNode.removeChild(existingModal);
      isModalInitialized = false;
    }

    // Create modal
    console.log('Creating feature unlock modal HTML and injecting CSS');
    injectCSS();
    const modalHTML = createFeatureUnlockModalHTML(options);
    const modalContainer = document.createElement('div');
    modalContainer.innerHTML = modalHTML;

    // Add to body
    console.log('Adding feature unlock modal to document body');
    document.body.appendChild(modalContainer.firstElementChild);

    modalOverlay = document.querySelector('.feature-unlock-modal-overlay');
    modalContent = document.querySelector('.feature-unlock-modal-content');

    if (!modalOverlay || !modalContent) {
      console.error('Failed to find modal elements after adding to DOM');
      return Promise.reject(new Error('Modal elements not found'));
    }

    console.log('Feature unlock modal elements found, initializing');
    isModalInitialized = true;

    // Show modal with animation
    setTimeout(() => {
      if (isClosing) return;

      console.log('Showing feature unlock modal with animation');
      modalOverlay.style.opacity = '1';
      modalContent.style.opacity = '1';
      modalContent.style.transform = 'scale(1)';
    }, 10);

    return new Promise(resolve => {
      console.log('Feature unlock modal promise created, waiting for user interaction');
      currentPromiseResolve = resolve;
    });
  }

  /**
   * Hides the feature unlock modal
   * @param {string} result - The result of the modal ('invitations', 'demo', or 'cancel')
   */
  function hideFeatureUnlockModal(result) {
    console.log('Feature unlock modal: Hiding with result:', result);
    isClosing = true;

    // Animate closing
    if (modalOverlay) {
      modalOverlay.style.opacity = '0';
      modalContent.style.opacity = '0';
      modalContent.style.transform = 'scale(0.95)';

      // Remove after animation completes
      setTimeout(() => {
        if (modalOverlay && modalOverlay.parentNode) {
          modalOverlay.parentNode.removeChild(modalOverlay);
        }
        isModalInitialized = false;

        // Resolve promise if it exists
        if (currentPromiseResolve) {
          currentPromiseResolve(result);
          currentPromiseResolve = null;
        }
      }, 300);
    }
  }

  /**
   * Handle go to invitations button click
   */
  function goToInvitations() {
    console.log('Feature unlock modal: Go to Invitations button clicked');
    hideFeatureUnlockModal('invitations');
  }

  /**
   * Handle try demo button click
   */
  function tryDemo() {
    console.log('Feature unlock modal: Try Demo button clicked');
    hideFeatureUnlockModal('demo');
  }

  /**
   * Handle cancel button click
   */
  function cancel() {
    console.log('Feature unlock modal: Cancel button clicked');
    hideFeatureUnlockModal('cancel');
  }

  // Public API
  global.FeatureUnlockModal = {
    show: showFeatureUnlockModal,
    hide: hideFeatureUnlockModal,
    goToInvitations: goToInvitations,
    tryDemo: tryDemo,
    cancel: cancel
  };

  console.log('FeatureUnlockModal loaded and available');
})(typeof window !== 'undefined' ? window : global);
