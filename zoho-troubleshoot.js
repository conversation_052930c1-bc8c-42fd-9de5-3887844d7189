/**
 * Zoho CRM Integration Troubleshooting Script
 *
 * Run this script with: node zoho-troubleshoot.js
 *
 * This script tests the Zoho CRM integration and helps diagnose issues
 * with authentication and API access.
 */

require('dotenv').config();
const axios = require('axios');

// Get Zoho credentials from environment variables
const clientId = process.env.ZOHO_CLIENT_ID;
const clientSecret = process.env.ZOHO_CLIENT_SECRET;
const refreshToken = process.env.ZOHO_REFRESH_TOKEN;
const accountsUrl = process.env.ZOHO_ACCOUNTS_URL || 'https://accounts.zoho.eu/oauth/v2/token';
const apiDomain = process.env.ZOHO_API_DOMAIN || 'https://www.zohoapis.eu';

console.log('===== Zoho CRM Integration Troubleshooting =====');
console.log('Testing with the following configuration:');
console.log(`- Client ID: ${clientId ? clientId.substring(0, 8) + '...' : 'NOT SET'}`);
console.log(`- Client Secret: ${clientSecret ? clientSecret.substring(0, 8) + '...' : 'NOT SET'}`);
console.log(`- Refresh Token: ${refreshToken ? refreshToken.substring(0, 8) + '...' : 'NOT SET'}`);
console.log(`- Accounts URL: ${accountsUrl}`);
console.log(`- API Domain: ${apiDomain}`);
console.log('================================================');

async function testGetAccessToken() {
  console.log('\n1. Testing access token retrieval...');

  if (!clientId || !clientSecret || !refreshToken) {
    console.error('❌ ERROR: Missing required Zoho credentials in .env file');
    console.log('Make sure you have set ZOHO_CLIENT_ID, ZOHO_CLIENT_SECRET, and ZOHO_REFRESH_TOKEN');
    return null;
  }

  try {
    console.log(`Requesting access token from: ${accountsUrl}`);

    const response = await axios({
      url: accountsUrl,
      method: 'post',
      params: {
        refresh_token: refreshToken,
        grant_type: 'refresh_token',
        client_id: clientId,
        client_secret: clientSecret,
      },
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    });

    console.log(`Response status: ${response.status}`);
    console.log('Response data:', JSON.stringify(response.data, null, 2));

    if (response.data.error) {
      console.error(`❌ ERROR: ${response.data.error} - ${response.data.error_description || 'No description provided'}`);

      if (response.data.error === 'invalid_client') {
        console.log('\nPOSSIBLE FIX: Your client ID or client secret may be invalid.');
        console.log('1. Double check your client credentials in the Zoho API Console');
        console.log('2. Try regenerating your client secret');
      }
      else if (response.data.error === 'invalid_grant' || response.data.error === 'invalid_code') {
        console.log('\nPOSSIBLE FIX: Your refresh token appears to be invalid or expired.');
        console.log('1. Generate a new grant token in the Zoho API Console at https://api-console.zoho.eu/ (for UK/EU accounts)');
        console.log('2. Exchange it for a new refresh token using the command:');
        console.log(`   curl -X POST "https://accounts.zoho.eu/oauth/v2/token" \\`);
        console.log(`   -d "code=YOUR_GRANT_CODE&grant_type=authorization_code&client_id=${clientId}&client_secret=${clientSecret}&redirect_uri=http://localhost:3000"`);
        console.log('3. Update your .env file with the new refresh token');
        console.log('\nSee ZOHO-TOKEN-RENEWAL.md for detailed instructions.');
      }

      return null;
    }

    if (!response.data.access_token) {
      console.error('❌ ERROR: Access token not found in response');
      return null;
    }

    console.log('✅ Successfully retrieved access token');
    return response.data.access_token;

  } catch (error) {
    console.error('❌ ERROR getting access token:');

    if (error.response) {
      console.error(`Response status: ${error.response.status}`);
      console.error('Response data:', JSON.stringify(error.response.data, null, 2));

      if (error.response.status === 401) {
        console.log('\nPOSSIBLE FIX: Authentication failed. Your credentials may be invalid or expired.');
        console.log('1. Double check your client ID, client secret, and refresh token');
        console.log('2. Try generating a new refresh token following the steps in ZOHO-CRM-SETUP.md');
      }
    } else {
      console.error(`Error message: ${error.message}`);

      if (error.message.includes('ECONNREFUSED') || error.message.includes('ENOTFOUND')) {
        console.log('\nPOSSIBLE FIX: Network error. Check your connection and Zoho API domain.');
        console.log('1. Verify your internet connection');
        console.log('2. Make sure the ZOHO_ACCOUNTS_URL and ZOHO_API_DOMAIN values are correct for UK (EU region)');
        console.log('   - ZOHO_API_DOMAIN should be https://www.zohoapis.eu');
        console.log('   - ZOHO_ACCOUNTS_URL should be https://accounts.zoho.eu/oauth/v2/token');
      }
    }

    return null;
  }
}

async function testApiAccess(accessToken) {
  if (!accessToken) {
    console.log('\n2. Skipping API access test (no access token)');
    return;
  }

  console.log('\n2. Testing API access with access token...');

  try {
    const url = `${apiDomain}/crm/v2/Leads`;
    console.log(`Making request to: ${url}`);

    const response = await axios.get(url, {
      headers: {
        'Authorization': `Zoho-oauthtoken ${accessToken}`
      }
    });

    console.log(`Response status: ${response.status}`);
    console.log('Response contains data:', !!response.data);
    console.log('✅ Successfully accessed Zoho CRM API');

  } catch (error) {
    console.error('❌ ERROR accessing Zoho CRM API:');

    if (error.response) {
      console.error(`Response status: ${error.response.status}`);
      console.error('Response data:', JSON.stringify(error.response.data, null, 2));

      if (error.response.status === 401) {
        console.log('\nPOSSIBLE FIX: API access denied. Check your token and permissions.');
        console.log('1. Make sure your Zoho account has the necessary permissions to access the CRM module');
        console.log('2. Verify that you requested the correct scopes (ZohoCRM.modules.all) when creating your client');
      }
    } else {
      console.error(`Error message: ${error.message}`);
    }
  }
}

async function testAddLead(accessToken) {
  if (!accessToken) {
    console.log('\n3. Skipping lead creation test (no access token)');
    return;
  }

  console.log('\n3. Testing lead creation...');

  const testData = {
    firstname: 'Test',
    lastname: 'User',
    email: `test${Date.now()}@example.com`,
    company: 'Test Company'
  };

  try {
    const url = `${apiDomain}/crm/v2/Leads`;
    console.log(`Making request to: ${url}`);
    console.log('Lead data:', JSON.stringify(testData, null, 2));

    const leadData = {
      data: [{
        Company: testData.company,
        Last_Name: testData.lastname,
        First_Name: testData.firstname,
        Email: testData.email,
        Source: 'Skills Assess Dashboard',
      }],
      trigger: [
        'approval',
        'workflow',
        'blueprint',
      ],
    };

    const response = await axios.post(url, leadData, {
      headers: {
        'Authorization': `Zoho-oauthtoken ${accessToken}`,
        'Content-Type': 'application/json'
      }
    });

    console.log(`Response status: ${response.status}`);
    console.log('Response data:', JSON.stringify(response.data, null, 2));
    console.log('✅ Successfully created test lead in Zoho CRM');

  } catch (error) {
    console.error('❌ ERROR creating test lead:');

    if (error.response) {
      console.error(`Response status: ${error.response.status}`);
      console.error('Response data:', JSON.stringify(error.response.data, null, 2));

      if (error.response.data && error.response.data.code === 'INVALID_DATA') {
        console.log('\nPOSSIBLE FIX: Invalid data format. The field names might be incorrect.');
        console.log('1. Check the field names in your lead data');
        console.log('2. Make sure they match the field names in your Zoho CRM account');
        console.log('3. Some fields may be required in your Zoho CRM configuration');
      }
    } else {
      console.error(`Error message: ${error.message}`);
    }
  }
}

// Run the tests
async function runTests() {
  try {
    const accessToken = await testGetAccessToken();
    await testApiAccess(accessToken);
    await testAddLead(accessToken);

    console.log('\n===== Troubleshooting Summary =====');
    if (accessToken) {
      console.log('✅ Access token retrieval: SUCCESS');
    } else {
      console.log('❌ Access token retrieval: FAILED');
      console.log('This is the critical issue that needs to be fixed first.');
    }
    console.log('=================================');

  } catch (error) {
    console.error('Error running tests:', error.message);
  }
}

runTests();
