# Zoho CRM Token Renewal Guide

This document provides step-by-step instructions for renewing your Zoho CRM tokens when you encounter an "invalid_code" error.

## Process Overview

When the Zoho CRM integration fails with an "invalid_code" error, you need to:
1. Generate a new grant token
2. Exchange it for a refresh token
3. Update your .env file

## Detailed Steps for UK/EU Accounts

### 1. Generate a New Grant Token

1. Log in to the Zoho API Console at **https://api-console.zoho.eu/** (note: use .eu domain for UK accounts)
2. Find your existing client or create a new one if needed:
   - Click on "Self Client" in the left menu
   - Find "SkillsAssess Dashboard" or your previously created client name
   - If you need to create a new client, click "Create New" and follow the setup instructions

3. Generate the grant code:
   - Click on your client name
   - Click the "Generate Code" button
   - Ensure `ZohoCRM.modules.all` is selected in the scopes
   - Click "Generate Code"
   - **COPY THE CODE IMMEDIATELY** - it will only be shown once

### 2. Exchange the Grant Token for a Refresh Token

#### Using Postman:
1. Create a new POST request to `https://accounts.zoho.eu/oauth/v2/token`
2. Set Content-Type header to `application/x-www-form-urlencoded`
3. Add the following in the Body (x-www-form-urlencoded format):
   - `code`: [Your grant code from step 1]
   - `grant_type`: authorization_code
   - `client_id`: 1000.ZX0LG7WWZ6H2UU00CR8W2ESDKKI1VY
   - `client_secret`: 34aa1ab1ffc6618531e1ab38aff5bb705b03b73e5b
   - `redirect_uri`: http://localhost:3000
4. Send the request
5. In the response, find and copy the `refresh_token` value

#### Using cURL:
Run this command in your terminal, replacing `YOUR_GRANT_CODE` with the code you generated:

```bash
curl -X POST "https://accounts.zoho.eu/oauth/v2/token" \
-d "code=**********************************************************************&grant_type=authorization_code&client_id=1000.S8O7LA51MKTFATGKRJ5P0DQZNMQSOK&client_secret=fe4f222e09bbf70ee28c61251b5045ab4665465dfd&redirect_uri=http://localhost:3000"
```

### 3. Update Your .env File

1. Open your `.env` file at `c:\Users\<USER>\assessment-dashboard\.env`
2. Find the line starting with `ZOHO_REFRESH_TOKEN=`
3. Replace the existing token with your new refresh token
4. Save the file

### 4. Test the Integration

1. Run the troubleshooting script again:
   ```
   node zoho-troubleshoot.js
   ```
2. If successful, you should see "✅ Access token retrieval: SUCCESS" in the summary

## Common Issues

### Error: "invalid_client"
- Double-check your client ID and client secret

### Error: "invalid_code" (after using a new grant code)
- Grant codes can only be used once and expire quickly
- Generate a new grant code and try again
- Ensure you're using the correct URL for your region (https://accounts.zoho.eu/oauth/v2/token for UK/EU)

### Error: "invalid_redirect_uri"
- Make sure the redirect URI exactly matches what you registered (http://localhost:3000)

### Note on Production Use

For production use, you might want to use a more appropriate redirect URI, but you must ensure it matches exactly what's registered with your client in the Zoho API Console.
