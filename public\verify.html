<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verify Your Email | Assessment Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/lottie-web@5.7.8/build/player/lottie.min.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-auth.js"></script>
    <!-- Add the local development config script -->
    <script src="localDevelopmentConfig.js"></script>
    <style>
        .verification-container {
            max-width: 500px;
            min-height: 400px;
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        .animation-container {
            width: 150px;
            height: 150px;
        }
        .btn-primary {
            background-color: #1547bb;
            transition: all 0.2s;
        }
        .btn-primary:hover {
            background-color: #0f3d98;
            transform: translateY(-1px);
        }
        .shake {
            animation: shake 0.5s cubic-bezier(.36,.07,.19,.97) both;
        }
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
            20%, 40%, 60%, 80% { transform: translateX(5px); }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen flex items-center justify-center">
    <div class="verification-container bg-white p-8 rounded-2xl my-8">
        <!-- Loading State -->
        <div id="loading-state" class="flex flex-col items-center justify-center">
            <div class="animation-container mb-6" id="loading-animation"></div>
            <h1 class="text-xl font-semibold text-gray-800 mb-2">Verifying Your Email</h1>
            <p class="text-gray-500 text-center">Please wait while we verify your email address...</p>
        </div>
        
        <!-- Success State -->
        <div id="success-state" class="hidden flex flex-col items-center justify-center">
            <div class="animation-container mb-6" id="success-animation"></div>
            <h1 class="text-xl font-semibold text-gray-800 mb-2">Email Verified!</h1>
            <p class="text-gray-500 text-center mb-6">Your email has been successfully verified. You can now continue using the dashboard.</p>
            <a href="index.html" class="btn-primary text-white font-medium py-2 px-6 rounded-lg">
                Continue to Login
            </a>
        </div>
        
        <!-- Error State -->
        <div id="error-state" class="hidden flex flex-col items-center justify-center">
            <div class="animation-container mb-6" id="error-animation"></div>
            <h1 class="text-xl font-semibold text-red-700 mb-2">Verification Failed</h1>
            <p id="error-message" class="text-gray-500 text-center mb-6">
                We couldn't verify your email address. The verification link may have expired or already been used.
            </p>
            <div class="flex flex-col sm:flex-row gap-3">
                <button id="resend-email-btn" class="btn-primary text-white font-medium py-2 px-6 rounded-lg">
                    Resend Verification
                </button>
                <a href="index.html" class="border border-gray-300 text-gray-700 font-medium py-2 px-6 rounded-lg text-center hover:bg-gray-50 transition">
                    Back to Login
                </a>
            </div>
        </div>
    </div>

    <script src="verify.js"></script>
</body>
</html>
