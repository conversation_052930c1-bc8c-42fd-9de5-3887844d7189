<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Skills Gap Analysis Modal Test</title>
    <style>
        body {
            font-family: system-ui, -apple-system, sans-serif;
            margin: 0;
            padding: 2rem;
            background: #f3f4f6;
        }
        
        .test-button {
            background: #1e3a8a;
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 0.5rem;
            font-size: 1rem;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .test-button:hover {
            background: #1e40af;
        }
    </style>
</head>
<body>
    <h1>Skills Gap Analysis Demo</h1>
    <button class="test-button" onclick="showSkillsGapAnalysis()">
        Show Skills Gap Analysis
    </button>

    <script>
        // Ensure script is loaded before allowing clicks
        document.querySelector('.test-button').disabled = true;
        window.addEventListener('load', () => {
            if (typeof showSkillsGapAnalysis === 'function') {
                document.querySelector('.test-button').disabled = false;
            }
        });
    </script>

    <!-- Include the skills gap modal script -->
    <script src="skills-gap-modal.js"></script>
</body>
</html>