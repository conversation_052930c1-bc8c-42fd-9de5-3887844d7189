(function(global) {
  // Add new utility functions at the top
  function isValidEmail(email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return emailRegex.test(email);
  }

  async function createAdminDocument(email, adminData) {
      return firebase.firestore()
          .collection('Admins')
          .doc(email)
          .set({
              ...adminData,
              updatedAt: firebase.firestore.FieldValue.serverTimestamp()
          });
  }

  // Add core email migration functions
  async function handleEmailMigration(currentUser, newEmail, currentPassword) {
      const db = firebase.firestore();

      try {
          if (!isValidEmail(newEmail)) {
              throw new Error('Invalid email format');
          }

          console.log('[Email Migration] Initiating email change:', {
              oldEmail: currentUser.email,
              newEmail
          });

          // Clear any existing pending migrations first
          const response = await fetch('/handle-email-change', {
              method: 'POST',
              headers: {
                  'Content-Type': 'application/json',
                  'Accept': 'application/json'
              },
              body: JSON.stringify({
                  oldEmail: currentUser.email,
                  newEmail,
                  action: 'initiate'
              })
          });

          if (!response.ok) {
              const errorData = await response.json();
              throw new Error(`Server error: ${errorData.error || response.statusText}`);
          }

          const result = await response.json();

          // Create new user account with the same password
          try {
              const newUserCredential = await firebase.auth()
                  .createUserWithEmailAndPassword(newEmail, currentPassword);

              // Send verification email to new account with custom action URL
              const actionCodeSettings = {
                  url: window.location.origin + '/verify.html?email=' + newEmail,
                  handleCodeInApp: true
              };

              await newUserCredential.user.sendEmailVerification(actionCodeSettings);

              console.log('[Email Migration] Created new auth account and sent verification');
          } catch (authError) {
              console.error('[Email Migration] Auth creation error:', authError);
              throw new Error(authError.message || 'Failed to create new account');
          }

          // Create and show the minimalistic confirmation dialog
          const dialogOverlay = document.createElement('div');
          dialogOverlay.className = 'email-confirm-overlay';
          dialogOverlay.innerHTML = `
              <style>
                  .email-confirm-overlay {
                      position: fixed;
                      top: 0;
                      left: 0;
                      width: 100%;
                      height: 100%;
                      background: rgba(0, 0, 0, 0.2);
                      backdrop-filter: blur(3px);
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      z-index: 2000;
                      opacity: 0;
                      transition: opacity 0.3s ease;
                  }
                  .email-confirm-content {
                      background: #fff;
                      border-radius: 0.75rem;
                      max-width: 400px;
                      width: 90%;
                      padding: 1.5rem;
                      box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
                      transform: scale(0.95);
                      opacity: 0;
                      transition: all 0.3s ease;
                  }
                  .email-confirm-title {
                      font-size: 1.125rem;
                      font-weight: 600;
                      margin-bottom: 0.75rem;
                      text-align: center;
                      color: #111827;
                  }
                  .email-confirm-text {
                      font-size: 0.875rem;
                      color: #374151;
                      line-height: 1.4;
                      margin-bottom: 1rem;
                      text-align: center;
                  }
                  .email-confirm-ok-button {
                      display: block;
                      margin: 0 auto;
                      padding: 0.5rem 1rem;
                      border: none;
                      border-radius: 0.5rem;
                      background-color: #3B82F6;
                      color: #fff;
                      font-size: 0.875rem;
                      cursor: pointer;
                      transition: background-color 0.2s;
                  }
                  .email-confirm-ok-button:hover {
                      background-color: #2563EB;
                  }
              </style>
              <div class="email-confirm-content">
                  <h3 class="email-confirm-title">Email Change Initiated</h3>
                  <div class="email-confirm-text">
                      We've updated your email to <strong>${newEmail}</strong> and sent a verification
                      link. Please verify your new email address to complete the change.
                  </div>
                  <button type="button" class="email-confirm-ok-button">
                      Ok, Got It
                  </button>
              </div>
          `;

          document.body.appendChild(dialogOverlay);

          // Animate modal appearance
          setTimeout(() => {
              dialogOverlay.style.opacity = '1';
              const content = dialogOverlay.querySelector('.email-confirm-content');
              content.style.opacity = '1';
              content.style.transform = 'scale(1)';
          }, 10);

          // Handle dialog dismissal
          const dismissButton = dialogOverlay.querySelector('.email-confirm-ok-button');
          dismissButton.addEventListener('click', async () => {
              dialogOverlay.style.opacity = '0';
              const content = dialogOverlay.querySelector('.email-confirm-content');
              content.style.opacity = '0';
              content.style.transform = 'scale(0.95)';
              await new Promise(resolve => setTimeout(resolve, 300)); // Wait for fade out
              dialogOverlay.remove();

              // Sign out the user and reload the page
              await firebase.auth().signOut();
              window.location.reload();
          });

          return {
              success: true,
              message: 'Email change initiated. Please check your new email for verification.',
              ...result
          };

      } catch (error) {
          console.error('[Email Migration] Error:', error);
          throw error;
      }
  }

  async function updateUIWithUserData(user) {
      try {
          showLoadingOverlay('account', 'Loading account details...');
          const adminDoc = await firebase.firestore()
              .collection('Admins')
              .doc(user.email)
              .get();

          if (!adminDoc.exists) {
              throw new Error('User data not found');
          }

          const userData = adminDoc.data();
          console.log('Updating profile form with user data:', userData);

          const firstNameInput = document.getElementById('firstName');
          const lastNameInput = document.getElementById('lastName');
          const emailInput = document.getElementById('email');
          const verificationBadge = document.querySelector('.badge-green');

          if (firstNameInput) firstNameInput.value = userData.firstname || '';
          if (lastNameInput) lastNameInput.value = userData.lastname || '';

          if (emailInput) {
              emailInput.value = user.email;
              emailInput.disabled = false;
          }

          if (verificationBadge) {
              verificationBadge.textContent = user.emailVerified ? 'Verified' : 'Unverified';
              verificationBadge.style.backgroundColor = user.emailVerified ? '#34D399' : '#EF4444';
          }

      } catch (error) {
          console.error('Error updating UI with user data:', error);
          showNotification('Failed to update account information', 'error');
      } finally {
          hideLoadingOverlay();
      }
  }

  // State management
  let isAccountUIInitialized = false;
  let isClosing = false;
  let currentUserData = null;

  async function handleAccountManagement(user) {
      isClosing = false;

      try {
          showLoadingOverlay('account', 'Loading account settings...'); // Add loading overlay
          // Fetch current user data from Firestore
          const adminDoc = await firebase.firestore()
              .collection('Admins')
              .doc(user.email)
              .get();

          if (!adminDoc.exists) {
              throw new Error('User data not found');
          }

          currentUserData = adminDoc.data();

          if (isAccountUIInitialized) {
              await updateUIWithUserData(user);
              showAccountModal();
              hideLoadingOverlay(); // Hide loading overlay
              return;
          }

          const overlay = document.createElement('div');
          overlay.id = 'account-management-overlay';
          overlay.className = 'modal-overlay';
          overlay.innerHTML = `
          <style>
            /* Modern Modal overlay styles */
            .modal-overlay {
              position: fixed;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              background: rgba(18, 28, 65, 0.3);
              backdrop-filter: blur(4px);
              display: flex;
              justify-content: center;
              align-items: center;
              z-index: 1000;
              opacity: 0;
              transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            }

            .modal-content {
              background: white;
              border-radius: 1.5rem;
              padding: 2rem; /* Reduced from 2.5rem for a cleaner feel */
              width: 90%;
              max-width: 34rem;
              max-height: 90vh;
              overflow-y: auto;
              opacity: 0;
              transform: scale(0.95);
              transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
              position: relative;
              box-shadow:
                0 20px 25px -5px rgba(18, 28, 65, 0.15),
                0 10px 10px -5px rgba(18, 28, 65, 0.08);
              font-size: 0.875rem; /* Slightly smaller base font size */
            }

            .modal-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 2rem; /* Reduced from 2.5rem */
            }

            .modal-title {
              font-size: 1.25rem; /* Reduced from 1.5rem for a more minimal look */
              font-weight: 600;
              color: #121c41;
              margin: 0;
            }

            .close-modal-button {
              background: none;
              border: none;
              padding: 0.5rem;
              cursor: pointer;
              color: #6B7280;
              transition: all 0.2s;
              border-radius: 9999px;
            }

            .close-modal-button:hover {
              background-color: #F3F4F6;
              color: #121c41;
            }

            /* Modern form styles */
            .input-field {
              border: 1.5px solid #E5E7EB;
              background-color: #F9FAFB;
              color: #121c41;
              transition: all 0.2s;
              width: 100%;
              padding: 0.65rem 1rem; /* Slightly reduced padding */
              border-radius: 0.75rem;
              font-size: 0.875rem; /* Reduced from 0.95rem */
            }

            .input-field:focus {
              outline: none;
              border-color: #1547bb;
              background-color: white;
              box-shadow: 0 0 0 4px rgba(21, 71, 187, 0.1);
            }

            .badge-green {
              background-color: #34D399;
              color: white;
              padding: 0.25rem 0.75rem;
              border-radius: 9999px;
              font-size: 0.75rem;
              font-weight: 500;
              display: inline-flex;
              align-items: center;
              gap: 0.25rem;
            }

            .badge-green svg {
              width: 14px;
              height: 14px;
            }

            .btn-primary {
              background-color: #1547bb;
              color: white;
              padding: 0.75rem 1.5rem;
              border-radius: 0.75rem;
              font-weight: 500;
              width: 100%;
              border: none;
              cursor: pointer;
              transition: all 0.2s;
              font-size: 0.875rem; /* Reduced for consistency */
            }

            .btn-primary:hover {
              background-color: #121c41;
              transform: translateY(-1px);
            }

            .btn-secondary {
              background-color: #F3F4F6;
              color: #4B5563;
              padding: 0.6rem 1.2rem; /* Slightly reduced padding */
              border-radius: 0.75rem;
              font-weight: 500;
              border: none;
              cursor: pointer;
              transition: all 0.2s;
              display: inline-flex;
              align-items: center;
              gap: 0.5rem;
              font-size: 0.875rem;
            }

            .btn-secondary:hover {
              background-color: #E5E7EB;
              color: #121c41;
            }

            .btn-secondary svg {
              width: 18px;
              height: 18px;
            }

            .form-grid {
              display: grid;
              gap: 1.5rem; /* Reduced from 2rem */
            }

            @media (min-width: 640px) {
              .form-grid-2 {
                grid-template-columns: repeat(2, 1fr);
                gap: 1.2rem; /* Slightly reduced */
              }
            }

            .form-label {
              display: block;
              font-size: 0.8rem; /* Smaller label text */
              font-weight: 500;
              color: #121c41;
              margin-bottom: 0.4rem;
            }

            .password-section {
              overflow: hidden;
              max-height: 0;
              opacity: 0;
              transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
              margin-top: -1rem;
            }

            .password-section.expanded {
              max-height: 300px;
              opacity: 1;
              margin-top: 0;
            }

            .password-toggle-container {
              display: flex;
              justify-content: flex-end;
              margin-bottom: 0.5rem;
            }

            /* Notification styles */
            .account-notification {
              position: fixed;
              bottom: 20px;
              right: 20px;
              padding: 1rem 1.5rem;
              border-radius: 0.75rem;
              color: white;
              opacity: 0;
              transform: translateY(10px);
              transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
              z-index: 1100;
              display: flex;
              align-items: center;
              gap: 0.5rem;
              box-shadow: 0 10px 15px -3px rgba(18, 28, 65, 0.15);
              font-size: 0.875rem;
            }

            .account-notification.success {
              background-color: #1547bb;
            }

            .account-notification.error {
              background-color: #EF4444;
            }

            /* Override for smaller Danger Zone heading/paragraph for a more minimal look */
            .danger-zone h3 {
              font-size: 1rem !important; /* Override .text-lg */
            }
            .danger-zone p {
              font-size: 0.85rem !important; /* Slightly smaller paragraph */
            }
          </style>

          <div class="modal-content">
            <div class="modal-header">
              <h2 class="modal-title">Profile Settings</h2>
              <button id="close-account-modal" class="close-modal-button">
                <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>

            <form class="form-grid">
              <div class="form-grid form-grid-2">
                <div>
                  <label class="form-label" for="firstName">First Name</label>
                  <input
                    type="text"
                    id="firstName"
                    class="input-field"
                  />
                </div>
                <div>
                  <label class="form-label" for="lastName">Last Name</label>
                  <input
                    type="text"
                    id="lastName"
                    class="input-field"
                  />
                </div>
              </div>

              <div>
                <div
                  style="
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 0.5rem;
                  "
                >
                  <label class="form-label" for="email" style="margin: 0;">Email</label>
                  <span class="badge-green">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                    Verified
                  </span>
                </div>
                <input
                  type="email"
                  id="email"
                  value="<EMAIL>"
                  class="input-field"
                />
              </div>

              <!-- Single Current Password Field (for both email & password changes) -->
              <div style="margin-top: 1rem;">
                <label class="form-label" for="currentPassword">Current Password</label>
                <input
                  type="password"
                  id="currentPassword"
                  class="input-field"
                  placeholder="Required if changing your email or password"
                />
              </div>

              <!-- Toggle for New Password Fields -->
              <div class="password-toggle-container">
                <button type="button" id="toggle-password-fields" class="btn-secondary">
                  <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743
                        5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1
                        1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"
                    />
                  </svg>
                  Change Password
                </button>
              </div>

              <div id="passwordSection" class="password-section">
                <div style="margin-bottom: 1.5rem;">
                  <label class="form-label" for="newPassword">New Password</label>
                  <input
                    type="password"
                    id="newPassword"
                    class="input-field"
                  />
                </div>
                <div>
                  <label class="form-label" for="confirmPassword">Confirm New Password</label>
                  <input
                    type="password"
                    id="confirmPassword"
                    class="input-field"
                  />
                </div>
              </div>

              <button type="submit" class="btn-primary">Update Account</button>
            </form>

            <!-- Danger Zone Section -->
            <div class="danger-zone mt-8 border-t border-gray-200 pt-8">
              <h3 class="text-red-600 text-lg font-semibold mb-4">Danger Zone</h3>
              <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                <div class="flex items-start">
                  <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-red-600" viewBox="0 0 20 20" fill="currentColor">
                      <path
                        fill-rule="evenodd"
                        d="M8.257 3.099c.765-1.36
                          2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213
                          2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11
                          13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1
                          1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                        clip-rule="evenodd"
                      />
                    </svg>
                  </div>
                  <div class="ml-3">
                    <h4 class="text-sm font-medium text-red-800">Delete Account</h4>
                    <p class="mt-1 text-sm text-red-700">
                      This action cannot be undone. This will permanently delete your account and remove
                      all associated data.
                    </p>
                    <div class="mt-4">
                      <button
                        id="delete-account-btn"
                        type="button"
                        class="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200"
                      >
                        Delete Account
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        `;

          document.body.appendChild(overlay);
          isAccountUIInitialized = true;

          // Update UI with user data
          await updateUIWithUserData(user);

          // Animate modal appearance with enhanced smooth animation
          // First ensure initial state
          overlay.style.opacity = '0';
          const modalContent = overlay.querySelector('.modal-content');
          modalContent.style.opacity = '0';
          modalContent.style.transform = 'scale(0.95)';

          // Force a reflow to ensure the initial state is applied
          void modalContent.offsetWidth;

          // Then trigger the animation with a slight delay for smoother appearance
          setTimeout(() => {
              if (isClosing) return;

              // Fade in the overlay first
              overlay.style.opacity = '1';

              // Then animate in the content with a slight delay for a more polished effect
              setTimeout(() => {
                  modalContent.style.opacity = '1';
                  modalContent.style.transform = 'scale(1)';
              }, 50);
          }, 10);

          // Initialize event listeners with user context
          initializeEventListeners(overlay, user);

          const closeButton = overlay.querySelector('#close-account-modal');
          if (closeButton) {
              closeButton.addEventListener('click', async () => {
                  await hideAccountModal();
              });
          }

      } catch (error) {
          console.error('Error initializing account management:', error);
          showNotification('Failed to load account settings', 'error');
      } finally {
          hideLoadingOverlay(); // Ensure loading overlay is hidden
      }
  }

  function initializeEventListeners(overlay, user) {
      const form = overlay.querySelector('form');
      const passwordSection = overlay.querySelector('#passwordSection');
      const togglePasswordButton = overlay.querySelector('#toggle-password-fields');

      // Toggle new password fields
      togglePasswordButton.addEventListener('click', () => {
          if (passwordSection.classList.contains('expanded')) {
              // Clear password fields when collapsing
              document.getElementById('newPassword').value = '';
              document.getElementById('confirmPassword').value = '';
              passwordSection.classList.remove('expanded');
              togglePasswordButton.innerHTML = `
                  <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743
                            5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1
                            1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"/>
                  </svg>
                  Change Password
              `;
          } else {
              passwordSection.classList.add('expanded');
              togglePasswordButton.innerHTML = `
                  <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M6 18L18 6M6 6l12 12"/>
                  </svg>
                  Cancel Password Change
              `;
          }
      });

      // Handle form submission
      form.addEventListener('submit', (e) => {
          handleFormSubmit(e, user);
      });

      // Close on Esc key
      document.addEventListener('keydown', async (event) => {
          if (event.key === 'Escape') {
              await hideAccountModal();
          }
      });

      // Add delete account button click handler
      const deleteAccountBtn = overlay.querySelector('#delete-account-btn');
      if (deleteAccountBtn) {
          deleteAccountBtn.addEventListener('click', () => showDeleteConfirmation(user));
      }
  }

  async function handleFormSubmit(e, user) {
      e.preventDefault();

      try {
          showLoadingOverlay('save', 'Saving account changes...');

          const formData = {
              firstName: document.getElementById('firstName').value.trim(),
              lastName: document.getElementById('lastName').value.trim(),
              email: document.getElementById('email').value.trim(),
              currentPassword: document.getElementById('currentPassword').value,
              newPassword: document.getElementById('newPassword').value,
              confirmPassword: document.getElementById('confirmPassword').value
          };

          // Basic validation
          if (!formData.firstName || !formData.lastName) {
              throw new Error('Please fill in all required fields');
          }

          // Prepare Firestore updates
          const adminRef = firebase.firestore().collection('Admins').doc(user.email);
          const updates = [
              adminRef.update({
                  firstname: formData.firstName,
                  lastname: formData.lastName,
                  updatedAt: firebase.firestore.FieldValue.serverTimestamp()
              })
          ];

          const passwordSection = document.getElementById('passwordSection');
          const emailChanged = formData.email !== user.email;
          const passwordChangeRequested = passwordSection.classList.contains('expanded');

          // If either email is changed OR password is changed, current password is required
          if ((emailChanged || passwordChangeRequested) && !formData.currentPassword) {
              showNotification('Please enter your current password', 'error');
              return;
          }

          // Handle password change
          if (passwordChangeRequested) {
              if (!formData.newPassword || !formData.confirmPassword) {
                  showNotification('Please fill in all new password fields', 'error');
                  return;
              }
              if (formData.newPassword.length < 8) {
                  showNotification('New password must be at least 8 characters long', 'error');
                  return;
              }
              if (formData.newPassword !== formData.confirmPassword) {
                  showNotification('New passwords do not match', 'error');
                  return;
              }

              // Reauthenticate using current password
              const credential = firebase.auth.EmailAuthProvider.credential(
                  user.email,
                  formData.currentPassword
              );
              await user.reauthenticateWithCredential(credential);

              // Update the password
              updates.push(user.updatePassword(formData.newPassword));
          }

          // Handle email change using Firebase
          if (emailChanged) {
              const result = await handleEmailMigration(user, formData.email, formData.currentPassword);
              showNotification(result.message, 'success');
              // Do not automatically hide or remove the main modal in this flow
              // (the new minimal modal from handleEmailMigration handles sign-out & reload)
              return;
          }

          // Commit any remaining updates
          await Promise.all(updates);
          showNotification('Account updated successfully!', 'success');
          // Remove automatic modal closing

      } catch (error) {
          console.error('Error updating account:', error);
          showNotification(error.message || 'Failed to update account', 'error');
      } finally {
          hideLoadingOverlay();
      }
  }

  async function completeEmailChange(user) {
      try {
          if (!user.emailVerified) {
              throw new Error('Please verify your new email address first');
          }

          const adminRef = firebase.firestore().collection('Admins').doc(user.email);
          const adminDoc = await adminRef.get();

          if (!adminDoc.exists) {
              throw new Error('User data not found');
          }

          const data = adminDoc.data();
          if (!data.emailChange) {
              return false;
          }

          await migrateUserData(data.emailChange.currentEmail, user.email);
          showNotification('Email change completed successfully!', 'success');
          return true;

      } catch (error) {
          console.error('Error completing email change:', error);
          showNotification(error.message || 'Failed to complete email change', 'error');
          return false;
      }
  }

  async function migrateUserData(oldEmail, newEmail) {
      const db = firebase.firestore();
      const batch = db.batch();

      try {
          // Copy admin document
          const oldAdminDoc = await db.collection('Admins').doc(oldEmail).get();
          const newAdminRef = db.collection('Admins').doc(newEmail);

          if (oldAdminDoc.exists) {
              const oldData = oldAdminDoc.data();
              // Remove emailChange data and mark for deletion
              delete oldData.emailChange;
              batch.set(newAdminRef, {
                  ...oldData,
                  updatedAt: firebase.firestore.FieldValue.serverTimestamp(),
                  migrationCompleted: true
              });

              // Mark old document for deletion
              batch.update(db.collection('Admins').doc(oldEmail), {
                  deletionScheduledAt: firebase.firestore.FieldValue.serverTimestamp(),
                  status: 'pending_deletion',
                  migratedTo: newEmail
              });
          }

          await batch.commit();
          console.log('User data migration completed successfully');
      } catch (error) {
          console.error('Error during user data migration:', error);
          throw error;
      }
  }

  async function hideAccountModal() {
      isClosing = true;
      const overlay = document.getElementById('account-management-overlay');
      if (!overlay) return;

      // Animate closing with a smoother sequence
      const modalContent = overlay.querySelector('.modal-content');
      if (modalContent) {
          // First fade out the content
          modalContent.style.opacity = '0';
          modalContent.style.transform = 'scale(0.95)';

          // Then fade out the overlay with a slight delay
          setTimeout(() => {
              overlay.style.opacity = '0';
          }, 50);
      } else {
          // If no modal content found, just fade out the overlay
          overlay.style.opacity = '0';
      }

      // Wait for animation to complete
      await new Promise(resolve => setTimeout(resolve, 350));

      if (document.body.contains(overlay)) {
          document.body.removeChild(overlay);
          // Clean up the keyboard event listener
          document.removeEventListener('keydown', async (event) => {
              if (event.key === 'Escape') {
                  await hideAccountModal();
              }
          });
      }

      isAccountUIInitialized = false;
  }

  function showNotification(message, type = 'success') {
      const existingNotifications = document.querySelectorAll('.account-notification');
      existingNotifications.forEach(notification => notification.remove());

      const notification = document.createElement('div');
      notification.className = `account-notification ${type}`;

      // Add icon based on notification type
      const icon = type === 'success'
          ? '<svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/></svg>'
          : '<svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/></svg>';

      notification.innerHTML = `${icon}<span>${message}</span>`;
      document.body.appendChild(notification);

      // Trigger fade in
      setTimeout(() => {
          notification.style.opacity = '1';
          notification.style.transform = 'translateY(0)';
      }, 10);

      // Remove after delay
      setTimeout(() => {
          notification.style.opacity = '0';
          notification.style.transform = 'translateY(10px)';
          setTimeout(() => {
              if (document.body.contains(notification)) {
                  notification.remove();
              }
          }, 300);
      }, 3000);
  }

  function showAccountModal() {
      const overlay = document.getElementById('account-management-overlay');
      if (overlay) {
          // First ensure the overlay is visible but transparent
          overlay.style.display = 'flex';
          overlay.style.opacity = '0';

          const modalContent = overlay.querySelector('.modal-content');
          if (modalContent) {
              // Set initial state for content
              modalContent.style.opacity = '0';
              modalContent.style.transform = 'scale(0.95)';

              // Force a reflow to ensure the initial state is applied
              void modalContent.offsetWidth;
          }

          // Then trigger the animation with a slight delay for smoother appearance
          setTimeout(() => {
              // Fade in the overlay first
              overlay.style.opacity = '1';

              if (modalContent) {
                  // Then animate in the content with a slight delay for a more polished effect
                  setTimeout(() => {
                      modalContent.style.opacity = '1';
                      modalContent.style.transform = 'scale(1)';
                  }, 50);
              }
          }, 10);
      }
  }

  async function showDeleteConfirmation(user) {
      // First close the account management modal
      await hideAccountModal();

      // Check if user has an active subscription
      let hasActiveSubscription = false;
      let subscriptionWarning = '';
      try {
          const doc = await firebase.firestore().collection('Admins').doc(user.email).get();
          if (doc.exists) {
              const userData = doc.data();
              hasActiveSubscription = userData.subscriptionActive && userData.subscriptionId;

              if (hasActiveSubscription) {
                  const subscriptionType = userData.subscriptionType || 'current';
                  const endDate = userData.subscriptionEndDate ?
                      new Date(userData.subscriptionEndDate.seconds * 1000).toLocaleDateString() :
                      'the end of the current billing period';

                  subscriptionWarning = `Your ${subscriptionType} subscription will be immediately canceled. Any remaining credits will be lost.`;
              }
          }
      } catch (error) {
          console.error('Error checking subscription status:', error);
      }

      // Construct the warning message
      let message = 'This action cannot be undone. It will permanently delete your account and remove all associated data.';
      if (subscriptionWarning) {
          message += '\n\n' + subscriptionWarning;
      }
      message += '\n\nPlease type DELETE in the field below to confirm.';

      // Create a custom input field for DELETE confirmation
      const customInputField = document.createElement('div');
      customInputField.innerHTML = `
          <div style="margin-top: 16px; margin-bottom: 8px;">
              <input
                  type="text"
                  id="delete-confirmation-input"
                  style="width: 100%; padding: 8px; border: 1px solid #D1D5DB; border-radius: 6px; font-size: 14px; text-align: center;"
                  placeholder="Type DELETE to confirm"
              />
          </div>
      `;

      // Use the warning modal with custom content
      const warningModalOptions = {
          title: 'Delete Account',
          message: message,
          confirmText: 'Delete Account',
          cancelText: 'Cancel',
          icon: 'error',
          confirmButtonStyle: 'danger',
          customContent: customInputField.innerHTML
      };

      // Create and show the modal
      const warningModal = document.createElement('div');
      warningModal.className = 'warning-modal-overlay';
      warningModal.innerHTML = createDeleteConfirmationHTML(warningModalOptions);
      document.body.appendChild(warningModal);

      // Get references to elements
      const modalOverlay = warningModal;
      const modalContent = warningModal.querySelector('.warning-modal-content');
      const confirmInput = warningModal.querySelector('#delete-confirmation-input');
      const confirmButton = warningModal.querySelector('.warning-modal-confirm-button');
      const cancelButton = warningModal.querySelector('.warning-modal-cancel-button');
      const closeButton = warningModal.querySelector('.warning-modal-close');

      // Disable confirm button initially
      confirmButton.disabled = true;
      confirmButton.classList.add('disabled');

      // Show modal with animation
      setTimeout(() => {
          modalOverlay.style.opacity = '1';
          modalContent.style.opacity = '1';
          modalContent.style.transform = 'scale(1)';
      }, 10);

      // Add event listeners
      confirmInput.addEventListener('input', (e) => {
          const isValid = e.target.value === 'DELETE';
          confirmButton.disabled = !isValid;
          if (isValid) {
              confirmButton.classList.remove('disabled');
          } else {
              confirmButton.classList.add('disabled');
          }
      });

      // Close modal function
      const closeModal = () => {
          modalOverlay.style.opacity = '0';
          modalContent.style.opacity = '0';
          modalContent.style.transform = 'scale(0.95)';
          setTimeout(() => modalOverlay.remove(), 300);
      };

      // Add event listeners for closing
      cancelButton.addEventListener('click', closeModal);
      closeButton.addEventListener('click', closeModal);
      modalOverlay.addEventListener('click', (event) => {
          if (event.target === modalOverlay) {
              closeModal();
          }
      });

      // Handle delete confirmation
      confirmButton.addEventListener('click', async () => {
          try {
              closeModal();
              showLoadingOverlay('delete', 'Deleting account...');
              console.log('[Account Deletion] Initiating account deletion for:', user.email);

              const adminDoc = await firebase.firestore()
                  .collection('Admins')
                  .doc(user.email)
                  .get();

              const userCompany = adminDoc.data()?.company;

              // Send deletion request to server
              const response = await fetch('/delete-account', {
                  method: 'POST',
                  headers: {
                      'Content-Type': 'application/json',
                  },
                  body: JSON.stringify({
                      email: user.email,
                      company: userCompany
                  })
              });

              if (!response.ok) {
                  throw new Error('Server deletion failed');
              }

              const result = await response.json();
              console.log('[Account Deletion] Server-side deletion completed');

              // Delete Firebase Authentication user
              console.log('[Account Deletion] Deleting Firebase auth user');
              await user.delete();

              console.log('[Account Deletion] Account deletion completed successfully');
              showNotification('Account deleted successfully. Logging out...', 'success');

              // Sign out from Firebase
              await firebase.auth().signOut();

              // Clear any local storage or session data
              localStorage.clear();
              sessionStorage.clear();

              // Short delay for notification to be visible
              setTimeout(() => {
                  window.location.href = 'index.html';
              }, 1500);

          } catch (error) {
              console.error('[Account Deletion] Error:', error);
              showNotification('Failed to delete account: ' + error.message, 'error');
              hideLoadingOverlay();
          }
      });
  }

  // Helper function to create delete confirmation HTML
  function createDeleteConfirmationHTML(options) {
      const {
          title = 'Warning',
          message = 'Are you sure you want to proceed?',
          confirmText = 'Confirm',
          cancelText = 'Cancel',
          icon = 'warning',
          confirmButtonStyle = 'danger',
          customContent = ''
      } = options;

      // Determine icon SVG
      let iconSvg = '';

      if (icon === 'warning') {
          iconSvg = `
              <svg xmlns="http://www.w3.org/2000/svg" class="warning-modal-icon warning" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
                  <line x1="12" y1="9" x2="12" y2="13"></line>
                  <line x1="12" y1="17" x2="12.01" y2="17"></line>
              </svg>
          `;
      } else if (icon === 'error') {
          iconSvg = `
              <svg xmlns="http://www.w3.org/2000/svg" class="warning-modal-icon error" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <circle cx="12" cy="12" r="10"></circle>
                  <line x1="15" y1="9" x2="9" y2="15"></line>
                  <line x1="9" y1="9" x2="15" y2="15"></line>
              </svg>
          `;
      }

      // Determine confirm button class
      let confirmButtonClass = 'warning-modal-confirm-button';
      if (confirmButtonStyle === 'danger') {
          confirmButtonClass += ' danger';
      }

      return `
          <style>
              .warning-modal-overlay {
                  position: fixed;
                  top: 0;
                  left: 0;
                  right: 0;
                  bottom: 0;
                  background-color: rgba(18, 28, 65, 0.3);
                  backdrop-filter: blur(3px);
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  z-index: 3000;
                  opacity: 0;
                  transition: opacity 0.3s ease;
              }

              .warning-modal-content {
                  background: #fff;
                  border-radius: 0.75rem;
                  width: 90%;
                  max-width: 450px;
                  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
                  transform: scale(0.95);
                  opacity: 0;
                  transition: all 0.3s ease;
                  overflow: hidden;
              }

              .warning-modal-header {
                  display: flex;
                  align-items: center;
                  padding: 1.25rem 1.5rem 0.75rem;
                  position: relative;
              }

              .warning-modal-icon {
                  width: 2rem;
                  height: 2rem;
                  margin-right: 0.75rem;
              }

              .warning-modal-icon.warning {
                  color: #F59E0B;
              }

              .warning-modal-icon.error {
                  color: #EF4444;
              }

              .warning-modal-title {
                  font-size: 1.25rem;
                  font-weight: 600;
                  color: #111827;
                  margin: 0;
                  flex-grow: 1;
              }

              .warning-modal-close {
                  background: none;
                  border: none;
                  color: #6B7280;
                  cursor: pointer;
                  padding: 0.25rem;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  border-radius: 0.375rem;
                  transition: background-color 0.2s, color 0.2s;
                  position: absolute;
                  top: 1rem;
                  right: 1rem;
              }

              .warning-modal-close:hover {
                  background-color: #F3F4F6;
                  color: #374151;
              }

              .warning-modal-body {
                  padding: 0.75rem 1.5rem 1.25rem;
              }

              .warning-modal-message {
                  margin: 0;
                  color: #4B5563;
                  font-size: 1rem;
                  line-height: 1.5;
                  white-space: pre-line;
              }

              .warning-modal-footer {
                  display: flex;
                  justify-content: flex-end;
                  padding: 1rem 1.5rem 1.5rem;
                  gap: 0.75rem;
              }

              .warning-modal-cancel-button {
                  padding: 0.5rem 1rem;
                  background-color: #F3F4F6;
                  color: #374151;
                  border: none;
                  border-radius: 0.375rem;
                  font-weight: 500;
                  font-size: 0.875rem;
                  cursor: pointer;
                  transition: background-color 0.2s;
              }

              .warning-modal-cancel-button:hover {
                  background-color: #E5E7EB;
              }

              .warning-modal-confirm-button {
                  padding: 0.5rem 1rem;
                  border: none;
                  border-radius: 0.375rem;
                  font-weight: 500;
                  font-size: 0.875rem;
                  cursor: pointer;
                  transition: background-color 0.2s;
              }

              .warning-modal-confirm-button.danger {
                  background-color: #EF4444;
                  color: white;
              }

              .warning-modal-confirm-button.danger:hover {
                  background-color: #DC2626;
              }

              .warning-modal-confirm-button.disabled {
                  opacity: 0.5;
                  cursor: not-allowed;
              }

              @media (max-width: 640px) {
                  .warning-modal-content {
                      width: 95%;
                      max-width: none;
                  }

                  .warning-modal-footer {
                      flex-direction: column-reverse;
                  }

                  .warning-modal-cancel-button,
                  .warning-modal-confirm-button {
                      width: 100%;
                      padding: 0.75rem 1rem;
                  }
              }
          </style>
          <div class="warning-modal-content">
              <div class="warning-modal-header">
                  ${iconSvg}
                  <h2 class="warning-modal-title">${title}</h2>
                  <button class="warning-modal-close">
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                      </svg>
                  </button>
              </div>
              <div class="warning-modal-body">
                  <p class="warning-modal-message">${message}</p>
                  ${customContent}
              </div>
              <div class="warning-modal-footer">
                  <button class="warning-modal-cancel-button">${cancelText}</button>
                  <button class="${confirmButtonClass}">${confirmText}</button>
              </div>
          </div>
      `;
  }

  // Public API
  global.handleAccountManagement = handleAccountManagement;
  global.completeEmailChange = completeEmailChange;

})(typeof window !== 'undefined' ? window : global);
