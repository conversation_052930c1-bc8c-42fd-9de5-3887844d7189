let latestAssessments = [];
let currentPage = 1;
let rowsPerPage = 10;
let isInitialized = false;
let filtersApplied = false;
let originalAssessments = [];
let assessmentsSnapshot = null;

// Event listener for DOMContentLoaded
document.addEventListener('DOMContentLoaded', function() {
    initializeAssessments();
});


// Initialize Date Range Picker
async function initializeDateRangePicker() {
    const picker = new Litepicker({
        element: document.getElementById('date-range-picker'),
        singleMode: false,
        format: 'YYYY-MM-DD',
        maxDate: new Date(),
        theme: {
            selected: 'bg-blue-500 text-white',
            button: 'bg-blue-500 text-white hover:bg-blue-600',
            hover: 'bg-blue-100',
        },
        setup: (picker) => {
            picker.on('selected', (startDate, endDate) => {
                applyAllFilters();
            });
        },
    });

    const dateRangeInput = document.getElementById('date-range-picker');
    dateRangeInput.addEventListener('input', function() {
        if (this.value === '') {
            populateTable(latestAssessments);
        }
    });
}

// Debounce function
function debounce(func, delay) {
    let timeoutId;
    return function(...args) {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => func.apply(this, args), delay);
    };
}

// Initialize Rows Per Page Select
function initializeRowsPerPageSelect() {
    const rowsPerPageSelect = document.getElementById('rows-per-page');
    if (!rowsPerPageSelect) {
        console.error('Rows per page select element not found');
        return;
    }
    rowsPerPageSelect.addEventListener('change', function() {
        rowsPerPage = parseInt(this.value);
        currentPage = 1;
        populateTable(latestAssessments);
    });
}

// Initialize Assessments Filter - Completely redesigned modal approach
function initializeAssessmentsFilter() {
    const filterButton = document.getElementById('filter-button');
    const filterModal = document.getElementById('filter-modal');
    const closeFilterModal = document.getElementById('close-filter-modal');
    const applyFiltersButton = document.getElementById('apply-filters');
    const resetFiltersButton = document.getElementById('reset-filters');
    const clearFiltersButton = document.getElementById('clear-filters');

    // Show the filter modal when filter button is clicked
    filterButton.addEventListener('click', () => {
        filterModal.classList.remove('hidden');
        document.body.classList.add('overflow-hidden'); // Prevent background scrolling
    });

    // Hide the modal when close button is clicked
    closeFilterModal.addEventListener('click', () => {
        filterModal.classList.add('hidden');
        document.body.classList.remove('overflow-hidden');
    });

    // Hide modal when clicking outside the modal content
    filterModal.addEventListener('click', (event) => {
        if (event.target === filterModal) {
            filterModal.classList.add('hidden');
            document.body.classList.remove('overflow-hidden');
        }
    });

    // Apply filters when the apply button is clicked
    applyFiltersButton.addEventListener('click', () => {
        applyAllFilters();
        filterModal.classList.add('hidden');
        document.body.classList.remove('overflow-hidden');
    });

    // Reset filters when reset button is clicked
    resetFiltersButton.addEventListener('click', () => {
        resetAllFilters();
    });

    // Clear all filters when clear button is clicked
    clearFiltersButton.addEventListener('click', () => {
        clearFilters();
    });

    // Initialize clear filters button visibility
    updateClearFiltersVisibility();

    // Add listeners for checkbox changes to update filter counts
    const checkboxes = filterModal.querySelectorAll('input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', () => {
            updateFilterButtonStatus();
        });
    });

    // Add listeners for other filter inputs
    document.getElementById('date-range-picker').addEventListener('change', updateFilterButtonStatus);
    document.getElementById('search-input').addEventListener('input', debounce(updateFilterButtonStatus, 300));

    // Initial update of filter button status
    updateFilterButtonStatus();
}

// Update filter button status to show active filters count
function updateFilterButtonStatus() {
    const filterButton = document.getElementById('filter-button');
    const statusCheckboxes = document.querySelectorAll('input[name="status"]:checked');
    const pathwayCheckboxes = document.querySelectorAll('input[name="pathway"]:checked');
    const assessmentTypeCheckboxes = document.querySelectorAll('input[name="assessmentType"]:checked');
    const dateRangeInput = document.getElementById('date-range-picker');
    const searchInput = document.getElementById('search-input');

    // Count all filter types
    const checkboxCount = statusCheckboxes.length + pathwayCheckboxes.length + assessmentTypeCheckboxes.length;
    const dateFilterActive = dateRangeInput.value ? 1 : 0;
    const searchFilterActive = searchInput.value ? 1 : 0;

    const filterCount = checkboxCount + dateFilterActive + searchFilterActive;

    // Update button text to show number of active filters
    const buttonText = filterButton.querySelector('span');
    if (filterCount > 0) {
        buttonText.textContent = `Filter (${filterCount})`;
        filterButton.classList.add('bg-blue-700');
    } else {
        buttonText.textContent = 'Filter';
        filterButton.classList.remove('bg-blue-700');
    }
}

// Reset all filters in the modal without applying
function resetAllFilters() {
    const checkboxes = document.querySelectorAll('#filter-modal input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
    updateFilterButtonStatus();
}

// Apply All Filters
function applyAllFilters() {
    const statusCheckboxes = document.querySelectorAll('input[name="status"]');
    const pathwayCheckboxes = document.querySelectorAll('input[name="pathway"]');
    const assessmentTypeCheckboxes = document.querySelectorAll('input[name="assessmentType"]');
    const dateRangeInput = document.getElementById('date-range-picker');
    const searchInput = document.getElementById('search-input');

    const selectedStatuses = Array.from(statusCheckboxes)
        .filter(checkbox => checkbox.checked)
        .map(checkbox => checkbox.value.toLowerCase());

    const selectedPathways = Array.from(pathwayCheckboxes)
        .filter(checkbox => checkbox.checked)
        .map(checkbox => checkbox.value.toLowerCase());

    const selectedTypes = Array.from(assessmentTypeCheckboxes)
        .filter(checkbox => checkbox.checked)
        .map(checkbox => checkbox.value.toLowerCase());

    const searchQuery = searchInput.value.toLowerCase();

    let filteredAssessments = [...originalAssessments];

    if (selectedStatuses.length > 0) {
        filteredAssessments = filteredAssessments.filter(assessment => {
            if (assessment.digital && assessment.soft && assessment.ai) {
                // All three assessment types available
                const completedCount = [
                    assessment.digital.status === 'completed',
                    assessment.soft.status === 'completed',
                    assessment.ai.status === 'completed'
                ].filter(Boolean).length;
                
                if (completedCount === 3) {
                    return selectedStatuses.includes('completed');
                } else if (completedCount === 0) {
                    return selectedStatuses.includes('started');
                } else {
                    return selectedStatuses.includes('completed') || selectedStatuses.includes('started');
                }
            } else if (assessment.digital && assessment.soft) {
                // Digital and soft skills only
                if (assessment.digital.status === 'completed' && assessment.soft.status === 'completed') {
                    return selectedStatuses.includes('completed');
                } else if (assessment.digital.status === 'pending' && assessment.soft.status === 'pending') {
                    return selectedStatuses.includes('started');
                } else {
                    return selectedStatuses.includes(assessment.digital.status) || selectedStatuses.includes(assessment.soft.status);
                }
            } else if (assessment.digital && assessment.ai) {
                // Digital and AI skills only
                if (assessment.digital.status === 'completed' && assessment.ai.status === 'completed') {
                    return selectedStatuses.includes('completed');
                } else if (assessment.digital.status === 'pending' && assessment.ai.status === 'pending') {
                    return selectedStatuses.includes('started');
                } else {
                    return selectedStatuses.includes(assessment.digital.status) || selectedStatuses.includes(assessment.ai.status);
                }
            } else if (assessment.soft && assessment.ai) {
                // Soft and AI skills only
                if (assessment.soft.status === 'completed' && assessment.ai.status === 'completed') {
                    return selectedStatuses.includes('completed');
                } else if (assessment.soft.status === 'pending' && assessment.ai.status === 'pending') {
                    return selectedStatuses.includes('started');
                } else {
                    return selectedStatuses.includes(assessment.soft.status) || selectedStatuses.includes(assessment.ai.status);
                }
            } else {
                // Single assessment type
                const primaryAssessment = assessment.digital || assessment.soft || assessment.ai;
                if (primaryAssessment) {
                    return selectedStatuses.includes(primaryAssessment.status);
                }
            }
            return false;
        });
    }

    if (selectedPathways.length > 0) {
        filteredAssessments = filteredAssessments.filter(assessment => {
            const digitalPathMatches = assessment.digital && assessment.digital.learningPath &&
                selectedPathways.includes(assessment.digital.learningPath.toLowerCase());
            const softPathMatches = assessment.soft && assessment.soft.learningPath &&
                selectedPathways.includes(assessment.soft.learningPath.toLowerCase());
            const aiPathMatches = assessment.ai && assessment.ai.learningPath &&
                selectedPathways.includes(assessment.ai.learningPath.toLowerCase());
            return digitalPathMatches || softPathMatches || aiPathMatches;
        });
    }

    if (selectedTypes.length > 0) {
        filteredAssessments = filteredAssessments.filter(assessment => {
            const hasDigital = assessment.digital && assessment.digital.status === 'completed';
            const hasSoft = assessment.soft && assessment.soft.status === 'completed';
            const hasAI = assessment.ai && assessment.ai.status === 'completed';

            if (selectedTypes.includes('digital') && selectedTypes.includes('soft') && selectedTypes.includes('ai')) {
                return hasDigital || hasSoft || hasAI;
            } else if (selectedTypes.includes('digital') && selectedTypes.includes('soft')) {
                return hasDigital || hasSoft;
            } else if (selectedTypes.includes('digital') && selectedTypes.includes('ai')) {
                return hasDigital || hasAI;
            } else if (selectedTypes.includes('soft') && selectedTypes.includes('ai')) {
                return hasSoft || hasAI;
            } else if (selectedTypes.includes('digital')) {
                return hasDigital;
            } else if (selectedTypes.includes('soft')) {
                return hasSoft;
            } else if (selectedTypes.includes('ai')) {
                return hasAI;
            }
            return true;
        });
    }

    if (dateRangeInput.value) {
        const [startDate, endDate] = dateRangeInput.value.split(' - ').map(date => new Date(date));
        filteredAssessments = filteredAssessments.filter(assessment => {
            const assessmentDate = new Date(assessment.createdAt);
            return assessmentDate >= startDate && assessmentDate <= endDate;
        });
    }

    if (searchQuery) {
        filteredAssessments = filteredAssessments.filter(assessment =>
            assessment.employee.name.toLowerCase().includes(searchQuery));
    }

    filtersApplied = selectedStatuses.length > 0 ||
                     selectedPathways.length > 0 ||
                     selectedTypes.length > 0 ||
                     dateRangeInput.value !== '' ||
                     searchQuery !== '';

    updateClearFiltersVisibility();
    updateFilterButtonStatus();

    // Track filter usage
    if (filtersApplied) {
        trackMilestone('assessments_filtered', {
            filtersUsed: {
                statuses: selectedStatuses,
                pathways: selectedPathways,
                types: selectedTypes,
                hasDateRange: dateRangeInput.value !== '',
                hasSearch: searchQuery !== ''
            },
            resultCount: filteredAssessments.length
        });
    }

    currentPage = 1;
    latestAssessments = filteredAssessments;
    populateTable(filteredAssessments);
}

// Clear Filters
function clearFilters() {
    // Reset checkboxes in the filter modal
    resetAllFilters();

    // Reset date picker and search
    document.getElementById('date-range-picker').value = '';
    document.getElementById('search-input').value = '';

    // Reset data
    latestAssessments = [...originalAssessments];
    filtersApplied = false;

    // Update UI
    updateClearFiltersVisibility();
    updateFilterButtonStatus();

    // Reset pagination and rerender table
    currentPage = 1;
    populateTable(latestAssessments);
}

// Update Clear Filters Visibility
function updateClearFiltersVisibility() {
    const clearFiltersButton = document.getElementById('clear-filters');
    if (filtersApplied) {
        clearFiltersButton.classList.remove('hidden');
    } else {
        clearFiltersButton.classList.add('hidden');
    }
}

// Update Pagination Info
function updatePaginationInfo(totalRows) {
    const startRowElement = document.getElementById('start-row');
    const endRowElement = document.getElementById('end-row');
    const totalRowsElement = document.getElementById('total-rows');

    const startRow = (currentPage - 1) * rowsPerPage + 1;
    const endRow = Math.min(startRow + rowsPerPage - 1, totalRows);

    startRowElement.textContent = startRow;
    endRowElement.textContent = endRow;
    totalRowsElement.textContent = totalRows;

    const prevPageButton = document.getElementById('prev-page');
    const nextPageButton = document.getElementById('next-page');

    prevPageButton.disabled = currentPage === 1;
    nextPageButton.disabled = endRow >= totalRows;

    const rowsPerPageSelect = document.getElementById('rows-per-page');
    if (rowsPerPageSelect) {
        rowsPerPageSelect.value = rowsPerPage;
    } else {
        console.error('Rows per page select element not found');
    }
}

// Handle Previous Page
function handlePrevPage() {
    if (currentPage > 1) {
        currentPage--;
        populateTable(latestAssessments);
    }
}

// Handle Next Page
function handleNextPage() {
    if ((currentPage * rowsPerPage) < latestAssessments.length) {
        currentPage++;
        populateTable(latestAssessments);
    }
}

// Add Pagination Event Listeners
function addPaginationEventListeners() {
    const prevPageButton = document.getElementById('prev-page');
    const nextPageButton = document.getElementById('next-page');
    prevPageButton.addEventListener('click', handlePrevPage);
    nextPageButton.addEventListener('click', handleNextPage);
}

// Handle Rows Per Page Change
function handleRowsPerPageChange() {
    const rowsPerPageSelect = document.getElementById('rows-per-page');
    rowsPerPageSelect.addEventListener('change', function() {
        rowsPerPage = parseInt(this.value);
        currentPage = 1;
        populateTable(latestAssessments);
    });
}

// Scroll To Table
function scrollToTable() {
    const table = document.querySelector('table');
    table.scrollIntoView({ behavior: 'smooth' });
}

// Set Max Rows
function setMaxRows() {
    const rowsPerPageSelect = document.getElementById('rows-per-page');
    rowsPerPageSelect.value = '100';
    rowsPerPageSelect.dispatchEvent(new Event('change'));
}

// Apply Status Filter
function applyStatusFilter(status) {
    const statusCheckboxes = document.querySelectorAll('input[name="status"]');
    statusCheckboxes.forEach(checkbox => {
        checkbox.checked = checkbox.value.toLowerCase() === status.toLowerCase();
    });
    applyAllFilters();
}

// Update Assessment Data
async function updateAssessmentData(userCompany) {
    try {
        const companyRef = db.collection('companies').doc(userCompany);
        const userSnapshot = await companyRef.collection('users').get();

        const totalEmployees = userSnapshot.docs.length;
        let completedCount = 0;
        let abortedCount = 0;
        let pendingCount = 0;

        userSnapshot.docs.forEach(doc => {
            const userData = doc.data();
            if (userData.status === 'completed') {
                completedCount++;
            } else if (userData.status === 'aborted') {
                abortedCount++;
            } else if (userData.status === 'started') {
                pendingCount++;
            }
        });

        updateCardData('total-employees', totalEmployees);
        updateCardData('completed', completedCount);
        updateCardData('aborted', abortedCount);
        updateCardData('pending', pendingCount);
    } catch (error) {
        console.error('Error updating assessment data:', error);
    }
}

// Update Card Data
function updateCardData(cardId, count) {
    const cardElement = document.getElementById(cardId);
    if (!cardElement) return;

    const countElement = cardElement.querySelector('.text-4xl');
    const emptyStateElement = cardElement.querySelector('.card-empty');
    const contentElement = cardElement.querySelector('.card-content');
    const skeletonElement = cardElement.querySelector('.skeleton-card');

    // Ensure skeleton is hidden first
    if (skeletonElement) {
        skeletonElement.style.display = 'none';
    }

    if (count === 0) {
        // Hide content, show empty state
        if (contentElement) contentElement.style.display = 'none';

        // Fade in the empty state
        if (emptyStateElement) {
            emptyStateElement.style.opacity = '0';
            emptyStateElement.style.display = 'block';
            setTimeout(() => {
                emptyStateElement.style.opacity = '1';
            }, 10);
        }
    } else {
        // Hide empty state, show content
        if (emptyStateElement) emptyStateElement.style.display = 'none';

        // Fade in the content with counter animation
        if (contentElement) {
            contentElement.style.opacity = '0';
            contentElement.style.display = 'block';
            setTimeout(() => {
                contentElement.style.opacity = '1';
                // Animate the counter
                if (countElement) {
                    animateCounter(countElement, 0, count);
                }
            }, 10);
        }
    }

    // Add click event listener
    cardElement.addEventListener('click', () => {
        scrollToTable();

        switch(cardId) {
            case 'total-employees':
                clearFilters();
                setMaxRows();
                break;
            case 'completed':
                applyStatusFilter('completed');
                break;
            case 'aborted':
                applyStatusFilter('aborted');
                break;
            case 'pending':
                applyStatusFilter('started');
                break;
        }
    });
}

// Counter animation function
function animateCounter(element, start, end) {
    const duration = 1000; // milliseconds
    const frameDuration = 1000 / 60; // 60fps
    const totalFrames = Math.round(duration / frameDuration);
    let frame = 0;

    const animate = () => {
        frame++;
        const progress = frame / totalFrames;
        const currentValue = Math.round(start + (end - start) * progress);

        element.textContent = currentValue;

        if (frame < totalFrames) {
            requestAnimationFrame(animate);
        } else {
            element.textContent = end;
        }
    };

    requestAnimationFrame(animate);
}

// Populate Table
function populateTable(data) {
    const tableBody = document.querySelector('table tbody');
    const actionsHeader = document.getElementById('actions-header');
    if (tableBody) {
        tableBody.innerHTML = '';

        const startIndex = (currentPage - 1) * rowsPerPage;
        const endIndex = startIndex + rowsPerPage;
        const paginatedData = data.slice(startIndex, endIndex);

        let hasAbortedAssessments = false;

        if (paginatedData.length === 0) {
            const noResultsRow = document.createElement('tr');
            noResultsRow.classList.add('border-t', 'border-gray-200', 'dark:border-gray-700', 'animate-fade-in');
            noResultsRow.innerHTML = `<td colspan="5" class="px-4 py-2 text-center">No matching assessments found.</td>`;
            tableBody.appendChild(noResultsRow);
        } else {
            paginatedData.forEach((item, index) => {
                const row = document.createElement('tr');
                row.classList.add(
                    'border-t',
                    'border-gray-200',
                    'dark:border-gray-700',
                    'hover:bg-gray-50',
                    'dark:hover:bg-gray-800',
                    'cursor-pointer',
                    'transition-colors',
                    'table-row-hover',
                    'table-row'
                );

                // Set animation delay based on index
                row.style.animationDelay = `${0.1 + (index * 0.05)}s`;

                let statusColor;
                row.item = item;

                // Store data attributes for handleRowClick and skills gap analysis
                row.setAttribute('data-email', item.employee.email);
                row.setAttribute('data-company', userCompany);
                row.setAttribute('data-employee-name', item.employee.name);
                row.setAttribute('data-employee-position', item.employee.position);

                // Generate assessment type display
                const hasDigital = item.digital && item.digital.status === 'completed';
                const hasSoft = item.soft && item.soft.status === 'completed';
                const hasAI = item.ai && item.ai.status === 'completed';
                let assessmentTypeDisplay = '';

                if (hasDigital && hasSoft && hasAI) {
                    assessmentTypeDisplay = `<span class="bg-purple-100 text-purple-600 dark:bg-purple-900/20 dark:text-purple-400 px-2 py-1 text-xs rounded-full">Digital, Soft & AI Skills</span>`;
                    statusColor = 'bg-purple-100 text-purple-600 dark:bg-purple-900/20 dark:text-purple-400';
                } else if (hasDigital && hasSoft) {
                    assessmentTypeDisplay = `<span class="bg-green-100 text-green-600 dark:bg-green-900/20 dark:text-green-400 px-2 py-1 text-xs rounded-full">Digital & Soft Skills</span>`;
                    statusColor = 'bg-green-100 text-green-600 dark:bg-green-900/20 dark:text-green-400';
                } else if (hasDigital && hasAI) {
                    assessmentTypeDisplay = `<span class="bg-teal-100 text-teal-600 dark:bg-teal-900/20 dark:text-teal-400 px-2 py-1 text-xs rounded-full">Digital & AI Skills</span>`;
                    statusColor = 'bg-teal-100 text-teal-600 dark:bg-teal-900/20 dark:text-teal-400';
                } else if (hasSoft && hasAI) {
                    assessmentTypeDisplay = `<span class="bg-indigo-100 text-indigo-600 dark:bg-indigo-900/20 dark:text-indigo-400 px-2 py-1 text-xs rounded-full">Soft & AI Skills</span>`;
                    statusColor = 'bg-indigo-100 text-indigo-600 dark:bg-indigo-900/20 dark:text-indigo-400';
                } else if (hasDigital) {
                    assessmentTypeDisplay = `<span class="bg-blue-100 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400 px-2 py-1 text-xs rounded-full">Digital Skills</span>`;
                    statusColor = 'bg-blue-100 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400';
                } else if (hasSoft) {
                    assessmentTypeDisplay = `<span class="bg-purple-100 text-purple-600 dark:bg-purple-900/20 dark:text-purple-400 px-2 py-1 text-xs rounded-full">Soft Skills</span>`;
                    statusColor = 'bg-purple-100 text-purple-600 dark:bg-purple-900/20 dark:text-purple-400';
                } else if (hasAI) {
                    assessmentTypeDisplay = `<span class="bg-orange-100 text-orange-600 dark:bg-orange-900/20 dark:text-orange-400 px-2 py-1 text-xs rounded-full">AI Skills</span>`;
                    statusColor = 'bg-orange-100 text-orange-600 dark:bg-orange-900/20 dark:text-orange-400';
                } else {
                    assessmentTypeDisplay = `<span class="bg-gray-100 text-gray-600 dark:bg-gray-900/20 dark:text-gray-400 px-2 py-1 text-xs rounded-full">Pending</span>`;
                    statusColor = 'bg-gray-100 text-gray-600 dark:bg-gray-900/20 dark:text-gray-400';
                }

                const createdAtFormatted = item.createdAt ? (item.createdAt instanceof Date ? item.createdAt.toISOString().split('T')[0] : item.createdAt.split('T')[0]) : '';

                let rowHTML = `
                <td class="px-4 py-2 flex items-center gap-3 w-1/4">
                    <img src="${item.employee.img}" alt="${item.employee.name}" class="w-8 h-8 rounded-full">
                    <div>
                        <p class="font-medium text-sm">${item.employee.name}</p>
                        <p class="text-xs text-gray-500 dark:text-gray-400">${item.employee.position}</p>
                    </div>
                </td>
                <td class="px-4 py-2 w-1/5">${assessmentTypeDisplay}</td>
                <td class="px-4 py-2 text-center w-1/12">
                    ${(hasDigital || hasSoft || hasAI) ? `
                        <button class="inline-flex items-center justify-center p-1.5 rounded-lg text-blue-600 hover:text-blue-800 hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
                                title="View Skills Gap Analysis"
                                onclick="handleSkillsGapClick(event, this)"
                                data-email="${item.employee.email}"
                                data-company="${userCompany}"
                                data-has-digital="${hasDigital}"
                                data-has-soft="${hasSoft}"
                                data-has-ai="${hasAI}"
                                data-learning-path="${hasAI ? item.ai.learningPath : (hasDigital ? item.digital.learningPath : (hasSoft ? item.soft.learningPath : ''))}">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-bar-chart-3">
                                <path d="M3 3v18h18"/>
                                <path d="M18 17V9"/>
                                <path d="M13 17V5"/>
                                <path d="M8 17v-3"/>
                            </svg>
                        </button>
                    ` : ''}
                </td>
                <td class="px-4 py-2 w-1/3">
                    <div class="flex flex-col gap-1">
                        ${hasDigital ? `
                            <div class="flex items-center gap-1">
                                <span class="inline-block w-2 h-2 bg-blue-500 rounded-full"></span>
                                <span class="text-xs">Digital: ${item.digital.learningPath}</span>
                            </div>
                        ` : ''}
                        ${hasSoft ? `
                            <div class="flex items-center gap-1">
                                <span class="inline-block w-2 h-2 bg-purple-500 rounded-full"></span>
                                <span class="text-xs">Soft: ${item.soft.learningPath}</span>
                            </div>
                        ` : ''}
                        ${hasAI ? `
                            <div class="flex items-center gap-1">
                                <span class="inline-block w-2 h-2 bg-green-500 rounded-full"></span>
                                <span class="text-xs">AI: ${item.ai.learningPath}</span>
                            </div>
                        ` : ''}
                        ${!hasDigital && !hasSoft && !hasAI ? `<span class="text-xs text-gray-500">No assessments completed</span>` : ''}
                    </div>
                </td>
                <td class="px-4 py-2 text-xs w-1/6">${createdAtFormatted}</td>`;

                // Check if any assessment was aborted
                if ((item.digital && item.digital.status === 'aborted') || (item.soft && item.soft.status === 'aborted')) {
                    hasAbortedAssessments = true;
                    if (item.reminded) {
                        rowHTML += `
                        <td class="px-4 py-2">
                            <img src="checked.png" alt="Reminder Sent"
                                 aria-label="Reminder sent to ${item.employee.name}"
                                 class="w-6 h-6"
                                 title="Reminder Sent">
                        </td>`;
                    } else {
                        rowHTML += `
                        <td class="px-4 py-2">
                            <img src="mail.png" alt="Send Reminder"
                                 aria-label="Send reminder to ${item.employee.name}"
                                 class="send-reminder-icon w-6 h-6 cursor-pointer transition duration-300 ease-in-out transform hover:scale-110"
                                 title="Send Reminder"
                                 data-user-email="${item.employee.email}"
                                 data-user-name="${item.employee.name}">
                        </td>`;
                    }
                } else {
                    rowHTML += '<td></td>';
                }

                row.innerHTML = rowHTML;
                tableBody.appendChild(row);
            });
        }

        actionsHeader.classList.toggle('hidden', !hasAbortedAssessments);
        const paginationRow = document.querySelector('table tfoot tr td');
        paginationRow.setAttribute('colspan', hasAbortedAssessments ? '6' : '5');

        updatePaginationInfo(data.length);
        addEventListenersToSendReminderIcons();
    } else {
        console.error("Table body element not found.");
    }
}


// Initialize Assessments
async function initializeAssessments(userCompany) {
    try {
        isInitialized = true;
        initializeAssessmentsFilter();
        handleRowsPerPageChange();
        addPaginationEventListeners();
        await initializeDateRangePicker();
        addEmailEventListeners();

        // Track assessments page access
        trackMilestone('assessments_page_viewed', {
            userCompany: userCompany
        });

        // Ensure we use the right company name when in demo mode
        if (window.isDemoMode) {
            userCompany = 'Barefoot eLearning';
            console.log('Assessments page using demo company:', userCompany);
        }

        // Store the company for other functions to use
        window.userCompany = userCompany;
        console.log('Assessments page initialized with company:', userCompany);

        const loadingOverlay = document.getElementById('loading-overlay');
        if (loadingOverlay) loadingOverlay.style.display = 'flex';

        // Properly set initial display states
        document.querySelectorAll('.card-empty').forEach(content => {
            content.style.display = 'none';
        });

        document.querySelectorAll('.card-content').forEach(content => {
            content.style.display = 'none';
        });

        document.querySelectorAll('.skeleton-card').forEach(card => {
            card.style.display = 'block';
        });

        // Setup real-time listener
        const companyRef = db.collection('companies').doc(userCompany);
        if (assessmentsSnapshot) {
            assessmentsSnapshot(); // Unsubscribe from previous listener
        }

        assessmentsSnapshot = companyRef.collection('users').onSnapshot(async (snapshot) => {
            if (snapshot.docChanges().length > 0) {
                console.log('Assessment data changes detected');

                // Clear cache
                sessionStorage.removeItem('assessmentsData');
                sessionStorage.removeItem('assessmentsLastFetchTime');

                // Fetch and update data
                const assessments = await fetchAssessmentData(userCompany);
                originalAssessments = [...assessments];
                latestAssessments = assessments;

                // Update UI
                populateTable(latestAssessments);
                await updateAssessmentData(userCompany);

                // Reset filters if applied
                if (filtersApplied) {
                    applyAllFilters();
                }
            }
        }, (error) => {
            console.error('Error in assessments snapshot:', error);
        });

        const initialData = await fetchAssessmentData(userCompany);
        originalAssessments = [...initialData];
        latestAssessments = initialData;

        populateTable(latestAssessments);
        await updateAssessmentData(userCompany);

        const searchInput = document.getElementById('search-input');
        searchInput.addEventListener('input', debounce(() => {
            applyAllFilters();
            updateFilterButtonStatus();
        }, 300));

        // Hide loaders with a smooth transition
        if (loadingOverlay) loadingOverlay.style.display = 'none';

        document.querySelectorAll('.skeleton-row').forEach(row => {
            row.classList.add('skeleton-fade-out');
            setTimeout(() => {
                row.style.display = 'none';
            }, 300);
        });

        document.querySelectorAll('.skeleton-card').forEach(card => {
            card.classList.add('skeleton-fade-out');
            setTimeout(() => {
                card.style.display = 'none';
            }, 300);
        });

    } catch (error) {
        console.error('Error initializing assessments:', error);
        const loadingOverlay = document.getElementById('loading-overlay');
        if (loadingOverlay) loadingOverlay.style.display = 'none';
    }
}

// Add cleanup function for snapshot listener
function cleanupAssessmentsListener() {
    if (assessmentsSnapshot) {
        assessmentsSnapshot();
        assessmentsSnapshot = null;
    }
}

// Generate Summary Text
function generateSummaryText(summary) {
    if (!summary || !summary.sectionScores) {
        return "No assessment data available.";
    }

    const sections = ['Essentials', 'Intermediate', 'Advanced', 'Champions'];
    const passThreshold = 70;

    let lastPassedSection = null;
    let recommendedPath = null;
    const sectionTexts = [];

    for (const section of sections) {
        if (summary.sectionScores[section] > 0) {
            const score = summary.sectionScores[section];
            const totalQuestions = summary.questionsPerSection[section];
            const percentage = ((score / totalQuestions) * 100).toFixed(0);
            sectionTexts.push(`${section} ${percentage}% (${score}/${totalQuestions})`);

            if (parseInt(percentage) >= passThreshold) {
                lastPassedSection = section;
            } else {
                recommendedPath = section;
                break;
            }
        } else {
            if (!recommendedPath) {
                recommendedPath = section;
            }
            break;
        }
    }

    if (sectionTexts.length === 0) {
        return "Assessment not started. Begin with Essentials.";
    }

    let summaryText = `Completed: ${sectionTexts.join(', ')}. `;

    if (recommendedPath) {
        summaryText += `Focus: Upskill in ${recommendedPath}.`;
        if (recommendedPath === 'Essentials') {
            summaryText += " Other Sections Not Yet Accessible.";
        }
    } else {
        summaryText += "All levels passed. Consider advanced specialization.";
    }

    return summaryText;
}

// Open Email Modal
function openEmailModal(userEmail, userName) {
    const modal = document.getElementById('email-modal');
    const toField = document.getElementById('email-to');
    const subjectField = document.getElementById('email-subject');
    const bodyField = document.getElementById('email-body');

    toField.value = userEmail;
    subjectField.value = 'Follow up: Complete Your Assessment';
    bodyField.value = `Hi ${userName},

We noticed you started your assessment, but haven't quite finished it yet. No worries!  Just wanted to send a friendly reminder to complete it whenever you have a chance.

If you have any questions or need assistance, please don't hesitate to reach out.

Best regards,
Your Assessment Team`;

    modal.classList.remove('hidden');
}

// Add Email Event Listeners
function addEmailEventListeners() {
    const modal = document.getElementById('email-modal');
    const closeModalButton = document.getElementById('close-modal');
    const sendEmailButton = document.getElementById('send-email');
    const loadingOverlay = document.createElement('div');
    loadingOverlay.id = 'email-loading-overlay';
    loadingOverlay.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center hidden';
    loadingOverlay.innerHTML = '<div class="text-white">Sending email...</div>';
    document.body.appendChild(loadingOverlay);

    closeModalButton.addEventListener('click', function() {
        modal.classList.add('hidden');
    });

    sendEmailButton.addEventListener('click', async function() {
        if (sendEmailButton.disabled) {
            return;
        }

        sendEmailButton.disabled = true;

        const to = document.getElementById('email-to').value;
        const subject = document.getElementById('email-subject').value;
        const body = document.getElementById('email-body').value;

        loadingOverlay.classList.remove('hidden');

        try {
            const response = await fetch(`${API_URL}/send-email`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ to, subject, body }),
            });

            if (response.ok) {
                const userDoc = await db.collection('companies').doc(userCompany).collection('users').where('userEmail', '==', to).get();
                if (!userDoc.empty) {
                    await userDoc.docs[0].ref.update({ reminded: true });
                }


                showNotification('Email sent successfully!', 'success');
                modal.classList.add('hidden');

                const emailIcon = document.querySelector(`[data-user-email="${to}"]`);
                if (emailIcon) {
                    emailIcon.src = 'checked.png';
                    emailIcon.title = 'Reminder Sent';
                    emailIcon.classList.remove('cursor-pointer', 'hover:scale-110');
                    emailIcon.style.pointerEvents = 'none';
                }
            } else {
                throw new Error('Failed to send email');
            }
        } catch (error) {
            console.error('Error:', error);
            alert('Failed to send email. Please try again.');
        } finally {
            loadingOverlay.classList.add('hidden');
            sendEmailButton.disabled = false;
        }
    });
}

// Add Event Listeners to Send Reminder Icons
function addEventListenersToSendReminderIcons() {
    const reminderIcons = document.querySelectorAll('.send-reminder-icon');
    reminderIcons.forEach(icon => {
        if (icon.src.includes('mail.png')) {
            icon.addEventListener('click', function(e) {
                e.preventDefault();
                const userEmail = this.getAttribute('data-user-email');
                const userName = this.getAttribute('data-user-name');
                if (userEmail && userName) {
                    openEmailModal(userEmail, userName);
                } else {
                    console.error('User email or name not found');
                }
            });
        }
    });
}

function showNotification(message, type = 'success') {
    const notificationContainer = document.createElement('div');
    notificationContainer.classList.add('fixed', 'top-4', 'right-4', 'z-50', 'p-4', 'rounded-md', 'shadow-md', 'max-w-md', 'transition-opacity', 'duration-300');

    if (type === 'success') {
        notificationContainer.classList.add('bg-green-100', 'border', 'border-green-400', 'text-green-700');
    } else {
        notificationContainer.classList.add('bg-red-100', 'border', 'border-red-400', 'text-red-700');
    }

    notificationContainer.textContent = message;

    document.body.appendChild(notificationContainer);

    setTimeout(() => {
        notificationContainer.classList.add('opacity-100');
    }, 10);

    setTimeout(() => {
        notificationContainer.classList.remove('opacity-100');
        notificationContainer.classList.add('opacity-0');
        setTimeout(() => {
            notificationContainer.remove();
        }, 300);
    }, 4700);
}

async function handleSkillsGapClick(event, button) {
    event.preventDefault();
    event.stopPropagation();

    // Add loading class to the button
    button.classList.add('loading-border');

    // Log the current state for debugging
    console.log('Skills gap click - Current state:', {
        isDemoMode: window.isDemoMode,
        userCompany: window.userCompany,
        originalUserCompany: window.originalUserCompany
    });

    const email = button.getAttribute('data-email');

    // Determine which company to use
    let company;
    if (window.isDemoMode === true) {
        company = 'Barefoot eLearning';
        console.log('Using demo company: Barefoot eLearning');
    } else {
        company = button.getAttribute('data-company') || window.userCompany || window.originalUserCompany;
        console.log('Using user company:', company);
    }

    if (!email || !company) {
        console.error('Missing email or company data');
        showNotification('Skills gap analysis still pending for this user, check again later', 'info');
        // Remove loading class if there's an error
        button.classList.remove('loading-border');
        return;
    }

    try {
        showLoadingOverlay();

        // First, fetch the user document to get the last assessment ID
        const userRef = db.collection('companies')
                         .doc(company)
                         .collection('users')
                         .doc(email);

        const userDoc = await userRef.get();
        if (!userDoc.exists) {
            throw new Error('User not found');
        }

        const userData = userDoc.data();

        // Get digital skills assessment data (original)
        const digitalSkillsData = await fetchDigitalSkillsData(userRef, userData);

        // Get soft skills assessment data
        const softSkillsData = await fetchSoftSkillsData(userRef, userData);

        // Get AI skills assessment data
        const aiSkillsData = await fetchAISkillsData(userRef, userData);

        // Get employee details from data attributes on the row or from userData
        const row = button.closest('tr');
        const employeeName = row.getAttribute('data-employee-name') ||
                             (userData.firstName && userData.lastName ?
                             `${userData.firstName} ${userData.lastName}` : 'Unknown');
        const employeePosition = row.getAttribute('data-employee-position') ||
                                userData.userRole || 'Unknown Position';

        // Extract specific learning paths for each type
        let digitalPath = null;
        let softSkillsPath = null;
        let aiSkillsPath = null;

        if (digitalSkillsData && digitalSkillsData.report) {
            digitalPath = digitalSkillsData.report.learningPath;
            console.log('Digital Skills Learning Path:', digitalPath);
        }

        if (softSkillsData && softSkillsData.report) {
            softSkillsPath = softSkillsData.report.learningPath;
            console.log('Soft Skills Learning Path:', softSkillsPath);
        }

        if (aiSkillsData && aiSkillsData.report) {
            aiSkillsPath = aiSkillsData.report.learningPath;
            console.log('AI Skills Learning Path:', aiSkillsPath);
        }

        // Create a combined data structure with all analysis types
        const combinedData = {
            metadata: {
                userCompany: company,
                userId: email,
                employeeName: employeeName,
                role: employeePosition,
                isEnrollable: !(userData.enrollmentStatus === 'enrolled' || userData.enrollmentStatus === 'processing'),
                availableAnalysisTypes: [],
                // Store all paths separately in metadata
                pathsByType: {
                    digitalSkills: digitalPath,
                    softSkills: softSkillsPath,
                    aiSkills: aiSkillsPath
                }
            }
        };

        // Decide which path to use as the default currentPath
        // AI skills take precedence, then digital, then soft skills
        combinedData.metadata.currentPath = aiSkillsPath || digitalPath || softSkillsPath;
        console.log('Default currentPath for skills gap modal:', combinedData.metadata.currentPath);

        // Only add data structures that exist
        if (digitalSkillsData && digitalSkillsData.report && digitalSkillsData.report.competencyAnalysis) {
            // Include the specific learning path in each analysis type data
            digitalSkillsData.report.analysisType = 'digitalSkills';
            combinedData.digitalSkills = digitalSkillsData;
            combinedData.metadata.availableAnalysisTypes.push('digitalSkills');
        }

        if (softSkillsData && softSkillsData.report && softSkillsData.report.competencyAnalysis) {
            // Include the specific learning path in each analysis type data
            softSkillsData.report.analysisType = 'softSkills';
            combinedData.softSkills = softSkillsData;
            combinedData.metadata.availableAnalysisTypes.push('softSkills');
        }

        if (aiSkillsData && aiSkillsData.report && aiSkillsData.report.competencyAnalysis) {
            // Include the specific learning path in each analysis type data
            aiSkillsData.report.analysisType = 'aiSkills';
            combinedData.aiSkills = aiSkillsData;
            combinedData.metadata.availableAnalysisTypes.push('aiSkills');
        }

        // Only proceed if at least one analysis type is available
        if (combinedData.metadata.availableAnalysisTypes.length === 0) {
            throw new Error('No skills analysis data available for this user');
        }

        console.log('Passing learning paths to skills gap modal:', combinedData.metadata.pathsByType);
        await window.showSkillsGapAnalysis(combinedData);

        // Track skills gap analysis milestone
        trackMilestone('skills_gap_analysis_viewed', {
            employeeEmail: email,
            employeeName: employeeName,
            hasDigitalSkills: !!digitalSkillsData,
            hasSoftSkills: !!softSkillsData,
            hasAISkills: !!aiSkillsData,
            availableAnalysisTypes: combinedData.metadata.availableAnalysisTypes
        });

        // Track using enhanced tracker
        if (window.UserJourneyTracker) {
            window.UserJourneyTracker.trackSkillsGapAnalysis('assessments', {
                employeeEmail: email,
                employeeName: employeeName,
                hasDigitalSkills: !!digitalSkillsData,
                hasSoftSkills: !!softSkillsData,
                hasAISkills: !!aiSkillsData,
                hasSoftSkills: !!softSkillsData,
                availableAnalysisTypes: combinedData.metadata.availableAnalysisTypes
            });
        }

    } catch (error) {
        console.error('Error showing skills gap analysis:', error);
        showNotification('Skills gap analysis still pending for this user, check again later', 'info');
    } finally {
        hideLoadingOverlay();
        // Remove loading class when operation completes (success or error)
        button.classList.remove('loading-border');
    }
}

// Helper function to fetch digital skills data
async function fetchDigitalSkillsData(userRef, userData) {
    try {
        const lastAssessmentId = userData.lastAssessmentId;
        if (!lastAssessmentId) return null;

        // Fetch the latest assessment summary first
        const summarySnapshot = await userRef
            .collection('assessmentSummaries')
            .orderBy('timestamp', 'desc')
            .limit(1)
            .get();

        // Get currentSection from the summary if available
        let currentSection = null;
        let learningPath = null;

        if (!summarySnapshot.empty) {
            const summaryData = summarySnapshot.docs[0].data();
            currentSection = summaryData.currentSection;
            learningPath = mapSectionToLearningPath(currentSection);
            console.log('Digital Skills - Current Section:', currentSection, 'Mapped Learning Path:', learningPath);
        }

        // Always get the assessment data
        const assessmentDoc = await userRef
            .collection('assessmentResults')
            .doc(lastAssessmentId)
            .get();

        if (!assessmentDoc.exists) return null;

        const assessmentData = assessmentDoc.data();

        // If we couldn't determine the learning path from summary, fall back to other sources
        if (!learningPath) {
            if (assessmentData.section) {
                learningPath = assessmentData.section;
                console.log('Digital Skills - Fallback to assessmentData.section:', learningPath);
            } else if (userData.currentPath) {
                learningPath = userData.currentPath;
                console.log('Digital Skills - Fallback to userData.currentPath:', learningPath);
            }
        }

        return {
            report: {
                employeeName: userData.firstName + ' ' + userData.lastName,
                role: userData.userRole,
                learningPath: learningPath, // Use the learning path from the most reliable source
                currentPath: learningPath, // Added for consistency
                competencyAnalysis: assessmentData.competencyAnalysis,
                summary: assessmentData.analysisSummary,
                enrollmentStatus: userData.enrollmentStatus || 'not_enrolled',
                email: userData.userEmail
            },
            recommendations: assessmentData.courseRecommendations?.map(rec => ({
                course: rec.courseName,
                reason: rec.justification
            })) || [],
            other_learning_paths_courses: assessmentData.otherPathRecommendations?.map(rec => ({
                course: rec.courseName,
                reason: rec.justification,
                learningPath: rec.learningPath
            })) || []
        };
    } catch (error) {
        console.error('Error fetching digital skills data:', error);
        return null;
    }
}

// Helper function to fetch soft skills data
async function fetchSoftSkillsData(userRef, userData) {
    try {
        const lastSoftSkillsAssessmentId = userData.lastSoftSkillsAssessmentId;
        if (!lastSoftSkillsAssessmentId) return null;

        // Fetch the latest soft skills assessment summary first
        const summarySnapshot = await userRef
            .collection('softSkillsSummaries')
            .orderBy('timestamp', 'desc')
            .limit(1)
            .get();

        // Get currentSection from the summary if available
        let currentSection = null;
        let learningPath = null;

        if (!summarySnapshot.empty) {
            const summaryData = summarySnapshot.docs[0].data();
            currentSection = summaryData.currentSection;
            learningPath = mapSectionToLearningPath(currentSection);
            console.log('Soft Skills - Current Section:', currentSection, 'Mapped Learning Path:', learningPath);
        }

        // Always fetch assessment data regardless of learning path source
        const assessmentDoc = await userRef
            .collection('softSkillsAssessmentResults')
            .doc(lastSoftSkillsAssessmentId)
            .get();

        if (!assessmentDoc.exists) return null;

        const assessmentData = assessmentDoc.data();

        // If no learning path from summary, try to get from assessment or user data
        if (!learningPath) {
            if (assessmentData.metadata?.learningPath) {
                learningPath = assessmentData.metadata.learningPath;
                console.log('Soft Skills - Fallback to assessment metadata:', learningPath);
            } else if (assessmentData.section) {
                learningPath = assessmentData.section;
                console.log('Soft Skills - Fallback to assessmentData.section:', learningPath);
            } else if (userData.currentPath) {
                learningPath = userData.currentPath;
                console.log('Soft Skills - Fallback to userData.currentPath:', learningPath);
            }
        }

        // Return the soft skills data with appropriate learning path
        return {
            report: {
                employeeName: userData.firstName + ' ' + userData.lastName,
                role: userData.userRole,
                learningPath: learningPath, // Use the learning path from summary or fallback
                currentPath: learningPath, // Added for consistency
                competencyAnalysis: assessmentData.competencyAnalysis,
                summary: assessmentData.analysisSummary,
                enrollmentStatus: userData.enrollmentStatus || 'not_enrolled',
                email: userData.userEmail
            },
            recommendations: assessmentData.courseRecommendations?.map(rec => ({
                course: rec.courseName,
                reason: rec.justification
            })) || [],
            other_learning_paths_courses: assessmentData.otherPathRecommendations?.map(rec => ({
                course: rec.courseName,
                reason: rec.justification,
                learningPath: rec.learningPath
            })) || []
        };
    } catch (error) {
        console.error('Error fetching soft skills data:', error);
        return null;
    }
}

// Helper function to fetch AI skills data
async function fetchAISkillsData(userRef, userData) {
    try {
        const lastAIAssessmentId = userData.lastAssessmentId_ai;
        if (!lastAIAssessmentId) return null;

        console.log('Fetching AI skills data for assessment ID:', lastAIAssessmentId);

        // Fetch the latest AI assessment summary first (like digital and soft skills)
        const summarySnapshot = await userRef
            .collection('assessmentSummaries_ai')
            .orderBy('timestamp', 'desc')
            .limit(1)
            .get();

        // Get currentSection from the summary if available
        let currentSection = null;
        let learningPath = null;

        if (!summarySnapshot.empty) {
            const summaryData = summarySnapshot.docs[0].data();
            currentSection = summaryData.currentSection;
            learningPath = mapSectionToLearningPath(currentSection);
            console.log('AI Skills - Current Section:', currentSection, 'Mapped Learning Path:', learningPath);
        }

        // Always fetch assessment data regardless of learning path source
        const assessmentDoc = await userRef
            .collection('assessmentResults_ai')
            .doc(lastAIAssessmentId)
            .get();

        if (!assessmentDoc.exists) {
            console.log('AI assessment document not found');
            return null;
        }

        const assessmentData = assessmentDoc.data();

        // If no learning path from summary, try fallback sources
        if (!learningPath) {
            // First, try assessment metadata
            if (assessmentData.metadata?.learningPath) {
                learningPath = assessmentData.metadata.learningPath;
                console.log('AI Skills - Fallback to assessment metadata:', learningPath);
            }
            // Try direct field in assessment data
            else if (assessmentData.learningPath) {
                learningPath = assessmentData.learningPath;
                console.log('AI Skills - Fallback to assessmentData.learningPath:', learningPath);
            }
            // Try section field (similar to other assessment types)
            else if (assessmentData.section) {
                learningPath = assessmentData.section;
                console.log('AI Skills - Fallback to assessmentData.section:', learningPath);
            }
            // Check user document for AI-specific learning path fields
            else if (userData.currentPath_ai) {
                learningPath = userData.currentPath_ai;
                console.log('AI Skills - Fallback to userData.currentPath_ai:', learningPath);
            }
            // Check if there's a general currentPath that might apply to AI
            else if (userData.currentPath) {
                learningPath = userData.currentPath;
                console.log('AI Skills - Fallback to userData.currentPath:', learningPath);
            }
            // Try to derive from AI skills analysis if available
            else if (userData.skillsAnalysis_ai) {
                // Parse the skills analysis to determine learning path
                const analysis = userData.skillsAnalysis_ai;
                if (analysis.includes('advanced') || analysis.includes('expert')) {
                    learningPath = 'advanced';
                } else if (analysis.includes('intermediate')) {
                    learningPath = 'intermediate';
                } else {
                    learningPath = 'essentials';
                }
                console.log('AI Skills - Fallback to skillsAnalysis_ai:', learningPath);
            }
            // Try to determine from competency analysis if available
            else if (assessmentData.competencyAnalysis) {
                const competencies = Object.values(assessmentData.competencyAnalysis);
                if (competencies.length > 0) {
                    const averageProficiency = competencies.reduce((sum, comp) => {
                        const proficiency = parseFloat(comp.proficiencyLevel?.replace('%', '') || 0);
                        return sum + proficiency;
                    }, 0) / competencies.length;

                    // Determine learning path based on average proficiency
                    if (averageProficiency >= 80) {
                        learningPath = 'advanced';
                    } else if (averageProficiency >= 60) {
                        learningPath = 'intermediate';
                    } else {
                        learningPath = 'essentials';
                    }
                    console.log('AI Skills - Derived from competency analysis, avg proficiency:', averageProficiency, 'path:', learningPath);
                }
            }
            // Default fallback based on user role
            else {
                // Try to make an intelligent guess based on user role
                const userRole = userData.userRole?.toLowerCase() || '';
                if (userRole.includes('senior') || userRole.includes('lead') || userRole.includes('manager') || userRole.includes('director')) {
                    learningPath = 'advanced';
                } else if (userRole.includes('specialist') || userRole.includes('analyst') || userRole.includes('coordinator')) {
                    learningPath = 'intermediate';
                } else {
                    learningPath = 'essentials';
                }
                console.log('AI Skills - Derived from user role:', userData.userRole, 'path:', learningPath);
            }
        }

        console.log('AI Skills - Final Learning Path:', learningPath);

        return {
            report: {
                employeeName: userData.firstName + ' ' + userData.lastName,
                role: userData.userRole,
                learningPath: learningPath,
                currentPath: learningPath,
                competencyAnalysis: assessmentData.competencyAnalysis,
                summary: assessmentData.analysisSummary,
                enrollmentStatus: userData.enrollmentStatus || 'not_enrolled',
                email: userData.userEmail,
                analysisType: 'aiSkills'
            },
            recommendations: assessmentData.courseRecommendations?.map(rec => ({
                course: rec.courseName,
                reason: rec.justification,
                isCurrentPath: rec.isCurrentPath
            })) || [],
            other_learning_paths_courses: assessmentData.otherPathRecommendations?.map(rec => ({
                course: rec.courseName,
                reason: rec.justification,
                learningPath: rec.learningPath,
                isCurrentPath: rec.isCurrentPath
            })) || []
        };
    } catch (error) {
        console.error('Error fetching AI skills data:', error);
        return null;
    }
}

// Helper function to map section numbers to learning path names
function mapSectionToLearningPath(sectionNumber) {
    if (!sectionNumber && sectionNumber !== 0) return null;

    switch (Number(sectionNumber)) {
        case 1: return 'Essentials';
        case 2: return 'Intermediate';
        case 3: return 'Advanced';
        case 4: return 'Champions';
        default: return null;
    }
}

async function fetchAssessmentData(userCompany) {
    const companyRef = db.collection('companies').doc(userCompany);
    const userSnapshot = await companyRef.collection('users').get();
    const users = userSnapshot.docs;
    const assessments = [];

    const userPromises = users.map(async (doc) => {
        const userData = doc.data();
        const [digitalResultsSnapshot, digitalSummarySnapshot, softSkillsResultsSnapshot, softSkillsSummarySnapshot, aiResultsSnapshot, aiSummarySnapshot] = await Promise.all([
            doc.ref.collection('assessmentResults')
                .orderBy('timestamp', 'desc')
                .limit(1)
                .get(),
            doc.ref.collection('assessmentSummaries')
                .orderBy('timestamp', 'desc')
                .limit(1)
                .get(),
            doc.ref.collection('softSkillsAssessmentResults')
                .orderBy('timestamp', 'desc')
                .limit(1)
                .get(),
            doc.ref.collection('softSkillsSummaries')
                .orderBy('timestamp', 'desc')
                .limit(1)
                .get(),
            doc.ref.collection('assessmentResults_ai')
                .orderBy('metadata.timestamp', 'desc')
                .limit(1)
                .get(),
            doc.ref.collection('assessmentSummaries_ai')
                .orderBy('timestamp', 'desc')
                .limit(1)
                .get()
        ]);

        const digitalResult = !digitalResultsSnapshot.empty ? digitalResultsSnapshot.docs[0].data() : null;
        const digitalSummary = !digitalSummarySnapshot.empty ? digitalSummarySnapshot.docs[0].data() : null;
        const softSkillsResult = !softSkillsResultsSnapshot.empty ? softSkillsResultsSnapshot.docs[0].data() : null;
        const softSkillsSummary = !softSkillsSummarySnapshot.empty ? softSkillsSummarySnapshot.docs[0].data() : null;
        const aiResult = !aiResultsSnapshot.empty ? aiResultsSnapshot.docs[0].data() : null;
        const aiSummary = !aiSummarySnapshot.empty ? aiSummarySnapshot.docs[0].data() : null;

        const createdAt = userData.createdAt ? userData.createdAt.toDate() : null;

        // Determine scores and paths for all assessment types
        const digitalScore = getScoreDisplay(digitalResult);
        const softSkillsScore = getScoreDisplay(softSkillsResult);
        const aiScore = getAIScoreDisplay(aiResult);
        const digitalPath = getPathDisplay(digitalResult);
        const softSkillsPath = getPathDisplay(softSkillsResult, 'soft');
        const aiPath = getAIPathDisplay(aiResult, aiSummary);

        // Build assessment object with digital, soft skills, and AI skills data
        const assessment = {
            employee: {
                name: `${userData.firstName} ${userData.lastName}`,
                position: userData.userRole,
                img: 'people.png',
                email: userData.userEmail
            },
            digital: {
                status: digitalResult ? 'completed' : 'pending',
                score: digitalScore,
                learningPath: digitalPath,
                summary: digitalSummary
            },
            soft: {
                status: softSkillsResult ? 'completed' : 'pending',
                score: softSkillsScore,
                learningPath: softSkillsPath,
                summary: softSkillsSummary
            },
            ai: {
                status: aiResult ? 'completed' : 'pending',
                score: aiScore,
                learningPath: aiPath,
                summary: aiSummary
            },
            createdAt: createdAt,
            reminded: userData.reminded || false
        };

        assessments.push(assessment);
    });

    await Promise.all(userPromises);

    return assessments.sort((a, b) => {
        if (a.createdAt && b.createdAt) return b.createdAt - a.createdAt;
        if (a.createdAt) return -1;
        if (b.createdAt) return 1;
        return 0;
    });
}

// Helper functions for score and path display
function getScoreDisplay(result) {
    if (!result) return 'pending';
    if (!result.totalQuestions) return '-------';
    return `${((result.score / result.totalQuestions) * 100).toFixed(0)}%`;
}

function getAIScoreDisplay(result) {
    if (!result) return 'pending';
    if (!result.competencyAnalysis) return '-------';
    
    // Calculate average proficiency from competency analysis
    const competencies = Object.values(result.competencyAnalysis);
    if (competencies.length === 0) return '-------';
    
    const totalProficiency = competencies.reduce((sum, comp) => {
        const proficiency = parseFloat(comp.proficiencyLevel?.replace('%', '') || 0);
        return sum + proficiency;
    }, 0);
    
    const averageProficiency = Math.round(totalProficiency / competencies.length);
    return `${averageProficiency}%`;
}

function getPathDisplay(result, type = 'digital') {
    if (!result) return 'pending';
    return result.section || '-------';
}

function getAIPathDisplay(result, summary) {
    if (!result) return 'pending';

    // First, try to get currentSection from the summary (like digital and soft skills)
    if (summary && summary.currentSection) {
        const learningPath = mapSectionToLearningPath(summary.currentSection);
        if (learningPath) {
            return learningPath;
        }
    }

    // Fallback: Try multiple possible locations for learning path in result
    if (result.metadata?.learningPath) {
        return result.metadata.learningPath;
    }

    // Check if it's stored directly in the result
    if (result.learningPath) {
        return result.learningPath;
    }

    // Check if it's in the section field (similar to other assessment types)
    if (result.section) {
        return result.section;
    }

    // Check user document fields that might contain AI learning path
    if (result.currentPath) {
        return result.currentPath;
    }

    return '-------';
}
