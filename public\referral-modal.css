/* Simple Referral Modal Styles */
.referral-modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.referral-modal-backdrop.show {
    opacity: 1;
    visibility: visible;
}

.referral-modal-container {
    background: white;
    border-radius: 12px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    transform: scale(0.95);
    transition: transform 0.3s ease;
}

.referral-modal-backdrop.show .referral-modal-container {
    transform: scale(1);
}

.referral-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 24px 24px 0 24px;
    border-bottom: 1px solid #e5e7eb;
    margin-bottom: 24px;
}

.referral-modal-title {
    font-size: 24px;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 4px 0;
}

.referral-modal-subtitle {
    font-size: 14px;
    color: #6b7280;
    margin: 0;
}

.referral-modal-close {
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.referral-modal-close:hover {
    background: #f3f4f6;
    color: #374151;
}

.referral-modal-content {
    padding: 0 24px 24px 24px;
}

.referral-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    margin-bottom: 32px;
}

.referral-stat-item {
    text-align: center;
    padding: 20px;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.referral-stat-number {
    font-size: 32px;
    font-weight: 700;
    color: #1e40af;
    margin-bottom: 4px;
}

.referral-stat-label {
    font-size: 14px;
    color: #64748b;
    font-weight: 500;
}

.referral-code-section {
    margin-bottom: 32px;
}

.referral-code-section h3 {
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 12px 0;
}

.referral-link-section {
    margin-bottom: 32px;
}

.referral-link-section h3 {
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 12px 0;
}

.referral-link-container {
    display: flex;
    gap: 8px;
    margin-bottom: 8px;
}

#referral-link-input {
    flex: 1;
    padding: 12px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 14px;
    color: #1f2937;
    background: #f9fafb;
}

.referral-link-description {
    font-size: 12px;
    color: #6b7280;
    margin: 0;
    font-style: italic;
}

.referral-code-container {
    display: flex;
    gap: 8px;
}

#referral-code-input {
    flex: 1;
    padding: 12px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
    background: #f9fafb;
    text-align: center;
}

.referral-copy-btn {
    padding: 12px 20px;
    background: #3b82f6;
    color: white;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.referral-copy-btn:hover {
    background: #2563eb;
}

.referral-how-it-works {
    margin-bottom: 32px;
}

.referral-how-it-works h3 {
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 16px 0;
}

.referral-steps {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.referral-step {
    display: flex;
    align-items: center;
    gap: 12px;
}

.referral-step-number {
    width: 32px;
    height: 32px;
    background: #3b82f6;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 14px;
    flex-shrink: 0;
}

.referral-step-text {
    color: #4b5563;
    font-size: 14px;
}

.referral-share-section h3 {
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 16px 0;
}

.referral-share-buttons {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.referral-share-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    color: #475569;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
}

.referral-share-btn:hover {
    background: #e2e8f0;
    border-color: #cbd5e1;
}

/* Mobile responsive */
@media (max-width: 640px) {
    .referral-modal-container {
        width: 95%;
        margin: 20px;
    }
    
    .referral-modal-header {
        padding: 20px 20px 0 20px;
    }
    
    .referral-modal-content {
        padding: 0 20px 20px 20px;
    }
    
    .referral-stats {
        grid-template-columns: 1fr;
    }
    
    .referral-share-buttons {
        flex-direction: column;
    }
    
    .referral-share-btn {
        justify-content: center;
    }
}
