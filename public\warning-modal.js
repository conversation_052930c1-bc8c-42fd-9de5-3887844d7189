/**
 * Warning Modal Component
 * A modern, customizable warning modal for critical actions
 */

(function(global) {
  'use strict';

  let isModalInitialized = false;
  let isClosing = false;
  let modalOverlay = null;
  let modalContent = null;
  let currentPromiseResolve = null;

  /**
   * Creates the modal HTML structure
   * @param {Object} options - Configuration options
   * @returns {string} HTML string for the modal
   */
  function createModalHTML(options) {
    const {
      title = 'Warning',
      message = 'Are you sure you want to proceed?',
      confirmText = 'Confirm',
      cancelText = 'Cancel',
      icon = 'warning', // warning, error, info
      confirmButtonStyle = 'danger', // danger, primary, secondary
    } = options;

    // Determine icon SVG based on type
    let iconSvg = '';
    
    if (icon === 'warning') {
      iconSvg = `
        <svg xmlns="http://www.w3.org/2000/svg" class="warning-modal-icon warning" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
          <line x1="12" y1="9" x2="12" y2="13"></line>
          <line x1="12" y1="17" x2="12.01" y2="17"></line>
        </svg>
      `;
    } else if (icon === 'error') {
      iconSvg = `
        <svg xmlns="http://www.w3.org/2000/svg" class="warning-modal-icon error" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <circle cx="12" cy="12" r="10"></circle>
          <line x1="15" y1="9" x2="9" y2="15"></line>
          <line x1="9" y1="9" x2="15" y2="15"></line>
        </svg>
      `;
    } else if (icon === 'info') {
      iconSvg = `
        <svg xmlns="http://www.w3.org/2000/svg" class="warning-modal-icon info" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <circle cx="12" cy="12" r="10"></circle>
          <line x1="12" y1="16" x2="12" y2="12"></line>
          <line x1="12" y1="8" x2="12.01" y2="8"></line>
        </svg>
      `;
    }

    // Determine confirm button class based on style
    let confirmButtonClass = 'warning-modal-confirm-button';
    if (confirmButtonStyle === 'danger') {
      confirmButtonClass += ' danger';
    } else if (confirmButtonStyle === 'primary') {
      confirmButtonClass += ' primary';
    } else if (confirmButtonStyle === 'secondary') {
      confirmButtonClass += ' secondary';
    }

    return `
      <div class="warning-modal-overlay">
        <div class="warning-modal-content">
          <div class="warning-modal-header">
            ${iconSvg}
            <h2 class="warning-modal-title">${title}</h2>
            <button class="warning-modal-close">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </button>
          </div>
          <div class="warning-modal-body">
            <p class="warning-modal-message">${message}</p>
          </div>
          <div class="warning-modal-footer">
            <button class="warning-modal-cancel-button">${cancelText}</button>
            <button class="${confirmButtonClass}">${confirmText}</button>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * Injects the required CSS styles
   */
  function injectCSS() {
    if (document.getElementById('warning-modal-styles')) return;

    const style = document.createElement('style');
    style.id = 'warning-modal-styles';
    style.textContent = `
      .warning-modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(18, 28, 65, 0.3);
        backdrop-filter: blur(3px);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 3000;
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      .warning-modal-content {
        background: #fff;
        border-radius: 0.75rem;
        width: 90%;
        max-width: 450px;
        box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        transform: scale(0.95);
        opacity: 0;
        transition: all 0.3s ease;
        overflow: hidden;
      }

      .warning-modal-header {
        display: flex;
        align-items: center;
        padding: 1.25rem 1.5rem 0.75rem;
        position: relative;
      }

      .warning-modal-icon {
        width: 2rem;
        height: 2rem;
        margin-right: 0.75rem;
      }

      .warning-modal-icon.warning {
        color: #F59E0B;
      }

      .warning-modal-icon.error {
        color: #EF4444;
      }

      .warning-modal-icon.info {
        color: #3B82F6;
      }

      .warning-modal-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #111827;
        margin: 0;
        flex-grow: 1;
      }

      .warning-modal-close {
        background: none;
        border: none;
        color: #6B7280;
        cursor: pointer;
        padding: 0.25rem;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 0.375rem;
        transition: background-color 0.2s, color 0.2s;
        position: absolute;
        top: 1rem;
        right: 1rem;
      }

      .warning-modal-close:hover {
        background-color: #F3F4F6;
        color: #374151;
      }

      .warning-modal-body {
        padding: 0.75rem 1.5rem 1.25rem;
      }

      .warning-modal-message {
        margin: 0;
        color: #4B5563;
        font-size: 1rem;
        line-height: 1.5;
      }

      .warning-modal-footer {
        display: flex;
        justify-content: flex-end;
        padding: 1rem 1.5rem 1.5rem;
        gap: 0.75rem;
      }

      .warning-modal-cancel-button {
        padding: 0.5rem 1rem;
        background-color: #F3F4F6;
        color: #374151;
        border: none;
        border-radius: 0.375rem;
        font-weight: 500;
        font-size: 0.875rem;
        cursor: pointer;
        transition: background-color 0.2s;
      }

      .warning-modal-cancel-button:hover {
        background-color: #E5E7EB;
      }

      .warning-modal-confirm-button {
        padding: 0.5rem 1rem;
        border: none;
        border-radius: 0.375rem;
        font-weight: 500;
        font-size: 0.875rem;
        cursor: pointer;
        transition: background-color 0.2s;
      }

      .warning-modal-confirm-button.danger {
        background-color: #EF4444;
        color: white;
      }

      .warning-modal-confirm-button.danger:hover {
        background-color: #DC2626;
      }

      .warning-modal-confirm-button.primary {
        background-color: #3B82F6;
        color: white;
      }

      .warning-modal-confirm-button.primary:hover {
        background-color: #2563EB;
      }

      .warning-modal-confirm-button.secondary {
        background-color: #6B7280;
        color: white;
      }

      .warning-modal-confirm-button.secondary:hover {
        background-color: #4B5563;
      }

      @media (max-width: 640px) {
        .warning-modal-content {
          width: 95%;
          max-width: none;
        }
        
        .warning-modal-footer {
          flex-direction: column-reverse;
        }
        
        .warning-modal-cancel-button,
        .warning-modal-confirm-button {
          width: 100%;
          padding: 0.75rem 1rem;
        }
      }
    `;
    document.head.appendChild(style);
  }

  /**
   * Initialize event listeners for the modal
   */
  function initializeEventListeners() {
    // Close button
    const closeButton = document.querySelector('.warning-modal-close');
    closeButton.addEventListener('click', () => hideWarningModal(false));

    // Cancel button
    const cancelButton = document.querySelector('.warning-modal-cancel-button');
    cancelButton.addEventListener('click', () => hideWarningModal(false));

    // Confirm button
    const confirmButton = document.querySelector('.warning-modal-confirm-button');
    confirmButton.addEventListener('click', () => hideWarningModal(true));

    // Click outside to close
    modalOverlay.addEventListener('click', (event) => {
      if (event.target === modalOverlay) {
        hideWarningModal(false);
      }
    });

    // Escape key to close
    document.addEventListener('keydown', handleKeyDown);
  }

  /**
   * Handle keydown events for the modal
   * @param {KeyboardEvent} event - The keyboard event
   */
  function handleKeyDown(event) {
    if (event.key === 'Escape' && isModalInitialized && !isClosing) {
      hideWarningModal(false);
    }
  }

  /**
   * Shows the warning modal
   * @param {Object} options - Configuration options
   * @returns {Promise<boolean>} - Resolves to true if confirmed, false if cancelled
   */
  function showWarningModal(options = {}) {
    isClosing = false;

    // Remove existing modal if it exists
    const existingModal = document.querySelector('.warning-modal-overlay');
    if (existingModal) {
      existingModal.parentNode.removeChild(existingModal);
      isModalInitialized = false;
    }

    // Remove existing keydown listener
    document.removeEventListener('keydown', handleKeyDown);

    // Create modal
    injectCSS();
    const modalHTML = createModalHTML(options);
    const modalContainer = document.createElement('div');
    modalContainer.innerHTML = modalHTML;

    // Add to body
    document.body.appendChild(modalContainer.firstElementChild);

    modalOverlay = document.querySelector('.warning-modal-overlay');
    modalContent = document.querySelector('.warning-modal-content');

    // Initialize event listeners
    initializeEventListeners();

    isModalInitialized = true;

    // Show modal with animation
    setTimeout(() => {
      if (isClosing) return;

      modalOverlay.style.opacity = '1';
      modalContent.style.opacity = '1';
      modalContent.style.transform = 'scale(1)';
    }, 10);

    return new Promise(resolve => {
      currentPromiseResolve = resolve;
    });
  }

  /**
   * Hides the warning modal
   * @param {boolean} result - The result of the modal (true for confirm, false for cancel)
   */
  function hideWarningModal(result) {
    isClosing = true;

    // Remove keydown listener
    document.removeEventListener('keydown', handleKeyDown);

    // Animate closing
    if (modalOverlay) {
      modalOverlay.style.opacity = '0';
      modalContent.style.opacity = '0';
      modalContent.style.transform = 'scale(0.95)';

      // Remove after animation completes
      setTimeout(() => {
        if (modalOverlay && modalOverlay.parentNode) {
          modalOverlay.parentNode.removeChild(modalOverlay);
        }
        isModalInitialized = false;

        // Resolve promise if it exists
        if (currentPromiseResolve) {
          currentPromiseResolve(result);
          currentPromiseResolve = null;
        }
      }, 300);
    }
  }

  // Public API
  global.WarningModal = {
    show: showWarningModal
  };
})(typeof window !== 'undefined' ? window : global);
