/**
 * Mobile Device Detection Utility
 * Provides functions to detect mobile devices and manage user preferences
 */

(function(global) {
  'use strict';

  /**
   * Check if the current device is a mobile device
   * Uses both User-Agent detection and screen size checks
   * @returns {boolean} True if the device is mobile, false otherwise
   */
  function isMobileDevice() {
    // User-Agent based detection
    const userAgent = navigator.userAgent || navigator.vendor || window.opera;
    const mobileRegex = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i;
    
    // Screen size based detection (typically mobile devices are narrower than 768px)
    const isNarrowScreen = window.innerWidth < 768;
    
    return mobileRegex.test(userAgent) || isNarrowScreen;
  }

  /**
   * Check if the user has dismissed the mobile warning
   * @returns {boolean} True if the warning has been dismissed, false otherwise
   */
  function hasUserDismissedWarning() {
    return localStorage.getItem('mobileWarningDismissed') === 'true';
  }

  /**
   * Mark the mobile warning as dismissed
   * @param {boolean} permanently - If true, the warning won't be shown again
   */
  function dismissWarning(permanently = false) {
    if (permanently) {
      localStorage.setItem('mobileWarningDismissed', 'true');
    } else {
      // For session-only dismissal, use sessionStorage instead
      sessionStorage.setItem('mobileWarningDismissed', 'true');
    }
  }

  /**
   * Check if the warning has been dismissed for the current session
   * @returns {boolean} True if dismissed for this session
   */
  function isWarningDismissedForSession() {
    return sessionStorage.getItem('mobileWarningDismissed') === 'true';
  }

  /**
   * Reset the dismissed state (for testing purposes)
   */
  function resetDismissedState() {
    localStorage.removeItem('mobileWarningDismissed');
    sessionStorage.removeItem('mobileWarningDismissed');
  }

  // Public API
  global.MobileDetection = {
    isMobileDevice,
    hasUserDismissedWarning,
    dismissWarning,
    isWarningDismissedForSession,
    resetDismissedState
  };

})(typeof window !== 'undefined' ? window : global);
