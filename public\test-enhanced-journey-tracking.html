<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced User Journey Tracking Test</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            padding: 20px; 
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .section { 
            margin: 20px 0; 
            padding: 15px; 
            border: 1px solid #ddd; 
            border-radius: 5px; 
            background: #fafafa;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .button-group {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 15px 0;
        }
        .result { 
            margin: 10px 0; 
            padding: 10px; 
            background: #f8f9fa; 
            border-radius: 3px; 
            font-family: monospace; 
            white-space: pre-wrap; 
            max-height: 400px; 
            overflow-y: auto;
            border-left: 4px solid #007bff;
        }
        .result.success { 
            background: #d4edda; 
            border-left-color: #28a745;
            color: #155724;
        }
        .result.error { 
            background: #f8d7da; 
            border-left-color: #dc3545;
            color: #721c24;
        }
        .result.warning { 
            background: #fff3cd; 
            border-left-color: #ffc107;
            color: #856404;
        }
        button { 
            padding: 10px 15px; 
            margin: 5px; 
            background: #007bff; 
            color: white; 
            border: none; 
            border-radius: 3px; 
            cursor: pointer;
            transition: background 0.2s;
        }
        button:hover { 
            background: #0056b3; 
        }
        button.secondary {
            background: #6c757d;
        }
        button.secondary:hover {
            background: #545b62;
        }
        button.success {
            background: #28a745;
        }
        button.success:hover {
            background: #1e7e34;
        }
        input { 
            padding: 8px; 
            margin: 5px; 
            width: 300px; 
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.connected {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .feature-demo {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .feature-card {
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: white;
        }
        .feature-card h4 {
            margin-top: 0;
            color: #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Enhanced User Journey Tracking Test</h1>
        <p>This page tests the enhanced user journey tracking system with granular "accessed" vs "used" tracking.</p>
        
        <!-- Connection Status -->
        <div id="connection-status" class="status disconnected">
            Checking Firebase connection...
        </div>

        <!-- Feature Access Testing -->
        <div class="section">
            <h3>Feature Access Testing</h3>
            <p>Test the difference between "accessed" (navigation) and "used" (meaningful actions).</p>
            
            <div class="feature-demo">
                <div class="feature-card">
                    <h4>Dashboard</h4>
                    <button onclick="testDashboardAccess()">Access Dashboard</button>
                    <button onclick="testDashboardUsage()" class="success">Use Dashboard Feature</button>
                </div>
                
                <div class="feature-card">
                    <h4>Invitations</h4>
                    <button onclick="testInvitationsAccess()">Access Invitations</button>
                    <button onclick="testInvitationsUsage()" class="success">Send Invitations</button>
                    <button onclick="testEmailVerification()" class="secondary">Verify Email</button>
                </div>
                
                <div class="feature-card">
                    <h4>Skills Gap Analysis</h4>
                    <button onclick="testSkillsGapFromDashboard()" class="success">From Dashboard</button>
                    <button onclick="testSkillsGapFromAssessments()" class="success">From Assessments</button>
                </div>
                
                <div class="feature-card">
                    <h4>Assessments</h4>
                    <button onclick="testAssessmentsAccess()">Access Assessments</button>
                    <button onclick="testAssessmentCompletion()" class="success">Complete Assessment</button>
                </div>
            </div>
        </div>

        <!-- Invitations Usage Criteria Testing -->
        <div class="section">
            <h3>Invitations Usage Criteria Testing</h3>
            <p>Test the specific criteria for marking invitations as "used".</p>
            
            <div class="button-group">
                <button onclick="checkInvitationsCriteria()">Check Current Criteria</button>
                <button onclick="simulateEmailVerification()" class="secondary">Simulate Email Verification</button>
                <button onclick="simulateAssessmentCompletion()" class="secondary">Simulate Assessment Completion</button>
                <button onclick="simulateInvitationSending()" class="secondary">Simulate Invitation Sending</button>
            </div>
        </div>

        <!-- Data Retrieval Testing -->
        <div class="section">
            <h3>Data Retrieval & Display</h3>
            <p>Test how the enhanced data is stored and retrieved.</p>
            
            <div class="button-group">
                <button onclick="getUserJourneyData()">Get User Journey Data</button>
                <button onclick="getFeatureStats()">Get Feature Statistics</button>
                <button onclick="clearTestData()" class="secondary">Clear Test Data</button>
            </div>
        </div>

        <!-- Real-time Tracking -->
        <div class="section">
            <h3>Real-time Tracking</h3>
            <p>Test real-time event tracking and milestone achievement.</p>
            
            <div class="button-group">
                <button onclick="startRealtimeTracking()">Start Monitoring</button>
                <button onclick="stopRealtimeTracking()" class="secondary">Stop Monitoring</button>
            </div>
            
            <div id="realtime-events" style="display: none;">
                <h4>Live Event Stream:</h4>
                <div id="event-stream" class="result"></div>
            </div>
        </div>

        <!-- Results Display -->
        <div class="section">
            <h3>Test Results</h3>
            <div id="results"></div>
        </div>
    </div>

    <!-- Include Firebase and the tracking system -->
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-auth.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-firestore.js"></script>
    <script src="user-journey-tracker.js"></script>

    <script>
        // Initialize Firebase
        const firebaseConfig = {
            apiKey: "AIzaSyDMUQCWXAprahuhrogEYFDEiMxwKALPXxc",
            authDomain: "barefoot-elearning-app.firebaseapp.com",
            projectId: "barefoot-elearning-app",
            databaseURL: "https://barefoot-elearning-app-default-rtdb.firebaseio.com/",
            storageBucket: "barefoot-elearning-app.appspot.com",
            messagingSenderId: "170819735788",
            appId: "1:170819735788:web:223af318437eb5d947d5c9"
        };

        if (!firebase.apps.length) {
            firebase.initializeApp(firebaseConfig);
        }

        let realtimeInterval = null;
        const results = document.getElementById('results');

        // Check Firebase connection
        firebase.auth().onAuthStateChanged((user) => {
            const status = document.getElementById('connection-status');
            if (user) {
                status.textContent = `Connected as: ${user.email}`;
                status.className = 'status connected';
                
                // Initialize tracking system
                if (window.UserJourneyTracker) {
                    window.UserJourneyTracker.initialize();
                }
            } else {
                status.textContent = 'Not authenticated - Please log in to test tracking';
                status.className = 'status disconnected';
            }
        });

        function addResult(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = `[${timestamp}] ${message}`;
            results.appendChild(resultDiv);
            results.scrollTop = results.scrollHeight;
        }

        // Feature Access Tests
        function testDashboardAccess() {
            if (window.UserJourneyTracker) {
                window.UserJourneyTracker.trackFeatureAccess('dashboard', { test: true });
                addResult('Dashboard accessed (navigation only)', 'success');
            } else {
                addResult('UserJourneyTracker not available', 'error');
            }
        }

        function testDashboardUsage() {
            if (window.UserJourneyTracker) {
                window.UserJourneyTracker.trackFeatureUsage('dashboard', { 
                    usageType: 'used',
                    action: 'data_interaction',
                    test: true 
                });
                addResult('Dashboard used (meaningful interaction)', 'success');
            } else {
                addResult('UserJourneyTracker not available', 'error');
            }
        }

        function testInvitationsAccess() {
            if (window.UserJourneyTracker) {
                window.UserJourneyTracker.trackInvitationsFeature('accessed', { test: true });
                addResult('Invitations accessed (navigation only)', 'success');
            } else {
                addResult('UserJourneyTracker not available', 'error');
            }
        }

        function testInvitationsUsage() {
            if (window.UserJourneyTracker) {
                window.UserJourneyTracker.trackInvitationSent(3, 'manual', { test: true });
                addResult('Invitations used (sent 3 invitations)', 'success');
            } else {
                addResult('UserJourneyTracker not available', 'error');
            }
        }

        function testEmailVerification() {
            if (window.UserJourneyTracker) {
                window.UserJourneyTracker.trackEmailVerification(true);
                addResult('Email verification tracked', 'success');
            } else {
                addResult('UserJourneyTracker not available', 'error');
            }
        }

        function testSkillsGapFromDashboard() {
            if (window.UserJourneyTracker) {
                window.UserJourneyTracker.trackSkillsGapAnalysis('dashboard', { test: true });
                addResult('Skills Gap Analysis used from Dashboard', 'success');
            } else {
                addResult('UserJourneyTracker not available', 'error');
            }
        }

        function testSkillsGapFromAssessments() {
            if (window.UserJourneyTracker) {
                window.UserJourneyTracker.trackSkillsGapAnalysis('assessments', { test: true });
                addResult('Skills Gap Analysis used from Assessments', 'success');
            } else {
                addResult('UserJourneyTracker not available', 'error');
            }
        }

        function testAssessmentsAccess() {
            if (window.UserJourneyTracker) {
                window.UserJourneyTracker.trackFeatureAccess('assessments', { test: true });
                addResult('Assessments accessed (navigation only)', 'success');
            } else {
                addResult('UserJourneyTracker not available', 'error');
            }
        }

        function testAssessmentCompletion() {
            if (window.UserJourneyTracker) {
                window.UserJourneyTracker.trackAssessmentCompletion({ 
                    assessmentType: 'digital',
                    test: true 
                });
                addResult('Assessment completion tracked', 'success');
            } else {
                addResult('UserJourneyTracker not available', 'error');
            }
        }

        // Criteria Testing
        async function checkInvitationsCriteria() {
            if (window.UserJourneyTracker) {
                try {
                    const criteria = await window.UserJourneyTracker.checkInvitationsUsageCriteria();
                    addResult(`Invitations criteria: ${JSON.stringify(criteria, null, 2)}`, 'info');
                } catch (error) {
                    addResult(`Error checking criteria: ${error.message}`, 'error');
                }
            } else {
                addResult('UserJourneyTracker not available', 'error');
            }
        }

        function simulateEmailVerification() {
            // This would normally be done by the auth system
            addResult('Email verification simulation - use testEmailVerification() instead', 'warning');
        }

        function simulateAssessmentCompletion() {
            testAssessmentCompletion();
        }

        function simulateInvitationSending() {
            testInvitationsUsage();
        }

        // Data Retrieval
        async function getUserJourneyData() {
            const user = firebase.auth().currentUser;
            if (!user) {
                addResult('Please log in first', 'error');
                return;
            }

            try {
                const doc = await firebase.firestore().collection('Admins').doc(user.email).get();
                if (doc.exists) {
                    const data = doc.data();
                    const journeyData = data.userJourney || {};
                    addResult(`User Journey Data: ${JSON.stringify(journeyData, null, 2)}`, 'info');
                } else {
                    addResult('No user data found', 'warning');
                }
            } catch (error) {
                addResult(`Error retrieving data: ${error.message}`, 'error');
            }
        }

        async function getFeatureStats() {
            const user = firebase.auth().currentUser;
            if (!user) {
                addResult('Please log in first', 'error');
                return;
            }

            try {
                const doc = await firebase.firestore().collection('Admins').doc(user.email).get();
                if (doc.exists) {
                    const data = doc.data();
                    const features = data.userJourney?.features || {};
                    
                    const stats = {
                        accessed: 0,
                        used: 0,
                        features: {}
                    };

                    Object.entries(features).forEach(([key, feature]) => {
                        if (feature.accessed) stats.accessed++;
                        if (feature.used) stats.used++;
                        
                        stats.features[key] = {
                            accessed: feature.accessed || false,
                            used: feature.used || false,
                            accessCount: feature.accessCount || 0,
                            usageCount: feature.usageCount || 0
                        };
                    });

                    addResult(`Feature Statistics: ${JSON.stringify(stats, null, 2)}`, 'info');
                } else {
                    addResult('No user data found', 'warning');
                }
            } catch (error) {
                addResult(`Error retrieving stats: ${error.message}`, 'error');
            }
        }

        async function clearTestData() {
            const user = firebase.auth().currentUser;
            if (!user) {
                addResult('Please log in first', 'error');
                return;
            }

            if (confirm('Are you sure you want to clear all test tracking data?')) {
                try {
                    await firebase.firestore().collection('Admins').doc(user.email).update({
                        'userJourney.features': firebase.firestore.FieldValue.delete(),
                        'userJourney.events': [],
                        'userJourney.milestones': firebase.firestore.FieldValue.delete()
                    });
                    addResult('Test data cleared successfully', 'success');
                } catch (error) {
                    addResult(`Error clearing data: ${error.message}`, 'error');
                }
            }
        }

        // Real-time tracking
        function startRealtimeTracking() {
            const eventStream = document.getElementById('event-stream');
            const realtimeSection = document.getElementById('realtime-events');
            
            realtimeSection.style.display = 'block';
            eventStream.textContent = 'Monitoring events...\n';

            // Monitor for new events every 2 seconds
            realtimeInterval = setInterval(async () => {
                const user = firebase.auth().currentUser;
                if (!user) return;

                try {
                    const doc = await firebase.firestore().collection('Admins').doc(user.email).get();
                    if (doc.exists) {
                        const events = doc.data().userJourney?.events || [];
                        const recentEvents = events.slice(0, 5); // Last 5 events
                        
                        let output = 'Recent Events:\n';
                        recentEvents.forEach(event => {
                            const timestamp = event.timestamp instanceof Date ? 
                                event.timestamp.toLocaleTimeString() : 
                                new Date(event.timestamp).toLocaleTimeString();
                            output += `[${timestamp}] ${event.eventType}: ${JSON.stringify(event.data || {})}\n`;
                        });
                        
                        eventStream.textContent = output;
                    }
                } catch (error) {
                    eventStream.textContent += `Error: ${error.message}\n`;
                }
            }, 2000);

            addResult('Real-time tracking started', 'success');
        }

        function stopRealtimeTracking() {
            if (realtimeInterval) {
                clearInterval(realtimeInterval);
                realtimeInterval = null;
                document.getElementById('realtime-events').style.display = 'none';
                addResult('Real-time tracking stopped', 'info');
            }
        }

        // Initialize on page load
        window.addEventListener('load', () => {
            addResult('Enhanced User Journey Tracking Test Page Loaded', 'info');
            
            // Check if UserJourneyTracker is available
            if (window.UserJourneyTracker) {
                addResult('UserJourneyTracker is available', 'success');
            } else {
                addResult('UserJourneyTracker not found', 'error');
            }
        });
    </script>
</body>
</html>
