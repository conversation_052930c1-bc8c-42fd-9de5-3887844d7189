(function(global) {
    async function handleUserFeedback(user) {
        const adminRef = db.collection('Admins').doc(user.email);
        const adminDoc = await adminRef.get();

        if (employeeCount < 5) {
            console.log('Skipping feedback for new user with less than 5 employees');
            return true;
        }
    
        if (adminDoc.exists && adminDoc.data().hasFeedback) {
            return true;
        }
    
        const overlay = document.createElement('div');
        overlay.id = 'feedback-overlay';
        overlay.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 opacity-0 transition-opacity duration-300';
        overlay.innerHTML = `
            <div class="bg-white p-6 rounded-lg shadow-lg max-w-md w-full transform transition-all duration-300 opacity-0 scale-95">
                <h2 class="text-2xl font-semibold mb-4 text-blue-600">We'd love your feedback!</h2>
                <p class="mb-6 text-gray-600">Before you go, could you spare a moment to share your experience with us?</p>
                <div class="flex justify-end space-x-3">
                    <button id="feedback-reject" class="px-5 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-200">No, thanks</button>
                    <button id="feedback-agree" class="px-5 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-200">Sure, I'll help</button>
                </div>
            </div>
        `;
    
        document.body.appendChild(overlay);
    
        setTimeout(() => {
            overlay.classList.remove('opacity-0');
            overlay.classList.add('opacity-100');
            const modalContent = overlay.querySelector('div');
            modalContent.classList.remove('opacity-0', 'scale-95');
            modalContent.classList.add('opacity-100', 'scale-100');
        }, 10);
    
        let ratingOverallValue = 0;
        let ratingPerformanceValue = 3;
    
        return new Promise((resolve) => {
            overlay.addEventListener('click', async (event) => {
                const target = event.target;
                if (target.id === 'feedback-reject') {
                    overlay.classList.add('opacity-0');
                    const modalContent = overlay.querySelector('div');
                    modalContent.classList.add('opacity-0', 'scale-95');
                    setTimeout(() => {
                        document.body.removeChild(overlay);
                        resolve(true);
                    }, 300);
                } else if (target.id === 'feedback-agree') {
                    overlay.innerHTML = `
                        <div class="bg-white p-6 rounded-lg shadow-lg max-w-md w-full transform transition-all duration-300 opacity-0 scale-95">
                            <h2 class="text-2xl font-semibold mb-4 text-blue-600">Your Feedback</h2>
                            <p class="mb-6 text-gray-600">Please rate your experience:</p>
                            <div class="mb-5">
                                <label class="block text-gray-700 mb-2">Overall Experience</label>
                                <div id="rating-overall" class="flex space-x-1">
                                    <!-- Stars will be inserted here -->
                                </div>
                            </div>
                            <div class="mb-5">
                                <label class="block text-gray-700 mb-2">Dashboard Performance</label>
                                <input id="rating-performance" type="range" min="1" max="5" value="3" class="w-full accent-blue-600">
                                <div class="text-center text-gray-700 mt-1" id="rating-performance-value">3</div>
                            </div>
                            <div class="mb-6">
                                <label class="block text-gray-700 mb-2">Additional Comments</label>
                                <textarea id="feedback-text" class="w-full h-24 p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Your feedback helps us improve."></textarea>
                            </div>
                            <div class="flex justify-between">
                                <button id="feedback-dismiss" class="px-5 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-200">Dismiss</button>
                                <button id="submit-feedback" class="px-5 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-200">Submit</button>
                            </div>
                        </div>
                    `;
    
                    // Animate new content appearance
                    const modalContent = overlay.querySelector('div');
                    setTimeout(() => {
                        modalContent.classList.remove('opacity-0', 'scale-95');
                        modalContent.classList.add('opacity-100', 'scale-100');
                    }, 10);
    
                    // Initialize star rating using Unicode characters
                    const ratingOverallDiv = overlay.querySelector('#rating-overall');
                    for (let i = 1; i <= 5; i++) {
                        const star = document.createElement('span');
                        star.className = 'text-gray-300 cursor-pointer text-3xl transition-colors duration-200';
                        star.innerHTML = '&#9733;'; // Unicode star character
                        star.dataset.value = i;
                        ratingOverallDiv.appendChild(star);
                    }
    
                    // Handle star rating clicks
                    ratingOverallDiv.addEventListener('click', (e) => {
                        if (e.target.tagName === 'SPAN') {
                            ratingOverallValue = parseInt(e.target.dataset.value);
                            const stars = ratingOverallDiv.querySelectorAll('span');
                            stars.forEach((star, index) => {
                                if (index < ratingOverallValue) {
                                    star.classList.remove('text-gray-300');
                                    star.classList.add('text-yellow-400');
                                } else {
                                    star.classList.add('text-gray-300');
                                    star.classList.remove('text-yellow-400');
                                }
                            });
                        }
                    });
    
                    // Handle slider value display
                    const ratingPerformanceInput = overlay.querySelector('#rating-performance');
                    const ratingPerformanceValueDiv = overlay.querySelector('#rating-performance-value');
                    ratingPerformanceInput.addEventListener('input', () => {
                        ratingPerformanceValue = parseInt(ratingPerformanceInput.value);
                        ratingPerformanceValueDiv.textContent = ratingPerformanceValue;
                    });
                } else if (target.id === 'feedback-dismiss') {
                    // Show the "You have declined to provide feedback" message
                    overlay.innerHTML = `
                        <div class="bg-white p-6 rounded-lg shadow-lg max-w-md w-full text-center transform transition-all duration-300 opacity-0 scale-95">
                            <p class="text-lg text-gray-700 mb-4">You have declined to provide feedback.</p>
                        </div>
                    `;
                    const modalContent = overlay.querySelector('div');
                    setTimeout(() => {
                        modalContent.classList.remove('opacity-0', 'scale-95');
                        modalContent.classList.add('opacity-100', 'scale-100');
                    }, 10);
                    // Start closing transition after delay
                    setTimeout(() => {
                        overlay.classList.add('opacity-0');
                        modalContent.classList.add('opacity-0', 'scale-95');
                        setTimeout(() => {
                            document.body.removeChild(overlay);
                            resolve(true);
                        }, 300);
                    }, 2000);
                } else if (target.id === 'submit-feedback') {
                    const feedbackText = overlay.querySelector('#feedback-text').value;
    
                    if (ratingOverallValue > 0 || ratingPerformanceValue > 0 || feedbackText.trim()) {
                        await adminRef.update({
                            feedback: feedbackText,
                            hasFeedback: true,
                            feedbackDate: firebase.firestore.FieldValue.serverTimestamp(),
                            ratingOverall: ratingOverallValue,
                            ratingPerformance: ratingPerformanceValue,
                        });
    
                        // Show confirmation dialog with tick icon
                        overlay.innerHTML = `
                            <div class="bg-white p-6 rounded-lg shadow-lg max-w-md w-full text-center transform transition-all duration-300 opacity-0 scale-95">
                                <div class="text-green-500 mb-4">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                    </svg>
                                </div>
                                <p class="text-lg text-gray-700 mb-4">Thank you for your feedback!</p>
                            </div>
                        `;
                        const modalContent = overlay.querySelector('div');
                        setTimeout(() => {
                            modalContent.classList.remove('opacity-0', 'scale-95');
                            modalContent.classList.add('opacity-100', 'scale-100');
                        }, 10);
    
                        setTimeout(() => {
                            overlay.classList.add('opacity-0');
                            modalContent.classList.add('opacity-0', 'scale-95');
                            setTimeout(() => {
                                document.body.removeChild(overlay);
                                resolve(true);
                            }, 300);
                        }, 2000);
                    } else {
                        showToast('Please provide some feedback before submitting.');
                    }
                }
            });
        });
    }

    global.handleUserFeedback = handleUserFeedback;
})(typeof window !== 'undefined' ? window : global);