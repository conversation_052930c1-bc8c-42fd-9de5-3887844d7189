<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo Mode Integration Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 2rem;
            background-color: #f8fafc;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 0.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            color: #1e293b;
            margin-bottom: 1rem;
        }
        
        .test-button {
            background: linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            cursor: pointer;
            margin: 0.5rem;
            font-weight: 600;
            transition: all 0.2s;
        }
        
        .test-button:hover {
            background: linear-gradient(135deg, #2563EB 0%, #1E40AF 100%);
            transform: translateY(-1px);
        }
        
        .test-button.danger {
            background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%);
        }
        
        .test-button.danger:hover {
            background: linear-gradient(135deg, #DC2626 0%, #B91C1C 100%);
        }
        
        .status-display {
            background-color: #f1f5f9;
            border: 1px solid #cbd5e1;
            border-radius: 0.375rem;
            padding: 1rem;
            margin: 1rem 0;
            font-family: monospace;
            font-size: 0.875rem;
        }
        
        .button-group {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }
        
        .demo-toggle-test {
            background-color: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .toggle-container {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }
        
        .toggle-switch {
            position: relative;
            width: 44px;
            height: 24px;
            background-color: #e5e7eb;
            border-radius: 12px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .toggle-switch.active {
            background-color: #3b82f6;
        }
        
        .toggle-dot {
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background-color: white;
            border-radius: 50%;
            transition: transform 0.3s;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        
        .toggle-switch.active .toggle-dot {
            transform: translateX(20px);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Demo Mode Integration Test</h1>
        <p>This page tests the integration between the feature unlock modal demo mode and the user menu demo toggle.</p>
        
        <div class="status-display" id="status-display">
            <strong>Current Status:</strong><br>
            Demo Mode: <span id="demo-status">Unknown</span><br>
            Session Storage: <span id="session-status">Unknown</span><br>
            User Company: <span id="company-status">Unknown</span><br>
            Original Company: <span id="original-company-status">Unknown</span>
        </div>
        
        <div class="demo-toggle-test">
            <h3>Manual Demo Toggle Test</h3>
            <div class="toggle-container">
                <span>Live Mode</span>
                <div class="toggle-switch" id="manual-toggle">
                    <div class="toggle-dot"></div>
                </div>
                <span>Demo Mode</span>
            </div>
            <p style="font-size: 0.875rem; color: #6b7280; margin-top: 0.5rem;">
                This simulates the user menu toggle behavior
            </p>
        </div>
        
        <div class="button-group">
            <button class="test-button" onclick="enableDemo()">
                Enable Demo Mode
            </button>
            
            <button class="test-button danger" onclick="disableDemo()">
                Disable Demo Mode
            </button>
            
            <button class="test-button" onclick="testFeatureUnlockModal()">
                Test Feature Unlock Modal
            </button>
            
            <button class="test-button" onclick="checkBannerVisibility()">
                Check Banner Visibility
            </button>
            
            <button class="test-button" onclick="refreshStatus()">
                Refresh Status
            </button>
            
            <button class="test-button" onclick="clearStorage()">
                Clear Storage
            </button>
        </div>
        
        <div style="margin-top: 2rem;">
            <h3>Instructions:</h3>
            <ol style="line-height: 1.6;">
                <li>Test enabling demo mode via the "Enable Demo Mode" button</li>
                <li>Verify that the banner appears and status updates</li>
                <li>Test the manual toggle to see if it syncs properly</li>
                <li>Test the feature unlock modal integration</li>
                <li>Verify that disabling demo mode removes the banner</li>
                <li>Check that session storage is properly managed</li>
            </ol>
        </div>
    </div>

    <!-- Include required scripts -->
    <script src="feature-unlock-modal.js"></script>
    
    <script>
        // Mock some global variables for testing
        window.originalUserCompany = 'Test Company Inc.';
        window.userCompany = 'Test Company Inc.';
        window.isDemoMode = false;
        
        // Mock functions that might not be available
        if (typeof window.updateNavigationState !== 'function') {
            window.updateNavigationState = function() {
                console.log('Mock updateNavigationState called');
            };
        }
        
        // Demo mode functions (copied from script.js for testing)
        function enableDemoMode() {
            window.isDemoMode = true;
            sessionStorage.setItem('demoMode', 'true');
            window.userCompany = 'Barefoot eLearning';
            showDemoBanner();
            updateManualToggle();
            refreshStatus();
            console.log('Demo mode enabled');
        }

        function disableDemoMode() {
            window.isDemoMode = false;
            sessionStorage.removeItem('demoMode');
            window.userCompany = window.originalUserCompany;
            hideDemoBanner();
            updateManualToggle();
            refreshStatus();
            console.log('Demo mode disabled');
        }
        
        function showDemoBanner() {
            // Remove existing banner
            hideDemoBanner();
            
            const banner = document.createElement('div');
            banner.id = 'demo-mode-banner';
            banner.style.cssText = `
                position: fixed;
                bottom: 0;
                left: 0;
                right: 0;
                background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
                color: white;
                z-index: 2000;
                padding: 0.5rem 1rem;
                display: flex;
                align-items: center;
                justify-content: space-between;
                box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
                min-height: 2.5rem;
            `;

            banner.innerHTML = `
                <div style="display: flex; align-items: center; gap: 0.5rem;">
                    <span style="font-weight: 600; font-size: 0.8rem; white-space: nowrap;">🔍 Demo Mode Active</span>
                    <span style="font-size: 0.7rem; opacity: 0.9; white-space: nowrap;">Viewing sample data</span>
                </div>
                <button onclick="disableDemo()" style="
                    background: rgba(255, 255, 255, 0.2);
                    border: 1px solid rgba(255, 255, 255, 0.3);
                    color: white;
                    padding: 0.375rem 0.625rem;
                    border-radius: 0.25rem;
                    font-size: 0.7rem;
                    cursor: pointer;
                    white-space: nowrap;
                    flex-shrink: 0;
                ">Exit Demo</button>
            `;

            document.body.appendChild(banner);
            document.body.style.paddingBottom = '3rem';
        }
        
        function hideDemoBanner() {
            const banner = document.getElementById('demo-mode-banner');
            if (banner) {
                banner.remove();
                document.body.style.paddingBottom = '2rem';
            }
        }
        
        function updateManualToggle() {
            const toggle = document.getElementById('manual-toggle');
            if (window.isDemoMode) {
                toggle.classList.add('active');
            } else {
                toggle.classList.remove('active');
            }
        }
        
        function refreshStatus() {
            document.getElementById('demo-status').textContent = window.isDemoMode ? 'Active' : 'Inactive';
            document.getElementById('session-status').textContent = sessionStorage.getItem('demoMode') || 'Not set';
            document.getElementById('company-status').textContent = window.userCompany || 'Not set';
            document.getElementById('original-company-status').textContent = window.originalUserCompany || 'Not set';
        }
        
        function enableDemo() {
            enableDemoMode();
        }
        
        function disableDemo() {
            disableDemoMode();
        }
        
        async function testFeatureUnlockModal() {
            if (window.FeatureUnlockModal) {
                try {
                    const result = await window.FeatureUnlockModal.show({
                        featureName: 'Dashboard',
                        employeeCount: 0
                    });
                    
                    if (result === 'demo') {
                        enableDemoMode();
                    }
                    
                    console.log('Feature unlock modal result:', result);
                } catch (error) {
                    console.error('Error with feature unlock modal:', error);
                }
            } else {
                alert('FeatureUnlockModal not available');
            }
        }
        
        function checkBannerVisibility() {
            const banner = document.getElementById('demo-mode-banner');
            alert(`Demo banner is ${banner ? 'visible' : 'hidden'}`);
        }
        
        function clearStorage() {
            sessionStorage.clear();
            refreshStatus();
            console.log('Storage cleared');
        }
        
        // Manual toggle event listener
        document.getElementById('manual-toggle').addEventListener('click', function() {
            if (window.isDemoMode) {
                disableDemoMode();
            } else {
                enableDemoMode();
            }
        });
        
        // Initialize
        refreshStatus();
        updateManualToggle();
        
        // Expose functions globally
        window.enableDemoMode = enableDemoMode;
        window.disableDemoMode = disableDemoMode;
        window.showDemoBanner = showDemoBanner;
        window.hideDemoBanner = hideDemoBanner;
    </script>
</body>
</html>
