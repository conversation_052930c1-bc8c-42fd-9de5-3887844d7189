// Initialize Firebase with the same config from other pages
const firebaseConfig = {
    apiKey: "AIzaSyDMUQCWXAprahuhrogEYFDEiMxwKALPXxc",
    authDomain: "barefoot-elearning-app.firebaseapp.com",
    projectId: "barefoot-elearning-app",
    databaseURL: "https://barefoot-elearning-app-default-rtdb.firebaseio.com/",
    storageBucket: "barefoot-elearning-app.appspot.com",
    messagingSenderId: "170819735788",
    appId: "1:170819735788:web:223af318437eb5d947d5c9"
};

// Initialize Firebase
firebase.initializeApp(firebaseConfig);

// Initialize animations
const loadingAnimation = lottie.loadAnimation({
    container: document.getElementById('loading-animation'),
    renderer: 'svg',
    loop: true,
    autoplay: true,
    path: 'assess_loading.json',
});

const successAnimation = lottie.loadAnimation({
    container: document.getElementById('success-animation'),
    renderer: 'svg',
    loop: false,
    autoplay: false,
    path: 'success.json',
});

const errorAnimation = lottie.loadAnimation({
    container: document.getElementById('error-animation'),
    renderer: 'svg',
    loop: false,
    autoplay: false,
    path: 'error.json', 
});

// Get URL parameters
const urlParams = new URLSearchParams(window.location.search);
const mode = urlParams.get('mode');
const actionCode = urlParams.get('oobCode');
const continueUrl = urlParams.get('continueUrl') || '/dashboard.html';
let email = urlParams.get('email') || '';

// Log parameters for debugging in local environment
if (window.appConfig && window.appConfig.isLocalEnvironment) {
    console.log('🔍 Verification Parameters:', {
        mode,
        actionCode: actionCode ? `${actionCode.substring(0, 5)}...` : null,
        email,
        continueUrl
    });
}

// Function to show different states
function showState(state) {
    document.getElementById('loading-state').classList.add('hidden');
    document.getElementById('success-state').classList.add('hidden');
    document.getElementById('error-state').classList.add('hidden');
    
    document.getElementById(`${state}-state`).classList.remove('hidden');
    
    if (state === 'success') {
        successAnimation.goToAndPlay(0, true);
    } else if (state === 'error') {
        errorAnimation.goToAndPlay(0, true);
    }
}

// Handle the verification
async function handleVerification() {
    // Check for local development test mode
    const isLocalTest = window.appConfig && window.appConfig.isLocalEnvironment && 
                        (!actionCode || actionCode.length !== 64);
    
    if (isLocalTest) {
        console.log('🧪 Running in local test mode - skipping actual Firebase verification');
        showState('success');
        return;
    }
    
    // Only proceed if we have a valid action code and mode is verifyEmail
    if (!actionCode || mode !== 'verifyEmail') {
        showState('error');
        document.getElementById('error-message').textContent = 
            'Invalid verification link. Please request a new verification email.';
        return;
    }

    try {
        // Apply the verification code
        await firebase.auth().applyActionCode(actionCode);
        
        // If we have the email, try to complete any pending email migration
        if (email) {
            try {
                const user = firebase.auth().currentUser;
                if (user && typeof window.completeEmailChange === 'function') {
                    await window.completeEmailChange(user);
                }
            } catch (migrationError) {
                console.error('Error completing email migration:', migrationError);
                // We don't fail the verification process because of migration issues
            }
        }
        
        showState('success');
        
    } catch (error) {
        console.error('Verification error:', error);
        showState('error');
        
        // Set specific error messages based on the error code
        if (error.code === 'auth/invalid-action-code') {
            document.getElementById('error-message').textContent = 
                'This verification link has expired or already been used. Please request a new one.';
        } else {
            document.getElementById('error-message').textContent = 
                `Verification failed: ${error.message}`;
        }
    }
}

// Handle resend verification email
document.getElementById('resend-email-btn').addEventListener('click', async () => {
    const resendButton = document.getElementById('resend-email-btn');
    
    try {
        // If we don't have the email from the URL, ask for it
        if (!email) {
            email = prompt('Please enter your email address to resend the verification:');
            if (!email) return;
        }
        
        // Disable the button and show loading state
        resendButton.disabled = true;
        resendButton.textContent = 'Sending...';
        
        // Send verification email
        await firebase.auth().sendPasswordResetEmail(email);
        
        // Show success message
        alert('A new verification email has been sent to your email address.');
        
    } catch (error) {
        console.error('Error sending verification email:', error);
        alert(`Failed to send verification email: ${error.message}`);
        resendButton.classList.add('shake');
        setTimeout(() => resendButton.classList.remove('shake'), 500);
    } finally {
        // Re-enable the button
        resendButton.disabled = false;
        resendButton.textContent = 'Resend Verification';
    }
});

// Start verification process when page loads
document.addEventListener('DOMContentLoaded', handleVerification);
