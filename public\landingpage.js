// Firebase configuration
const firebaseConfig = {
    apiKey: "AIzaSyDMUQCWXAprahuhrogEYFDEiMxwKALPXxc",
    authDomain: "barefoot-elearning-app.firebaseapp.com",
    projectId: "barefoot-elearning-app",
    databaseURL: "https://barefoot-elearning-app-default-rtdb.firebaseio.com/",
    storageBucket: "barefoot-elearning-app.appspot.com",
    messagingSenderId: "170819735788",
    appId: "1:170819735788:web:223af318437eb5d947d5c9"
};

// Initialize Firebase
if (!firebase.apps.length) {
    firebase.initializeApp(firebaseConfig);
}

const db = firebase.firestore();
const form = document.getElementById('demoForm');
const loadingOverlay = document.getElementById('loading-overlay');

// Initialize loading animation
const loadingAnimation = lottie.loadAnimation({
    container: document.getElementById('loading-animation'),
    renderer: 'svg',
    loop: true,
    autoplay: false,
    path: 'assess_loading.json'
});

// Show loading overlay
function showLoadingOverlay() {
    loadingOverlay.style.display = 'flex';
    loadingAnimation.play();
}

// Hide loading overlay
function hideLoadingOverlay() {
    loadingOverlay.style.display = 'none';
    loadingAnimation.stop();
}

// Show error message
function showError(fieldId, message) {
    const errorElement = document.getElementById(`${fieldId}Error`);
    const inputElement = document.getElementById(fieldId);
    
    if (errorElement && inputElement) {
        errorElement.textContent = message;
        errorElement.style.display = 'block';
        inputElement.style.borderColor = '#ef4444';
    }
}

// Hide error message
function hideError(fieldId) {
    const errorElement = document.getElementById(`${fieldId}Error`);
    const inputElement = document.getElementById(fieldId);
    
    if (errorElement && inputElement) {
        errorElement.style.display = 'none';
        inputElement.style.borderColor = '#d1d5db';
    }
}

// Validate email format
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Validate form
function validateForm() {
    let isValid = true;
    const firstname = document.getElementById('firstname').value.trim();
    const lastname = document.getElementById('lastname').value.trim();
    const email = document.getElementById('email').value.trim();
    const company = document.getElementById('company').value.trim();
    const role = document.getElementById('role').value.trim();

    // Clear all previous errors
    ['firstname', 'lastname', 'email', 'company', 'role'].forEach(hideError);

    // Validate first name
    if (!firstname) {
        showError('firstname', 'Please enter your first name');
        isValid = false;
    }

    // Validate last name
    if (!lastname) {
        showError('lastname', 'Please enter your last name');
        isValid = false;
    }

    // Validate email
    if (!email) {
        showError('email', 'Please enter your email address');
        isValid = false;
    } else if (!isValidEmail(email)) {
        showError('email', 'Please enter a valid email address');
        isValid = false;
    }

    // Validate company
    if (!company) {
        showError('company', 'Please enter your company name');
        isValid = false;
    }

    // Validate role
    if (!role) {
        showError('role', 'Please enter your role');
        isValid = false;
    }

    return isValid;
}

// Handle form submission
form.addEventListener('submit', async (e) => {
    e.preventDefault();

    if (!validateForm()) {
        return;
    }

    const firstname = document.getElementById('firstname').value.trim();
    const lastname = document.getElementById('lastname').value.trim();
    const email = document.getElementById('email').value.trim();
    const company = document.getElementById('company').value.trim();
    const role = document.getElementById('role').value.trim();

    showLoadingOverlay();

    try {
        // Save data to April_expo collection
        await db.collection('April_expo').add({
            firstname,
            lastname,
            email,
            company,
            role,
            timestamp: firebase.firestore.FieldValue.serverTimestamp()
        });

        // Store user data in sessionStorage for use on the next page
        sessionStorage.setItem('demoUser', JSON.stringify({
            firstname,
            lastname,
            email,
            company,
            role
        }));

        // Redirect to the video page
        window.location.href = 'landing-page-2.html';
    } catch (error) {
        console.error('Error saving data:', error);
        hideLoadingOverlay();
        alert('There was an error processing your request. Please try again.');
    }
});
