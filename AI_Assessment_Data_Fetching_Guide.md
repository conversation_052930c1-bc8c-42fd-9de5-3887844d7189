# AI Assessment Data Fetching Guide

This document provides comprehensive instructions on how to fetch data for the AI assessment system, including user data, assessment results, recommendations, and cached content.

## Table of Contents

1. [Database Structure Overview](#database-structure-overview)
2. [Authentication & Setup](#authentication--setup)
3. [Fetching User Data](#fetching-user-data)
4. [Fetching Assessment Results](#fetching-assessment-results)
5. [Fetching AI Recommendations](#fetching-ai-recommendations)
6. [Fetching Cached Data](#fetching-cached-data)
7. [API Endpoints](#api-endpoints)
8. [Error Handling](#error-handling)
9. [Code Examples](#code-examples)

## Database Structure Overview

The AI assessment system uses Firebase Firestore with the following structure:

```
companies/
├── {companyId}/
│   └── users/
│       └── {userEmail}/
│           ├── User Profile Data
│           ├── lastAssessmentId_ai: string
│           ├── lastAssessmentDate_ai: timestamp
│           ├── skillsAnalysis_ai: string
│           ├── courseRecommendations_ai: array
│           ├── otherPathRecommendations_ai: array
│           ├── assessmentResults_ai/
│           │   └── {assessmentId}/
│           └── assessmentSummaries_ai/
│               └── {summaryId}/
├── frameworks_ai/
│   └── {role}/
└── questionCache_ai/
    └── {cacheKey}/
```

## Authentication & Setup

### Firebase Configuration

```javascript
// Initialize Firebase
const firebaseConfig = {
  apiKey: "your-api-key",
  authDomain: "barefoot-elearning-app.firebaseapp.com",
  projectId: "barefoot-elearning-app",
  databaseURL: "https://barefoot-elearning-app-default-rtdb.firebaseio.com/",
  storageBucket: "barefoot-elearning-app.appspot.com",
  messagingSenderId: "your-sender-id",
  appId: "your-app-id"
};

firebase.initializeApp(firebaseConfig);
const db = firebase.firestore();
```

### Required Parameters

- **userEmail**: User's email address (used as document ID)
- **userCompany**: Company identifier (used as collection document ID)
- **assessmentId**: Specific assessment identifier (optional for latest)

## Fetching User Data

### Basic User Information

```javascript
async function fetchUserData(userEmail, userCompany) {
  try {
    const userRef = db.collection('companies')
                     .doc(userCompany)
                     .collection('users')
                     .doc(userEmail);
    
    const userDoc = await userRef.get();
    
    if (!userDoc.exists) {
      throw new Error('User not found');
    }
    
    return userDoc.data();
  } catch (error) {
    console.error('Error fetching user data:', error);
    throw error;
  }
}
```

### User Profile Fields

The user document contains:
- `firstName`: string
- `lastName`: string
- `userEmail`: string
- `userRole`: string
- `userCompany`: string
- `userPhone`: string
- `status`: string ('started', 'completed', 'aborted')
- `createdAt`: timestamp
- `updatedAt`: timestamp

### AI-Specific User Fields

- `lastAssessmentId_ai`: Reference to latest AI assessment
- `lastAssessmentDate_ai`: Timestamp of last AI assessment
- `skillsAnalysis_ai`: Overall skills analysis summary
- `courseRecommendations_ai`: Array of recommended courses
- `otherPathRecommendations_ai`: Array of alternative learning path recommendations

## Fetching Assessment Results

### Latest Assessment Results

```javascript
async function fetchLatestAssessmentResults(userEmail, userCompany) {
  try {
    // Get user document to find latest assessment ID
    const userData = await fetchUserData(userEmail, userCompany);
    const lastAssessmentId = userData.lastAssessmentId_ai;
    
    if (!lastAssessmentId) {
      throw new Error('No AI assessment found for user');
    }
    
    // Fetch the assessment results
    const userRef = db.collection('companies')
                     .doc(userCompany)
                     .collection('users')
                     .doc(userEmail);
    
    const assessmentDoc = await userRef
      .collection('assessmentResults_ai')
      .doc(lastAssessmentId)
      .get();
    
    if (!assessmentDoc.exists) {
      throw new Error('Assessment results not found');
    }
    
    return assessmentDoc.data();
  } catch (error) {
    console.error('Error fetching assessment results:', error);
    throw error;
  }
}
```

### Assessment Results Structure

```javascript
{
  competencyAnalysis: {
    "Excel Skills": {
      proficiencyLevel: "65%",
      strengthAreas: ["Basic formulas", "Data entry"],
      gapAreas: ["Excel - Advanced formulas", "Excel - Pivot tables"],
      progressionPath: ["Intermediate Excel", "Advanced Excel"]
    },
    "Communication Skills": {
      proficiencyLevel: "80%",
      strengthAreas: ["Written communication", "Presentation skills"],
      gapAreas: ["Communication - Public speaking"],
      progressionPath: ["Advanced Communication"]
    }
  },
  analysisSummary: "Overall summary of user's skills and development needs",
  courseRecommendations: [
    {
      courseName: "Excel - Intermediate",
      justification: "To improve data analysis capabilities",
      isCurrentPath: true
    }
  ],
  otherPathRecommendations: [
    {
      courseName: "PowerBI - Advanced",
      justification: "For advanced data visualization",
      learningPath: "Advanced Learning Pathway",
      isCurrentPath: false
    }
  ],
  metadata: {
    timestamp: timestamp,
    role: "Marketing Executive",
    learningPath: "intermediate"
  }
}
```

### All Assessment Results for User

```javascript
async function fetchAllAssessmentResults(userEmail, userCompany) {
  try {
    const userRef = db.collection('companies')
                     .doc(userCompany)
                     .collection('users')
                     .doc(userEmail);
    
    const assessmentResults = await userRef
      .collection('assessmentResults_ai')
      .orderBy('metadata.timestamp', 'desc')
      .get();
    
    const results = [];
    assessmentResults.forEach(doc => {
      results.push({
        id: doc.id,
        ...doc.data()
      });
    });
    
    return results;
  } catch (error) {
    console.error('Error fetching all assessment results:', error);
    throw error;
  }
}
```

## Fetching AI Recommendations

### Current Recommendations from User Document

```javascript
async function fetchCurrentRecommendations(userEmail, userCompany) {
  try {
    const userData = await fetchUserData(userEmail, userCompany);
    
    return {
      courseRecommendations: userData.courseRecommendations_ai || [],
      otherPathRecommendations: userData.otherPathRecommendations_ai || [],
      skillsAnalysis: userData.skillsAnalysis_ai || '',
      lastAssessmentDate: userData.lastAssessmentDate_ai
    };
  } catch (error) {
    console.error('Error fetching current recommendations:', error);
    throw error;
  }
}
```

### Detailed Recommendations from Assessment Results

```javascript
async function fetchDetailedRecommendations(userEmail, userCompany) {
  try {
    const assessmentData = await fetchLatestAssessmentResults(userEmail, userCompany);
    
    return {
      competencyAnalysis: assessmentData.competencyAnalysis,
      summary: assessmentData.analysisSummary,
      recommendations: assessmentData.courseRecommendations.map(rec => ({
        course: rec.courseName,
        reason: rec.justification,
        isCurrentPath: rec.isCurrentPath
      })),
      otherPathRecommendations: assessmentData.otherPathRecommendations?.map(rec => ({
        course: rec.courseName,
        reason: rec.justification,
        learningPath: rec.learningPath,
        isCurrentPath: rec.isCurrentPath
      })) || []
    };
  } catch (error) {
    console.error('Error fetching detailed recommendations:', error);
    throw error;
  }
}
```

## Fetching Cached Data

### Framework Data

```javascript
async function fetchFramework(role) {
  try {
    const frameworkDoc = await db.collection('frameworks_ai').doc(role).get();

    if (!frameworkDoc.exists) {
      throw new Error(`Framework not found for role: ${role}`);
    }

    return frameworkDoc.data();
  } catch (error) {
    console.error('Error fetching framework:', error);
    throw error;
  }
}
```

### Framework Structure

```javascript
{
  role: "Marketing Executive",
  coreCompetencies: [
    "Digital Marketing Strategy",
    "Content Creation",
    "Data Analysis",
    "Communication Skills"
  ],
  developmentPath: {
    levels: [
      {
        name: "Foundation",
        focus: "Basic marketing principles and tools",
        outcomes: ["Understanding of marketing fundamentals", "Basic tool proficiency"]
      },
      {
        name: "Intermediate",
        focus: "Advanced marketing techniques and analytics",
        outcomes: ["Campaign optimization", "Data-driven decision making"]
      },
      {
        name: "Advanced",
        focus: "Strategic marketing leadership",
        outcomes: ["Marketing strategy development", "Team leadership"]
      }
    ]
  },
  successMetrics: [
    "Campaign performance improvement",
    "Lead generation increase",
    "ROI optimization"
  ]
}
```

### Cached Questions

```javascript
async function fetchCachedQuestions(role, section, questionType = 'knowledge') {
  try {
    const cacheKey = questionType === 'self_assessment'
      ? `${role}_${section}_self_assessment`
      : `${role}_${section}_questions`;

    const cachedDoc = await db.collection('questionCache_ai').doc(cacheKey).get();

    if (!cachedDoc.exists) {
      return null; // No cached questions found
    }

    const data = cachedDoc.data();
    const cacheAge = Date.now() - data.timestamp.toDate();
    const CACHE_TTL = 7 * 24 * 60 * 60 * 1000; // 7 days

    if (cacheAge > CACHE_TTL) {
      console.log('Cache expired, returning null');
      return null;
    }

    return data.questions;
  } catch (error) {
    console.error('Error fetching cached questions:', error);
    return null;
  }
}
```

## API Endpoints

### Server Endpoints for Data Fetching

#### Get User Assessment Data
```
GET /api/user-assessment-data
Parameters:
- email: string (required)
- company: string (required)

Response:
{
  userData: {...},
  assessmentResults: {...},
  recommendations: {...}
}
```

#### Get Framework
```
GET /api/framework
Parameters:
- role: string (required)

Response:
{
  framework: {...}
}
```

#### Get Cached Questions
```
GET /api/cached-questions
Parameters:
- role: string (required)
- section: string (required)
- type: string (optional, 'knowledge' or 'self_assessment')

Response:
{
  questions: [...] or null
}
```

## Error Handling

### Common Error Scenarios

1. **User Not Found**
```javascript
if (!userDoc.exists) {
  throw new Error('User not found');
}
```

2. **No Assessment Data**
```javascript
if (!userData.lastAssessmentId_ai) {
  throw new Error('No AI assessment found for user');
}
```

3. **Assessment Results Missing**
```javascript
if (!assessmentDoc.exists) {
  throw new Error('Assessment results not found');
}
```

4. **Permission Denied**
```javascript
catch (error) {
  if (error.code === 'permission-denied') {
    throw new Error('Access denied to user data');
  }
}
```

### Error Response Structure

```javascript
{
  success: false,
  error: {
    code: 'USER_NOT_FOUND',
    message: 'User not found in the system',
    details: 'No user document exists for the provided email and company'
  }
}
```

## Code Examples

### Complete User Assessment Data Fetch

```javascript
async function fetchCompleteUserAssessmentData(userEmail, userCompany) {
  try {
    // Fetch user data
    const userData = await fetchUserData(userEmail, userCompany);

    // Check if user has AI assessment data
    if (!userData.lastAssessmentId_ai) {
      return {
        userData,
        hasAssessment: false,
        message: 'No AI assessment completed'
      };
    }

    // Fetch latest assessment results
    const assessmentResults = await fetchLatestAssessmentResults(userEmail, userCompany);

    // Fetch framework for user's role
    const framework = await fetchFramework(userData.userRole);

    return {
      userData,
      assessmentResults,
      framework,
      hasAssessment: true,
      lastAssessmentDate: userData.lastAssessmentDate_ai
    };

  } catch (error) {
    console.error('Error fetching complete user assessment data:', error);
    throw error;
  }
}
```

### Batch User Data Fetch

```javascript
async function fetchMultipleUsersData(userEmails, userCompany) {
  try {
    const promises = userEmails.map(email =>
      fetchCompleteUserAssessmentData(email, userCompany)
        .catch(error => ({
          email,
          error: error.message
        }))
    );

    const results = await Promise.all(promises);

    return {
      successful: results.filter(r => !r.error),
      failed: results.filter(r => r.error)
    };

  } catch (error) {
    console.error('Error in batch fetch:', error);
    throw error;
  }
}
```

### Real-time Data Listener

```javascript
function setupUserDataListener(userEmail, userCompany, callback) {
  const userRef = db.collection('companies')
                   .doc(userCompany)
                   .collection('users')
                   .doc(userEmail);

  return userRef.onSnapshot(
    (doc) => {
      if (doc.exists) {
        callback(null, doc.data());
      } else {
        callback(new Error('User not found'), null);
      }
    },
    (error) => {
      callback(error, null);
    }
  );
}

// Usage
const unsubscribe = setupUserDataListener('<EMAIL>', 'company-id',
  (error, userData) => {
    if (error) {
      console.error('Listener error:', error);
    } else {
      console.log('Updated user data:', userData);
      // Handle real-time updates
    }
  }
);

// Clean up listener when done
// unsubscribe();
```

### Assessment Summary Fetch

```javascript
async function fetchAssessmentSummaries(userEmail, userCompany) {
  try {
    const userRef = db.collection('companies')
                     .doc(userCompany)
                     .collection('users')
                     .doc(userEmail);

    const summaries = await userRef
      .collection('assessmentSummaries_ai')
      .orderBy('timestamp', 'desc')
      .get();

    const summaryData = [];
    summaries.forEach(doc => {
      summaryData.push({
        id: doc.id,
        ...doc.data()
      });
    });

    return summaryData;
  } catch (error) {
    console.error('Error fetching assessment summaries:', error);
    throw error;
  }
}
```

## Best Practices

1. **Always check for data existence** before accessing nested properties
2. **Use try-catch blocks** for all async operations
3. **Implement proper error handling** with meaningful error messages
4. **Cache frequently accessed data** to improve performance
5. **Use real-time listeners** for live data updates when needed
6. **Validate user permissions** before fetching sensitive data
7. **Handle offline scenarios** gracefully
8. **Implement retry logic** for failed requests
9. **Use batch operations** when fetching multiple documents
10. **Monitor cache hit rates** and adjust TTL accordingly

## Security Considerations

1. **Validate user access** to company data
2. **Sanitize input parameters** to prevent injection attacks
3. **Use Firebase Security Rules** to control data access
4. **Implement rate limiting** for API endpoints
5. **Log access attempts** for audit purposes
6. **Encrypt sensitive data** in transit and at rest
7. **Use authentication tokens** for API access
8. **Implement proper session management**

## Performance Optimization

1. **Use indexes** for frequently queried fields
2. **Implement pagination** for large result sets
3. **Cache frequently accessed data** in memory
4. **Use compound queries** to reduce round trips
5. **Optimize document structure** to minimize reads
6. **Monitor query performance** and optimize slow queries
7. **Use connection pooling** for database connections
8. **Implement data compression** for large payloads

## Troubleshooting

### Common Issues

1. **Permission Denied Errors**
   - Check Firebase Security Rules
   - Verify user authentication
   - Ensure proper company access

2. **Data Not Found**
   - Verify document paths
   - Check collection names (ensure _ai suffix)
   - Confirm data exists in database

3. **Cache Misses**
   - Check cache TTL settings
   - Verify cache key generation
   - Monitor cache performance

4. **Slow Query Performance**
   - Add appropriate indexes
   - Optimize query structure
   - Consider data denormalization

### Debug Logging

```javascript
// Enable debug logging
firebase.firestore.setLogLevel('debug');

// Custom logging function
function logDataFetch(operation, params, result, error) {
  console.log({
    timestamp: new Date().toISOString(),
    operation,
    params,
    success: !error,
    error: error?.message,
    resultSize: result ? JSON.stringify(result).length : 0
  });
}
```

This guide provides comprehensive instructions for fetching AI assessment data from the Firebase Firestore database with proper error handling, security considerations, and performance optimization techniques.
