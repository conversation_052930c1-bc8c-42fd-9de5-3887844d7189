/* Border loading animation */
@keyframes borderPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(37, 99, 235, 0.1);
    border-color: rgba(37, 99, 235, 0.2);
  }
  50% {
    box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.3);
    border-color: rgba(37, 99, 235, 0.6);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(37, 99, 235, 0.1);
    border-color: rgba(37, 99, 235, 0.2);
  }
}

.learner-card-loading {
  position: relative;
  border: 1px solid rgba(37, 99, 235, 0.3);
  animation: borderPulse 1.5s ease-in-out infinite;
  transform: translateZ(0); /* Force hardware acceleration */
}

/* Subtle gradient overlay to indicate loading state */
.learner-card-loading::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.3) 25%,
    rgba(237, 242, 247, 0.3) 37%,
    rgba(255, 255, 255, 0.3) 63%
  );
  background-size: 400% 100%;
  animation: shimmer 2s infinite linear;
  pointer-events: none;
  border-radius: 8px;
}

@keyframes shimmer {
  0% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}
