<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Referral System - SkillsAssess</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <!-- Firebase -->
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-auth.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-firestore.js"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-8 text-center">Referral System Test</h1>
        
        <!-- Test Results -->
        <div id="test-results" class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h2 class="text-xl font-semibold mb-4">Test Results</h2>
            <div id="results-content" class="space-y-2">
                <p class="text-gray-600">Running tests...</p>
            </div>
        </div>

        <!-- Manual Test Section -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h2 class="text-xl font-semibold mb-4">Manual Tests</h2>
            
            <div class="space-y-4">
                <div>
                    <h3 class="font-medium mb-2">1. Test Referral Code Generation</h3>
                    <button id="test-generate-code" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                        Generate Test Code
                    </button>
                    <span id="generated-code" class="ml-4 font-mono"></span>
                </div>

                <div>
                    <h3 class="font-medium mb-2">2. Test Referral Code Validation</h3>
                    <input type="text" id="test-code-input" placeholder="Enter referral code" 
                           class="border rounded px-3 py-2 mr-2" maxlength="8">
                    <button id="test-validate-code" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                        Validate Code
                    </button>
                    <span id="validation-result" class="ml-4"></span>
                </div>

                <div>
                    <h3 class="font-medium mb-2">3. Test User Referral Data</h3>
                    <button id="test-user-data" class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600">
                        Check Current User Data
                    </button>
                    <div id="user-data-result" class="mt-2 p-3 bg-gray-50 rounded hidden">
                        <pre id="user-data-content" class="text-sm"></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyDMUQCWXAprahuhrogEYFDEiMxwKALPXxc",
            authDomain: "barefoot-elearning-app.firebaseapp.com",
            projectId: "barefoot-elearning-app",
            databaseURL: "https://barefoot-elearning-app-default-rtdb.firebaseio.com/",
            storageBucket: "barefoot-elearning-app.appspot.com",
            messagingSenderId: "170819735788",
            appId: "1:170819735788:web:223af318437eb5d947d5c9"
        };

        firebase.initializeApp(firebaseConfig);
        const db = firebase.firestore();

        // Function to generate referral code (same as in signup.js)
        function generateReferralCode() {
            const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
            let result = '';
            for (let i = 0; i < 8; i++) {
                result += characters.charAt(Math.floor(Math.random() * characters.length));
            }
            return result;
        }

        // Test functions
        function addTestResult(message, type = 'info') {
            const resultsContent = document.getElementById('results-content');
            const p = document.createElement('p');
            p.className = type === 'success' ? 'text-green-600' : 
                         type === 'error' ? 'text-red-600' : 'text-gray-600';
            p.textContent = message;
            resultsContent.appendChild(p);
        }

        // Run automatic tests
        async function runTests() {
            addTestResult('Starting referral system tests...');
            
            // Test 1: Code generation
            try {
                const code = generateReferralCode();
                if (code && code.length === 8) {
                    addTestResult(`✓ Code generation works: ${code}`, 'success');
                } else {
                    addTestResult('✗ Code generation failed', 'error');
                }
            } catch (error) {
                addTestResult(`✗ Code generation error: ${error.message}`, 'error');
            }

            // Test 2: Check if user is authenticated
            const user = firebase.auth().currentUser;
            if (user) {
                addTestResult(`✓ User authenticated: ${user.email}`, 'success');
                
                // Test 3: Check user data structure
                try {
                    const userDoc = await db.collection('Admins').doc(user.email).get();
                    if (userDoc.exists) {
                        const userData = userDoc.data();
                        const hasReferralCode = userData.referralCode ? true : false;
                        const hasReferralStats = userData.referralStats ? true : false;
                        
                        addTestResult(`✓ User document exists`, 'success');
                        addTestResult(`${hasReferralCode ? '✓' : '✗'} Referral code: ${userData.referralCode || 'Missing'}`, 
                                    hasReferralCode ? 'success' : 'error');
                        addTestResult(`${hasReferralStats ? '✓' : '✗'} Referral stats present`, 
                                    hasReferralStats ? 'success' : 'error');
                    } else {
                        addTestResult('✗ User document not found', 'error');
                    }
                } catch (error) {
                    addTestResult(`✗ Error checking user data: ${error.message}`, 'error');
                }
            } else {
                addTestResult('✗ User not authenticated - please log in first', 'error');
            }
        }

        // Manual test handlers
        document.getElementById('test-generate-code').addEventListener('click', () => {
            const code = generateReferralCode();
            document.getElementById('generated-code').textContent = code;
        });

        document.getElementById('test-validate-code').addEventListener('click', async () => {
            const code = document.getElementById('test-code-input').value.trim().toUpperCase();
            const resultElement = document.getElementById('validation-result');
            
            if (!code) {
                resultElement.textContent = 'Please enter a code';
                resultElement.className = 'ml-4 text-red-600';
                return;
            }

            try {
                const query = await db.collection('Admins')
                    .where('referralCode', '==', code)
                    .limit(1)
                    .get();
                
                if (!query.empty) {
                    const referrerDoc = query.docs[0];
                    resultElement.textContent = `Valid! Belongs to: ${referrerDoc.id}`;
                    resultElement.className = 'ml-4 text-green-600';
                } else {
                    resultElement.textContent = 'Invalid code';
                    resultElement.className = 'ml-4 text-red-600';
                }
            } catch (error) {
                resultElement.textContent = `Error: ${error.message}`;
                resultElement.className = 'ml-4 text-red-600';
            }
        });

        document.getElementById('test-user-data').addEventListener('click', async () => {
            const user = firebase.auth().currentUser;
            const resultDiv = document.getElementById('user-data-result');
            const contentPre = document.getElementById('user-data-content');
            
            if (!user) {
                contentPre.textContent = 'User not authenticated';
                resultDiv.classList.remove('hidden');
                return;
            }

            try {
                const userDoc = await db.collection('Admins').doc(user.email).get();
                if (userDoc.exists) {
                    const userData = userDoc.data();
                    const relevantData = {
                        email: user.email,
                        referralCode: userData.referralCode,
                        referralStats: userData.referralStats,
                        wasReferred: userData.wasReferred,
                        referredBy: userData.referredBy,
                        referralCodeUsed: userData.referralCodeUsed,
                        credits: userData.credits
                    };
                    contentPre.textContent = JSON.stringify(relevantData, null, 2);
                } else {
                    contentPre.textContent = 'User document not found';
                }
                resultDiv.classList.remove('hidden');
            } catch (error) {
                contentPre.textContent = `Error: ${error.message}`;
                resultDiv.classList.remove('hidden');
            }
        });

        // Wait for auth state and run tests
        firebase.auth().onAuthStateChanged((user) => {
            setTimeout(runTests, 1000); // Give some time for auth to settle
        });
    </script>
</body>
</html>
