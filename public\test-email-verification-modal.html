<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Verification Modal Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            padding: 2rem;
            background-color: #f9fafb;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 0.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .test-button {
            background-color: #3B82F6;
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 0.375rem;
            font-weight: 500;
            cursor: pointer;
            margin: 0.5rem;
            transition: background-color 0.2s;
        }
        .test-button:hover {
            background-color: #2563EB;
        }
        .result {
            margin-top: 1rem;
            padding: 1rem;
            border-radius: 0.375rem;
            background-color: #F3F4F6;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Email Verification Modal Test</h1>
        <p>This page tests the email verification modal component.</p>
        
        <button class="test-button" onclick="testModal()">
            Test Email Verification Modal
        </button>
        
        <button class="test-button" onclick="testModalWithCustomMessage()">
            Test with Custom Message
        </button>
        
        <div id="result" class="result" style="display: none;">
            <strong>Result:</strong> <span id="result-text"></span>
        </div>
    </div>

    <!-- Include the modal script -->
    <script src="email-verification-modal.js"></script>
    
    <script>
        async function testModal() {
            try {
                const result = await EmailVerificationModal.show();
                showResult(`User ${result ? 'confirmed' : 'cancelled'} the modal`);
            } catch (error) {
                showResult(`Error: ${error.message}`);
            }
        }
        
        async function testModalWithCustomMessage() {
            try {
                const result = await EmailVerificationModal.show({
                    title: 'Custom Verification Title',
                    message: 'This is a custom message for testing the email verification modal with different content.',
                    confirmText: 'Custom Confirm',
                    cancelText: 'Custom Cancel'
                });
                showResult(`User ${result ? 'confirmed' : 'cancelled'} the custom modal`);
            } catch (error) {
                showResult(`Error: ${error.message}`);
            }
        }
        
        function showResult(text) {
            const resultDiv = document.getElementById('result');
            const resultText = document.getElementById('result-text');
            resultText.textContent = text;
            resultDiv.style.display = 'block';
            
            // Hide after 5 seconds
            setTimeout(() => {
                resultDiv.style.display = 'none';
            }, 5000);
        }
    </script>
</body>
</html>
