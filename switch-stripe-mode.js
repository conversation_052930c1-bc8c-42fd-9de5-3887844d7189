/**
 * Utility script to switch between Stripe test and live modes
 * 
 * Usage:
 * node switch-stripe-mode.js [test|live]
 */

require('dotenv').config();
const fs = require('fs');
const path = require('path');

// Get the mode from command line arguments
const args = process.argv.slice(2);
const requestedMode = args[0]?.toLowerCase();

if (!requestedMode || (requestedMode !== 'test' && requestedMode !== 'live')) {
  console.error('Please specify a valid mode: "test" or "live"');
  console.log('Usage: node switch-stripe-mode.js [test|live]');
  process.exit(1);
}

// Read the current .env file
const envPath = path.join(__dirname, '.env');
let envContent = fs.readFileSync(envPath, 'utf8');

// Update the STRIPE_MODE in the .env file
const modeRegex = /STRIPE_MODE=.*/;
if (modeRegex.test(envContent)) {
  // Replace existing STRIPE_MODE
  envContent = envContent.replace(modeRegex, `STRIPE_MODE=${requestedMode}`);
} else {
  // Add STRIPE_MODE if it doesn't exist
  envContent += `\nSTRIPE_MODE=${requestedMode}`;
}

// Write the updated content back to the .env file
fs.writeFileSync(envPath, envContent);

// Update the active keys in the .env file
const publishableKeyRegex = /STRIPE_PUBLISHABLE_KEY=.*/;
const secretKeyRegex = /STRIPE_SECRET_KEY=.*/;
const webhookSecretRegex = /STRIPE_WEBHOOK_SECRET=.*/;

const testPublishableKey = process.env.STRIPE_TEST_PUBLISHABLE_KEY;
const testSecretKey = process.env.STRIPE_TEST_SECRET_KEY;
const testWebhookSecret = process.env.STRIPE_TEST_WEBHOOK_SECRET;

const livePublishableKey = process.env.STRIPE_LIVE_PUBLISHABLE_KEY;
const liveSecretKey = process.env.STRIPE_LIVE_SECRET_KEY;
const liveWebhookSecret = process.env.STRIPE_LIVE_WEBHOOK_SECRET;

if (requestedMode === 'test') {
  envContent = envContent.replace(publishableKeyRegex, `STRIPE_PUBLISHABLE_KEY=${testPublishableKey}`);
  envContent = envContent.replace(secretKeyRegex, `STRIPE_SECRET_KEY=${testSecretKey}`);
  envContent = envContent.replace(webhookSecretRegex, `STRIPE_WEBHOOK_SECRET=${testWebhookSecret}`);
} else {
  envContent = envContent.replace(publishableKeyRegex, `STRIPE_PUBLISHABLE_KEY=${livePublishableKey}`);
  envContent = envContent.replace(secretKeyRegex, `STRIPE_SECRET_KEY=${liveSecretKey}`);
  envContent = envContent.replace(webhookSecretRegex, `STRIPE_WEBHOOK_SECRET=${liveWebhookSecret}`);
}

// Write the updated content back to the .env file
fs.writeFileSync(envPath, envContent);

console.log(`Switched to Stripe ${requestedMode.toUpperCase()} mode`);
console.log('Restart your server for changes to take effect');
