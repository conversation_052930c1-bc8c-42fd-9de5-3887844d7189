(function (global) {
  let otherPathRecommendations = [];
  let courseRecommendations = []; // Add this line to store course recommendations at module level

  // Function to get user's learning paths from both assessment types
  async function getUserLearningPaths(learner) {
    try {
      const userRef = db
        .collection("companies")
        .doc(learner.company)
        .collection("users")
        .doc(learner.id);

      const userDoc = await userRef.get();
      if (!userDoc.exists) throw new Error("User not found");

      const userData = userDoc.data();

      // Get digital skills learning path
      const digitalSummarySnapshot = await userRef
        .collection("assessmentSummaries")
        .orderBy("timestamp", "desc")
        .limit(1)
        .get();

      let digitalPath = null;
      if (!digitalSummarySnapshot.empty) {
        const summaryData = digitalSummarySnapshot.docs[0].data();
        const currentSection = summaryData.currentSection;
        digitalPath = mapSectionToLearningPath(currentSection);
      }

      // Get soft skills learning path
      const softSummarySnapshot = await userRef
        .collection("softSkillsSummaries")
        .orderBy("timestamp", "desc")
        .limit(1)
        .get();

      let softPath = null;
      if (!softSummarySnapshot.empty) {
        const summaryData = softSummarySnapshot.docs[0].data();
        const currentSection = summaryData.currentSection;
        softPath = mapSectionToLearningPath(currentSection);
      }

      // Get AI skills learning path
      const aiSummarySnapshot = await userRef
        .collection("assessmentSummaries_ai")
        .orderBy("timestamp", "desc")
        .limit(1)
        .get();

      let aiPath = null;
      if (!aiSummarySnapshot.empty) {
        const summaryData = aiSummarySnapshot.docs[0].data();
        // For AI assessments, the learning path might be stored differently
        // Check both currentSection (if it follows the same pattern) and direct learningPath
        const currentSection = summaryData.currentSection;
        if (currentSection !== undefined) {
          aiPath = mapSectionToLearningPath(currentSection);
        } else if (summaryData.learningPath) {
          aiPath = summaryData.learningPath;
        }
      }

      // If no path from summary, try user document AI fields
      if (!aiPath && userData.lastAssessmentId_ai) {
        // Check if there's an AI-specific current path
        if (userData.currentPath_ai) {
          aiPath = userData.currentPath_ai;
        }
        // Check if there's AI skills analysis that can determine path
        else if (userData.skillsAnalysis_ai) {
          const analysis = userData.skillsAnalysis_ai.toLowerCase();
          if (analysis.includes('advanced') || analysis.includes('expert')) {
            aiPath = 'advanced';
          } else if (analysis.includes('intermediate')) {
            aiPath = 'intermediate';
          } else {
            aiPath = 'essentials';
          }
        }
        // Try the latest AI assessment result for learning path
        else {
          try {
            const latestAIResult = await userRef
              .collection("assessmentResults_ai")
              .orderBy("metadata.timestamp", "desc")
              .limit(1)
              .get();
            
            if (!latestAIResult.empty) {
              const aiData = latestAIResult.docs[0].data();
              aiPath = aiData.metadata?.learningPath || aiData.learningPath;
            }
          } catch (error) {
            console.log('Could not fetch AI assessment result for learning path');
          }
        }
      }

      return {
        digital: digitalPath,
        soft: softPath,
        ai: aiPath
      };
    } catch (error) {
      console.error("Error getting user learning paths:", error);
      return { digital: null, soft: null, ai: null };
    }
  }

  // Helper function to map section numbers to learning path names
  function mapSectionToLearningPath(sectionNumber) {
    if (!sectionNumber && sectionNumber !== 0) return null;

    const pathMap = {
      1: 'Essentials',
      2: 'Intermediate',
      3: 'Advanced',
      4: 'Champions'
    };

    return pathMap[Number(sectionNumber)] || null;
  }

  // Modified function to load course recommendations based on pathwayId
  async function loadRecommendations(learner, pathwayId) {
    try {
      // No longer showing loading overlay here since we use the card border animation
      if (!learner.company) {
        console.error("Company information missing for learner:", learner);
        throw new Error("Company information missing");
      }

      const userRef = db
        .collection("companies")
        .doc(learner.company)
        .collection("users")
        .doc(learner.id);

      const userDoc = await userRef.get();
      if (!userDoc.exists) throw new Error("User not found");

      const userData = userDoc.data();
      const userLearningPaths = await getUserLearningPaths(learner);
      console.log("User learning paths:", userLearningPaths, "Current pathway:", pathwayId);

      const matchingTypes = Object.entries(userLearningPaths)
        .filter(([_, path]) => path && path.toLowerCase() === pathwayId.toLowerCase())
        .map(([type]) => type);

      console.log("Assessment types matching pathway:", matchingTypes);

      const recommendations = await Promise.all([
        matchingTypes.includes('digital') ?
          fetchDigitalRecommendations(userRef, userData).then(recs =>
            recs.map(rec => ({ ...rec, assessmentType: 'Digital Skills' }))
          ) : [],
        matchingTypes.includes('soft') ?
          fetchSoftSkillsRecommendations(userRef, userData).then(recs =>
            recs.map(rec => ({ ...rec, assessmentType: 'Soft Skills' }))
          ) : [],
        matchingTypes.includes('ai') ?
          fetchAIRecommendations(userRef, userData).then(recs =>
            recs.map(rec => ({ ...rec, assessmentType: 'AI Skills' }))
          ) : []
      ]);

      courseRecommendations = recommendations.flat();
      console.log("Filtered course recommendations:", courseRecommendations);

    } catch (error) {
      console.error("Error loading course recommendations:", error);
      courseRecommendations = [];
    } finally {
      // No longer hiding loading overlay here since we use the card border animation
    }
  }

  // Helper function to fetch digital skills recommendations
  async function fetchDigitalRecommendations(userRef, userData) {
    try {
      const lastAssessmentId = userData.lastAssessmentId;
      if (!lastAssessmentId) return [];

      const assessmentDoc = await userRef
        .collection("assessmentResults")
        .doc(lastAssessmentId)
        .get();

      if (!assessmentDoc.exists) return [];

      const assessmentData = assessmentDoc.data();
      return assessmentData.courseRecommendations?.map(rec => ({
        course: rec.courseName,
        reason: rec.justification
      })) || [];
    } catch (error) {
      console.error("Error fetching digital skills recommendations:", error);
      return [];
    }
  }

  // Helper function to fetch soft skills recommendations
  async function fetchSoftSkillsRecommendations(userRef, userData) {
    try {
      const lastSoftSkillsAssessmentId = userData.lastSoftSkillsAssessmentId;
      if (!lastSoftSkillsAssessmentId) return [];

      const assessmentDoc = await userRef
        .collection("softSkillsAssessmentResults")
        .doc(lastSoftSkillsAssessmentId)
        .get();

      if (!assessmentDoc.exists) return [];

      const assessmentData = assessmentDoc.data();
      return assessmentData.courseRecommendations?.map(rec => ({
        course: rec.courseName,
        reason: rec.justification
      })) || [];
    } catch (error) {
      console.error("Error fetching soft skills recommendations:", error);
      return [];
    }
  }

  // Helper function to fetch AI skills recommendations
  async function fetchAIRecommendations(userRef, userData) {
    try {
      const lastAIAssessmentId = userData.lastAssessmentId_ai;
      if (!lastAIAssessmentId) return [];

      const assessmentDoc = await userRef
        .collection("assessmentResults_ai")
        .doc(lastAIAssessmentId)
        .get();

      if (!assessmentDoc.exists) return [];

      const assessmentData = assessmentDoc.data();
      return assessmentData.courseRecommendations?.map(rec => ({
        course: rec.courseName,
        reason: rec.justification,
        isCurrentPath: rec.isCurrentPath
      })) || [];
    } catch (error) {
      console.error("Error fetching AI skills recommendations:", error);
      return [];
    }
  }

  // Modified function to load other path recommendations based on pathwayId
  async function loadOtherPathRecommendations(learner, pathwayId) {
    try {
      // No longer showing loading overlay here since we use the card border animation
      if (!learner.company) {
        console.error("Company information missing for learner:", learner);
        throw new Error("Company information missing");
      }

      const userRef = db
        .collection("companies")
        .doc(learner.company)
        .collection("users")
        .doc(learner.id);

      const userDoc = await userRef.get();
      if (!userDoc.exists) throw new Error("User not found");

      const userData = userDoc.data();
      const userLearningPaths = await getUserLearningPaths(learner);

      const matchingTypes = Object.entries(userLearningPaths)
        .filter(([_, path]) => path && path.toLowerCase() === pathwayId.toLowerCase())
        .map(([type]) => type);

      console.log("Assessment types matching pathway for other path recommendations:", matchingTypes);

      const recommendations = await Promise.all([
        matchingTypes.includes('digital') ?
          fetchDigitalOtherPathRecommendations(userRef, userData).then(recs =>
            recs.map(rec => ({ ...rec, assessmentType: 'Digital Skills' }))
          ) : [],
        matchingTypes.includes('soft') ?
          fetchSoftSkillsOtherPathRecommendations(userRef, userData).then(recs =>
            recs.map(rec => ({ ...rec, assessmentType: 'Soft Skills' }))
          ) : [],
        matchingTypes.includes('ai') ?
          fetchAIOtherPathRecommendations(userRef, userData).then(recs =>
            recs.map(rec => ({ ...rec, assessmentType: 'AI Skills' }))
          ) : []
      ]);

      otherPathRecommendations = recommendations.flat();
      console.log("Filtered other path recommendations:", otherPathRecommendations);

    } catch (error) {
      console.error("Error loading other path recommendations:", error);
      otherPathRecommendations = [];
    } finally {
      // No longer hiding loading overlay here since we use the card border animation
    }
  }

  // Helper function for digital skills other path recommendations
  async function fetchDigitalOtherPathRecommendations(userRef, userData) {
    try {
      const lastAssessmentId = userData.lastAssessmentId;
      if (!lastAssessmentId) return [];

      const assessmentDoc = await userRef
        .collection("assessmentResults")
        .doc(lastAssessmentId)
        .get();

      if (!assessmentDoc.exists) return [];

      const assessmentData = assessmentDoc.data();
      return assessmentData.otherPathRecommendations || [];
    } catch (error) {
      console.error("Error fetching digital other path recommendations:", error);
      return [];
    }
  }

  // Helper function for soft skills other path recommendations
  async function fetchSoftSkillsOtherPathRecommendations(userRef, userData) {
    try {
      const lastSoftSkillsAssessmentId = userData.lastSoftSkillsAssessmentId;
      if (!lastSoftSkillsAssessmentId) return [];

      const assessmentDoc = await userRef
        .collection("softSkillsAssessmentResults")
        .doc(lastSoftSkillsAssessmentId)
        .get();

      if (!assessmentDoc.exists) return [];

      const assessmentData = assessmentDoc.data();
      return assessmentData.otherPathRecommendations || [];
    } catch (error) {
      console.error("Error fetching soft skills other path recommendations:", error);
      return [];
    }
  }

  // Helper function for AI skills other path recommendations
  async function fetchAIOtherPathRecommendations(userRef, userData) {
    try {
      const lastAIAssessmentId = userData.lastAssessmentId_ai;
      if (!lastAIAssessmentId) return [];

      const assessmentDoc = await userRef
        .collection("assessmentResults_ai")
        .doc(lastAIAssessmentId)
        .get();

      if (!assessmentDoc.exists) return [];

      const assessmentData = assessmentDoc.data();
      return assessmentData.otherPathRecommendations?.map(rec => ({
        courseName: rec.courseName,
        reason: rec.justification,
        learningPath: rec.learningPath,
        isCurrentPath: rec.isCurrentPath
      })) || [];
    } catch (error) {
      console.error("Error fetching AI skills other path recommendations:", error);
      return [];
    }
  }

  async function createCourseSelectionOverlay(learner, pathwayId) {
    return new Promise(async (resolve) => {
      // Load both core recommendations & other path recommendations
      await Promise.all([
        loadRecommendations(learner, pathwayId),
        loadOtherPathRecommendations(learner, pathwayId),
      ]);

    // Get user learning paths to determine which assessments they've taken
    const userLearningPaths = await getUserLearningPaths(learner);

    // Determine which assessment types the user has completed
    const hasDigital = userLearningPaths.digital !== null;
    const hasSoft = userLearningPaths.soft !== null;
    const hasAI = userLearningPaths.ai !== null;

    console.log("Creating overlay for learner:", learner.name, "Pathway:", pathwayId, "Has Digital:", hasDigital, "Has Soft:", hasSoft, "Has AI:", hasAI);

    // Create overlay
    const overlay = document.createElement("div");
    overlay.id = "courseSelectionOverlay";
    overlay.classList.add("overlay");

    // Create content container
    const content = document.createElement("div");
    content.classList.add("overlay-content");

    // Add styles for the improved overlay
    const style = document.createElement("style");
    style.textContent = `
      /* Close button */
      .close-button {
        position: absolute;
        top: 16px;
        right: 16px;
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background-color: #f3f4f6;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: background-color 0.2s;
        z-index: 10;
      }

      .close-button:hover {
        background-color: #e5e7eb;
      }

      .close-button svg {
        width: 18px;
        height: 18px;
        color: #6b7280;
      }
      /* Base overlay styles */
      .overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        backdrop-filter: blur(5px);
      }

      .overlay-content {
        background-color: #fff;
        border-radius: 12px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        width: 95%;
        max-width: 1200px;
        max-height: 85vh;
        overflow-y: auto;
        padding: 24px;
        position: relative;
        display: flex;
        flex-direction: column;
      }

      /* Header styling */
      .overlay-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 16px;
        border-bottom: 1px solid #e5e7eb;
      }

      .overlay-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: #111827;
        margin: 0;
      }

      /* Control panel styling */
      .control-panel {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
        margin-bottom: 20px;
        align-items: center;
      }

      .button-group {
        display: flex;
        gap: 8px;
        align-items: center;
      }

      /* Toggle button styling */
      .toggle-button {
        display: inline-flex;
        align-items: center;
        padding: 8px 12px;
        border-radius: 6px;
        font-size: 0.875rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s;
        background-color: #f3f4f6;
        color: #374151;
        border: none;
      }

      .toggle-button:hover {
        background-color: #e5e7eb;
      }

      .toggle-button svg {
        margin-right: 6px;
        width: 16px;
        height: 16px;
      }

      /* AI Recommendations toggle */
      .ai-recommendations-container {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-left: auto;
      }

      .toggle-switch {
        position: relative;
        display: inline-flex;
        width: 44px;
        height: 22px;
      }

      .toggle-switch input {
        opacity: 0;
        width: 0;
        height: 0;
      }

      .toggle-slider {
        position: absolute;
        cursor: pointer;
        inset: 0;
        background-color: #e5e7eb;
        border-radius: 22px;
        transition: 0.3s;
      }

      .toggle-slider:before {
        position: absolute;
        content: "";
        height: 18px;
        width: 18px;
        left: 2px;
        bottom: 2px;
        background-color: white;
        border-radius: 50%;
        transition: 0.3s;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .toggle-switch input:checked + .toggle-slider {
        background-color: #2563eb;
      }

      .toggle-switch input:checked + .toggle-slider:before {
        transform: translateX(22px);
      }

      .toggle-label {
        font-size: 0.875rem;
        font-weight: 500;
        color: #4b5563;
        display: flex;
        align-items: center;
      }

      .toggle-label svg {
        margin-left: 6px;
        color: #2563eb;
      }

      /* Search and filter */
      .search-filter-container {
        display: flex;
        flex-direction: column;
        gap: 12px;
        margin-bottom: 20px;
      }

      .search-container {
        position: relative;
        width: 100%;
      }

      .search-icon {
        position: absolute;
        left: 12px;
        top: 50%;
        transform: translateY(-50%);
        color: #9ca3af;
        pointer-events: none;
      }

      .course-search-input {
        width: 100%;
        padding: 10px 10px 10px 40px;
        border-radius: 8px;
        border: 1px solid #e5e7eb;
        font-size: 0.875rem;
        transition: border-color 0.2s;
        outline: none;
      }

      .course-search-input:focus {
        border-color: #2563eb;
        box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.1);
      }

      /* Filter container styling is now in the tooltip section above */

      .filter-button {
        padding: 6px 12px;
        border-radius: 8px;
        font-size: 0.75rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s;
        border: none;
        display: flex;
        align-items: center;
      }

      .filter-button.filter-all {
        background-color: #1547bb;
        color: white;
      }

      .filter-button.filter-all:not(.active) {
        background-color: #f3f4f6;
        color: #6b7280;
      }

      .filter-button.filter-digital {
        background-color: #dbeafe;
        color: #1e40af;
      }

      .filter-button.filter-digital.active {
        background-color: #1e40af;
        color: white;
      }

      .filter-button.filter-soft {
        background-color: #f3e8ff;
        color: #7e22ce;
      }

      .filter-button.filter-soft.active {
        background-color: #7e22ce;
        color: white;
      }

      .filter-button.filter-recommended {
        background-color: #ecfdf5;
        color: #047857;
      }

      .filter-button.filter-recommended.active {
        background-color: #047857;
        color: white;
      }

      .filter-button.filter-ai {
        background-color: #dcfce7;
        color: #15803d;
      }

      .filter-button.filter-ai.active {
        background-color: #15803d;
        color: white;
      }

      .filter-button svg {
        margin-right: 6px;
        width: 16px;
        height: 16px;
      }

      /* Course list styling */
      .course-list {
        border-radius: 8px;
        overflow: visible;
      }

      .category-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #111827;
        margin: 0 0 16px 0;
        padding-bottom: 8px;
        border-bottom: 1px solid #e5e7eb;
      }

      .subcategory-title {
        font-size: 1rem;
        font-weight: 500;
        color: #4b5563;
        margin: 16px 0 12px 0;
      }

      .courses-container {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 12px;
        margin-bottom: 24px;
      }

      /* UPDATED: Course item styling - now clickable cards instead of checkbox items */
      .course-item {
        display: flex;
        flex-direction: column;
        padding: 12px;
        border-radius: 6px;
        background-color: #f9fafb;
        border: 1px solid transparent;
        transition: all 0.2s;
        cursor: pointer;
        position: relative;
        min-height: 60px;
      }

      .course-item:hover {
        background-color: #f3f4f6;
        border-color: #d1d5db;
      }

      /* Selected state for course items */
      .course-item.selected {
        border: 2px solid #2563eb;
        box-shadow: 0 0 0 1px rgba(37, 99, 235, 0.2);
        background-color: #eff6ff;
      }

      .course-item.other-path-item {
        background-color: #f0f9ff;
      }

      .course-item.other-path-item:hover {
        background-color: #e0f2fe;
        border-color: #d1d5db;
      }

      .course-item.other-path-item.selected {
        border: 2px solid #0284c7;
        box-shadow: 0 0 0 1px rgba(2, 132, 199, 0.2);
      }

      /* Hide the checkbox */
      .course-checkbox {
        display: none;
      }

      /* Checkmark indicator for selected items */
      .course-item.selected::after {
        content: '';
        position: absolute;
        top: 8px;
        right: 8px;
        width: 18px;
        height: 18px;
        background-color: #2563eb;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .course-item.selected::before {
        content: '';
        position: absolute;
        top: 12px;
        right: 12px;
        width: 6px;
        height: 10px;
        border: solid white;
        border-width: 0 2px 2px 0;
        transform: rotate(45deg);
        z-index: 1;
      }

      .course-item.other-path-item.selected::after {
        background-color: #0284c7;
      }

      .course-label-container {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        align-items: flex-start;
        width: 100%;
      }

      .course-label {
        font-size: 0.8125rem;
        color: #111827;
        flex-grow: 1;
        margin-right: 8px;
        line-height: 1.4;
        /* Prevent long text from breaking layout */
        word-break: break-word;
      }

      /* Skill badge styling */
      .skill-badge {
        display: flex;
        align-items: center;
        padding: 3px 8px;
        border-radius: 12px;
        font-size: 0.6875rem;
        font-weight: 500;
        white-space: nowrap;
        margin-top: 6px;
        align-self: flex-start;
      }

      .digital-badge {
        background-color: #dbeafe;
        color: #1e40af;
      }

      .digital-badge svg {
        margin-right: 4px;
        width: 12px;
        height: 12px;
      }

      .soft-badge {
        background-color: #f3e8ff;
        color: #7e22ce;
      }

      .soft-badge svg {
        margin-right: 4px;
        width: 12px;
        height: 12px;
      }

      /* Recommended tag styling */
      .recommended-tag {
        display: inline-flex;
        align-items: center;
        font-size: 0.6875rem;
        color: #059669;
        margin-left: 6px;
        transition: opacity 0.2s;
      }

      .recommended-tag svg {
        margin-right: 4px;
        width: 14px;
        height: 14px;
      }

      /* Recommended tag styling with expandable functionality */
      .recommended-tag.expandable {
        cursor: pointer;
        padding: 3px 8px;
      }

      .recommended-tag .expand-icon {
        margin-left: 4px;
        transition: transform 0.2s;
      }

      /* Updated styles for course item and reason details */
      .course-item {
        position: relative; /* Required for absolute positioning context */
      }

      /* Styles for the overlay itself to handle bottom details */
      .overlay-content {
        padding-bottom: 80px !important; /* Add extra padding at bottom for details */
      }

      /* Basic reason details styles */
      .reason-details {
        position: absolute;
        left: 0;
        right: 0;
        z-index: 30; /* Higher than other elements to appear on top */
        background-color: #f9fafb; /* Match card background */
        border: 1px solid #e5e7eb;
        padding: 8px 12px;
        margin-top: 0; /* Reset margin */
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        font-size: 0.75rem;
        color: #4b5563;
        line-height: 1.4;
        max-height: 200px; /* Limit height for very long content */
        overflow-y: auto; /* Allow scrolling for long content */
      }

      /* Default position - below the card */
      .reason-details.position-below {
        top: 100%;
        border-top: none;
        border-radius: 0 0 6px 6px;
      }

      /* Alternative position - above the card */
      .reason-details.position-above {
        bottom: 100%;
        border-bottom: none;
        border-radius: 6px 6px 0 0;
      }

      /* Special styling for reason details in the scrollable other-path-section */
      .other-path-section .reason-details {
        margin-top: 8px;
        padding-top: 8px;
        border-top: 1px solid #e5e7eb;
        width: 100%;
        order: 1; /* Ensure it appears after the course label */
        position: static; /* Return to normal flow */
        box-shadow: none;
        border: none;
        background: transparent;
        padding: 8px 0;
      }

      /* Make sure action buttons remain visible */
      .action-button-container {
        margin-top: 20px;
        padding-top: 8px;
        border-top: 1px solid #e5e7eb;
        width: 100%;
        order: 1; /* Ensure it appears after the course label */
        position: static; /* Return to normal flow */
        box-shadow: none;
        border: none;
        background: transparent;
        padding: 8px 0;
      }

      /* Updated for other-path-item background */
      .course-item.other-path-item .reason-details {
        background-color: #f0f9ff;
      }

      /* Add hover state for the cards with expanded content */
      .course-item.expanded {
        z-index: 25; /* Raise above other cards but below the details */
      }

      /* Modified filter container to support two rows */
      .filter-container {
        display: flex;
        flex-direction: column;
        gap: 8px;
        width: 100%;
      }

      .filter-row {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
      }

      /* Container for follow-up recommendations with a fixed height and scrolling */
      .other-path-section {
        margin-top: 30px;
        border-top: 2px dashed #e5e7eb;
        padding-top: 20px;
        transition: opacity 0.4s ease, max-height 0.4s ease;
        position: relative;
        z-index: 10; /* Lower z-index than tooltips */
        max-height: 400px; /* Fixed height for the follow-up section */
        overflow-y: auto; /* Enable vertical scrolling */
        padding-bottom: 20px; /* Add some bottom padding */
      }

      /* Scroll styling for modern browsers */
      .other-path-section::-webkit-scrollbar {
        width: 8px;
      }

      .other-path-section::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 10px;
      }

      .other-path-section::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 10px;
      }

      .other-path-section::-webkit-scrollbar-thumb:hover {
        background: #a1a1a1;
      }

      .other-path-section.hidden {
        opacity: 0;
        max-height: 0;
        overflow: hidden;
        pointer-events: none;
      }

      /* Add an indicator to show there's more content to scroll */
      .scroll-indicator {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 20px; /* Reduced height for less obstruction */
        background: linear-gradient(to bottom, rgba(255,255,255,0), rgba(255,255,255,0.6)); /* More subtle gradient */
        pointer-events: none;
        z-index: 20;
        opacity: 0;
        transition: opacity 0.3s;
      }

      .other-path-section.needs-scroll .scroll-indicator {
        opacity: 1;
      }

      .path-badge {
        margin-left: 6px;
        padding: 2px 8px;
        background-color: #e0f2fe;
        color: #0284c7;
        border-radius: 12px;
        font-size: 0.75rem;
      }

      /* Action buttons */
      .action-button-container {
        display: flex;
        justify-content: flex-end;
        gap: 12px;
        margin-top: 20px;
        padding-top: 20px;
        border-top: 1px solid #e5e7eb;
      }

      .action-button {
        padding: 10px 16px;
        border-radius: 8px;
        font-size: 0.875rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s;
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 120px;
      }

      .cancel-button {
        background-color: #f3f4f6;
        color: #4b5563;
        border: 1px solid #d1d5db;
      }

      .cancel-button:hover {
        background-color: #e5e7eb;
      }

      .confirm-button {
        background-color: #2563eb;
        color: white;
        border: none;
      }

      .confirm-button:hover:not(:disabled) {
        background-color: #1d4ed8;
      }

      .confirm-button:disabled {
        background-color: #93c5fd;
        cursor: not-allowed;
      }

      /* Selection count badge */
      .selection-count {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        background-color: #2563eb;
        color: white;
        border-radius: 12px;
        padding: 1px 8px;
        font-size: 0.75rem;
        margin-left: 8px;
      }

      /* Recommendation modal */
      .recommendation-modal-overlay {
        position: fixed;
        top: 0; left: 0; right: 0; bottom: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: rgba(0,0,0,0.4);
        z-index: 10000;
        backdrop-filter: blur(3px);
      }

      .recommendation-modal {
        background-color: #fff;
        padding: 24px;
        border-radius: 12px;
        width: 400px;
        max-width: 90vw;
        box-shadow: 0 20px 25px -5px rgba(0,0,0,0.1), 0 10px 10px -5px rgba(0,0,0,0.04);
      }

      .recommendation-modal h3 {
        margin-top: 0;
        font-size: 1.25rem;
        font-weight: 600;
        color: #111827;
      }

      .recommendation-modal p {
        font-size: 0.875rem;
        color: #4b5563;
      }

      .modal-radio-group {
        margin: 16px 0;
      }

      .modal-radio-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 12px;
        font-size: 0.875rem;
        color: #4b5563;
        cursor: pointer;
      }

      .modal-radio-item input {
        margin: 3px 10px 0 0;
        cursor: pointer;
      }

      .modal-buttons {
        display: flex;
        justify-content: flex-end;
        gap: 12px;
        margin-top: 20px;
      }

      .modal-buttons button {
        padding: 8px 16px;
        border-radius: 6px;
        font-size: 0.875rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s;
      }

      .modal-buttons button:first-child {
        background-color: #f3f4f6;
        color: #4b5563;
        border: 1px solid #d1d5db;
      }

      .modal-buttons button:first-child:hover {
        background-color: #e5e7eb;
      }

      .modal-buttons button:last-child {
        background-color: #2563eb;
        color: white;
        border: none;
      }

      .modal-buttons button:last-child:hover {
        background-color: #1d4ed8;
      }

      /* Responsive adjustments */
      @media (max-width: 768px) {
        .overlay-content {
          padding: 16px;
          width: 95%;
        }

        .courses-container {
          grid-template-columns: 1fr;
        }

        .control-panel {
          flex-direction: column;
          align-items: flex-start;
        }

        .ai-recommendations-container {
          margin-left: 0;
          width: 100%;
          justify-content: space-between;
        }

        .search-filter-container {
          flex-direction: column;
        }

        .filter-container {
          width: 100%;
          justify-content: space-between;
        }
      }
    `;
    document.head.appendChild(style);

    // Header with title
    const header = document.createElement("div");
    header.classList.add("overlay-header");

    const title = document.createElement("h2");
    title.textContent = `Select courses for ${learner.name}`;
    title.classList.add("overlay-title");

    header.appendChild(title);
    content.appendChild(header);

    // Control panel - contains all the buttons
    const controlPanel = document.createElement("div");
    controlPanel.classList.add("control-panel");

    // Button group for selection buttons
    const buttonGroup = document.createElement("div");
    buttonGroup.classList.add("button-group");

    // "Select All" button with icon
    const selectAllButton = document.createElement("button");
    selectAllButton.classList.add("toggle-button", "select-all-button");
    selectAllButton.innerHTML = `
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
        <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" />
        <path fill-rule="evenodd" d="M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z" clip-rule="evenodd" />
      </svg>
      Select All
    `;
    selectAllButton.addEventListener("click", () => {
      const courseItems = courseList.querySelectorAll('.course-item');
      const checkboxes = courseList.querySelectorAll('input[type="checkbox"]');

      // Check if all items are already selected
      const allSelected = Array.from(courseItems).every((item) => item.classList.contains("selected"));

      // Toggle selection state for all items
      courseItems.forEach((item) => {
        if (allSelected) {
          item.classList.remove("selected");
        } else {
          item.classList.add("selected");
        }
      });

      // Update checkbox states to match
      checkboxes.forEach((checkbox) => {
        checkbox.checked = !allSelected;
      });

      updateCourseSelectionButtonState();
      selectAllButton.innerHTML = allSelected ?
        `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
          <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" />
          <path fill-rule="evenodd" d="M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z" clip-rule="evenodd" />
        </svg> Select All` :
        `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
          <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" />
          <path fill-rule="evenodd" d="M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z" clip-rule="evenodd" />
        </svg> Unselect All`;
    });

    buttonGroup.appendChild(selectAllButton);

    // "Select All Recommended" button with icon
    let selectAllRecommendedButton;

    // AI Recommendations toggle
    const aiRecommendationsContainer = document.createElement("div");
    aiRecommendationsContainer.classList.add("ai-recommendations-container");

    if (courseRecommendations.length > 0 || otherPathRecommendations.length > 0) {
      // Toggle switch
      const toggleSwitch = document.createElement("label");
      toggleSwitch.classList.add("toggle-switch");
      toggleSwitch.innerHTML = `
        <input type="checkbox" checked>
        <span class="toggle-slider"></span>
      `;

      const toggleLabel = document.createElement("span");
      toggleLabel.classList.add("toggle-label");
      toggleLabel.innerHTML = `
        AI Recommendations
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clip-rule="evenodd" />
        </svg>
      `;

      aiRecommendationsContainer.appendChild(toggleSwitch);
      aiRecommendationsContainer.appendChild(toggleLabel);

      // "Select All Recommended" button with icon
      selectAllRecommendedButton = document.createElement("button");
      selectAllRecommendedButton.classList.add("toggle-button", "select-recommended-button");
      selectAllRecommendedButton.innerHTML = `
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
        </svg>
        Select All Recommended
      `;

      selectAllRecommendedButton.addEventListener("click", () => {
        // 1) Gather all recommended courses (core + other)
        const recommendedCore = courseRecommendations.map((rec) => rec.course).filter(Boolean);
        const recommendedOther = otherPathRecommendations.map((rec) => rec.courseName).filter(Boolean);
        const allRecommendedTitles = [...recommendedCore, ...recommendedOther];

        // 2) Check if ALL recommended are already selected
        const courseItems = courseList.querySelectorAll('.course-item');
        const checkboxes = courseList.querySelectorAll("input[type='checkbox']");
        const allRecommendedChecked = Array.from(checkboxes)
          .filter((cb) => allRecommendedTitles.includes(cb.value))
          .every((cb) => cb.checked);

        // 3) If ALL recommended are selected, just unselect them immediately (no dialog)
        if (allRecommendedChecked) {
          // Update visual selection state
          courseItems.forEach((item) => {
            const courseTitle = item.dataset.courseTitle;
            if (allRecommendedTitles.includes(courseTitle)) {
              item.classList.remove('selected');
            }
          });

          // Update checkbox states
          checkboxes.forEach((checkbox) => {
            if (allRecommendedTitles.includes(checkbox.value)) {
              checkbox.checked = false;
            }
          });

          selectAllRecommendedButton.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
            </svg>
            Select All Recommended
          `;
          updateCourseSelectionButtonState();
        } else {
          // 4) Otherwise, open the custom radio-button modal
          openRecommendationSelectionModal();
        }
      });

      buttonGroup.appendChild(selectAllRecommendedButton);

      const toggleInput = toggleSwitch.querySelector("input");
      toggleInput.addEventListener("change", () => {
        const showRecommendations = toggleInput.checked;
        toggleLabel.innerHTML = showRecommendations
          ? `AI Recommendations
             <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
               <path fill-rule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clip-rule="evenodd" />
             </svg>`
          : `AI Recommendations Off`;

        // Show/hide recommended tags
        const recommendationElements = courseList.querySelectorAll(".recommended-tag");
        recommendationElements.forEach((element) => {
          if (showRecommendations) {
            element.style.display = "inline-flex";
            setTimeout(() => (element.style.opacity = "1"), 10);
          } else {
            element.style.opacity = "0";
            setTimeout(() => (element.style.display = "none"), 200);
          }
        });

        // Only hide reason details when toggling recommendations off
        if (!showRecommendations) {
          document.querySelectorAll(".reason-details").forEach(el => {
            el.style.display = "none";
          });
          document.querySelectorAll(".expand-icon").forEach(icon => {
            icon.style.transform = "rotate(0)";
          });
        }

        // Show/hide Other Path Recommendations section smoothly
        const otherPathSection = courseList.querySelector(".other-path-section");
        if (otherPathSection) {
          if (showRecommendations) {
            otherPathSection.classList.remove("hidden");
            // Recalculate scroll indicator after animation completes
            setTimeout(() => {
              // Check if the function exists (it will if the other path section was created)
              if (typeof checkScrollNeeded === 'function') {
                checkScrollNeeded();
              }
            }, 300);
          } else {
            otherPathSection.classList.add("hidden");
          }
        }

        // Toggle the "Select All Recommended" button
        if (selectAllRecommendedButton) {
          selectAllRecommendedButton.style.display = showRecommendations ? "inline-flex" : "none";
        }

        updateCourseSelectionButtonState();
      });

      if (selectAllRecommendedButton) {
        selectAllRecommendedButton.style.display =
          courseRecommendations.length > 0 || otherPathRecommendations.length > 0
            ? "inline-flex"
            : "none";
      }
    }

    controlPanel.appendChild(buttonGroup);
    controlPanel.appendChild(aiRecommendationsContainer);
    content.appendChild(controlPanel);

    // Search and filter container
    const searchFilterContainer = document.createElement("div");
    searchFilterContainer.classList.add("search-filter-container");

    // Search input
    const searchContainer = document.createElement("div");
    searchContainer.classList.add("search-container");

    const searchIcon = document.createElement("div");
    searchIcon.classList.add("search-icon");
    searchIcon.innerHTML = `
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" width="20" height="20">
        <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
      </svg>
    `;

    const searchInput = document.createElement("input");
    searchInput.type = "text";
    searchInput.placeholder = "Search courses...";
    searchInput.classList.add("course-search-input");

    searchContainer.appendChild(searchIcon);
    searchContainer.appendChild(searchInput);
    searchFilterContainer.appendChild(searchContainer);

    // Filter buttons (now placed below search bar)
    const filterContainer = document.createElement("div");
    filterContainer.classList.add("filter-container");

    // Create "All" filter button
    const filterAll = document.createElement("button");
    // Set default active state based on user's assessment history
    const assessmentCount = (hasDigital ? 1 : 0) + (hasSoft ? 1 : 0) + (hasAI ? 1 : 0);
    if (assessmentCount !== 1) {
      // If user has multiple types or no types, set "All" as active by default
      filterAll.classList.add("filter-button", "filter-all", "active");
    } else {
      filterAll.classList.add("filter-button", "filter-all");
    }
    filterAll.innerHTML = `
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" width="14" height="14">
        <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
      </svg>
      All
    `;

    // Create Digital Skills filter if applicable
    let filterDigital;
    if (hasDigital) {
      filterDigital = document.createElement("button");
      // If user has only digital skills, set it as active by default
      if (hasDigital && !hasSoft && !hasAI) {
        filterDigital.classList.add("filter-button", "filter-digital", "active");
      } else {
        filterDigital.classList.add("filter-button", "filter-digital");
      }
      filterDigital.innerHTML = `
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" width="14" height="14">
          <path fill-rule="evenodd" d="M3 5a2 2 0 012-2h10a2 2 0 012 2v8a2 2 0 01-2 2h-2.22l.123.489.804.804A1 1 0 0113 18H7a1 1 0 01-.707-1.707l.804-.804L7.22 15H5a2 2 0 01-2-2V5zm5.771 7H5V5h10v7H8.771z" clip-rule="evenodd" />
        </svg>
        Digital Skills
      `;
    }

    // Create Soft Skills filter if applicable
    let filterSoft;
    if (hasSoft) {
      filterSoft = document.createElement("button");
      // If user has only soft skills, set it as active by default
      if (!hasDigital && hasSoft && !hasAI) {
        filterSoft.classList.add("filter-button", "filter-soft", "active");
      } else {
        filterSoft.classList.add("filter-button", "filter-soft");
      }
      filterSoft.innerHTML = `
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" width="14" height="14">
          <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z" />
        </svg>
        Soft Skills
      `;
    }

    // Create AI Skills filter if applicable
    let filterAI;
    if (hasAI) {
      filterAI = document.createElement("button");
      // If user has only AI skills, set it as active by default
      if (!hasDigital && !hasSoft && hasAI) {
        filterAI.classList.add("filter-button", "filter-ai", "active");
      } else {
        filterAI.classList.add("filter-button", "filter-ai");
      }
      filterAI.innerHTML = `
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" width="14" height="14">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
        </svg>
        AI Skills
      `;
    }

    // Create Recommended filter
    const filterRecommended = document.createElement("button");
    filterRecommended.classList.add("filter-button", "filter-recommended");
    filterRecommended.innerHTML = `
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" width="14" height="14">
        <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
      </svg>
      Recommended
    `;

    // Create top row for primary filters
    const topFilterRow = document.createElement("div");
    topFilterRow.classList.add("filter-row");

    // Add primary filters to top row
    topFilterRow.appendChild(filterAll);

    if (hasDigital && filterDigital) {
      topFilterRow.appendChild(filterDigital);
    }

    if (hasSoft && filterSoft) {
      topFilterRow.appendChild(filterSoft);
    }

    if (hasAI && filterAI) {
      topFilterRow.appendChild(filterAI);
    }

    // Create bottom row for recommended filter
    const bottomFilterRow = document.createElement("div");
    bottomFilterRow.classList.add("filter-row");
    bottomFilterRow.appendChild(filterRecommended);

    // Add both rows to the filter container
    filterContainer.appendChild(topFilterRow);
    filterContainer.appendChild(bottomFilterRow);

    searchFilterContainer.appendChild(filterContainer);

    content.appendChild(searchFilterContainer);

    // Course List container
    const courseList = document.createElement("div");
    courseList.classList.add("course-list");

    // Store all course items for filtering
    const allCourseItems = [];

    //
    // 1) CORE LEARNING PATH RECOMMENDATIONS
    //
    const coreSectionTitle = document.createElement("h3");
    coreSectionTitle.textContent = "Core Learning Path Recommendations";
    coreSectionTitle.classList.add("category-title");
    courseList.appendChild(coreSectionTitle);

    const pathwayCourses = learningPathData[pathwayId].courseCategories || [];

    // Filter course categories based on the user's assessment types
    const filteredCourseCategories = pathwayCourses.filter(category => {
      // If the category has at least one course, check the skill type of the first course
      if (category.courses && category.courses.length > 0) {
        const skillType = category.courses[0].skillType?.toLowerCase();

        // Only include digital skill categories if the user has completed digital assessment
        if (skillType === 'digital' && !hasDigital) {
          return false;
        }

        // Only include soft skill categories if the user has completed soft skills assessment
        if (skillType === 'soft' && !hasSoft) {
          return false;
        }

        // Only include AI skill categories if the user has completed AI assessment
        if (skillType === 'ai' && !hasAI) {
          return false;
        }

        return true;
      }
      return false;
    });

    // Display a message if there are no categories to show
    if (filteredCourseCategories.length === 0) {
      const noCoursesMessage = document.createElement("p");
      noCoursesMessage.textContent = "No courses available for your completed assessments.";
      noCoursesMessage.classList.add("text-gray-500", "text-center", "my-4");
      courseList.appendChild(noCoursesMessage);
    } else {
      // Render the filtered categories
      filteredCourseCategories.forEach((category) => {
        const categorySection = document.createElement("div");
        categorySection.classList.add("category-section");

        const categoryTitle = document.createElement("h4");
        categoryTitle.textContent = category.category;
        categoryTitle.classList.add("subcategory-title");
        categorySection.appendChild(categoryTitle);

        const coursesContainer = document.createElement("div");
        coursesContainer.classList.add("courses-container");

        category.courses.forEach((course) => {
          const courseItem = document.createElement("div");
          courseItem.classList.add("course-item");
          courseItem.dataset.courseTitle = course.title;

          // Add skill type class for styling
          if (course.skillType) {
            courseItem.classList.add(`skill-${course.skillType}`);
          }

          // Hidden checkbox for maintaining the data structure
          const courseCheckbox = document.createElement("input");
          courseCheckbox.type = "checkbox";
          courseCheckbox.id = `course-${course.title}`;
          courseCheckbox.value = course.title;
          courseCheckbox.classList.add("course-checkbox");
          courseItem.appendChild(courseCheckbox);

          // Create a container for the course label and skill badge
          const courseLabelContainer = document.createElement("div");
          courseLabelContainer.classList.add("course-label-container");

          const courseLabel = document.createElement("div");
          courseLabel.textContent = course.title;
          courseLabel.classList.add("course-label");

          // Check if the course is recommended
          const recommendation = courseRecommendations.find(
            (rec) => rec.course === course.title
          );
          const isRecommended = !!recommendation;

          if (isRecommended) {
            const recommendedTag = document.createElement("span");
            recommendedTag.classList.add("recommended-tag", "expandable");
            recommendedTag.innerHTML = `
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
              </svg>
              Recommended
              <svg class="expand-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" width="16" height="16">
                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
              </svg>
            `;
            courseLabel.appendChild(recommendedTag);

            // Create hidden details element
            const reasonDetails = document.createElement("div");
            reasonDetails.classList.add("reason-details");
            reasonDetails.textContent = recommendation.reason;
            reasonDetails.style.display = "none";
            courseLabelContainer.appendChild(reasonDetails);

            // Add click event to toggle details
            recommendedTag.addEventListener("click", (e) => {
              e.stopPropagation(); // Prevent course selection
              const details = courseLabelContainer.querySelector(".reason-details");
              const isExpanded = details.style.display !== "none";

              // Close any other open reason details first
              document.querySelectorAll(".reason-details").forEach(detail => {
                if (detail !== details) {
                  detail.style.display = "none";
                }
              });

              // Remove expanded class from all other items
              document.querySelectorAll(".course-item").forEach(item => {
                if (item !== courseItem) {
                  item.classList.remove("expanded");
                }
              });

              // Reset all other expand icons
              document.querySelectorAll(".expand-icon").forEach(icon => {
                if (icon !== recommendedTag.querySelector(".expand-icon")) {
                  icon.style.transform = "rotate(0)";
                }
              });

              // If we're about to show the details, check if we should place them above or below
              if (!isExpanded) {
                // Intelligent positioning - determine if card is near the bottom of the overlay
                const courseRect = courseItem.getBoundingClientRect();
                const overlayRect = document.querySelector('.overlay-content').getBoundingClientRect();
                const spaceBelow = overlayRect.bottom - courseRect.bottom;
                const detailsHeight = 80; // Estimate height, or could measure actual height

                // Remove any existing positioning classes
                details.classList.remove('position-below', 'position-above');

                // If not enough space below, position above
                if (spaceBelow < detailsHeight) {
                  details.classList.add('position-above');
                  // Make sure the overlay scrolls to reveal the details if they go above the visible area
                  if (courseRect.top < overlayRect.top + 60) {
                    document.querySelector('.overlay-content').scrollBy({
                      top: -(detailsHeight + 20),
                      behavior: 'smooth'
                    });
                  }
                } else {
                  // Enough space below, use default position
                  details.classList.add('position-below');
                  // Check if we need to scroll down to ensure visibility
                  if (spaceBelow < detailsHeight * 1.5) {
                    document.querySelector('.overlay-content').scrollBy({
                      top: detailsHeight,
                      behavior: 'smooth'
                    });
                  }
                }
              }

              // Toggle this detail
              details.style.display = isExpanded ? "none" : "block";

              // Toggle expanded class on the parent course item
              if (isExpanded) {
                courseItem.classList.remove("expanded");
              } else {
                courseItem.classList.add("expanded");
              }

              // Update only this icon
              const icon = recommendedTag.querySelector(".expand-icon");
              icon.style.transform = isExpanded ? "rotate(0)" : "rotate(180deg)";
            });
          }

          // Add skill type badge with icon
          if (course.skillType) {
            const skillBadge = document.createElement("span");
            skillBadge.classList.add("skill-badge");

            if (course.skillType === 'digital') {
              skillBadge.classList.add("digital-badge");
              skillBadge.innerHTML = `
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M3 5a2 2 0 012-2h10a2 2 0 012 2v8a2 2 0 01-2 2h-2.22l.123.489.804.804A1 1 0 0113 18H7a1 1 0 01-.707-1.707l.804-.804L7.22 15H5a2 2 0 01-2-2V5zm5.771 7H5V5h10v7H8.771z" clip-rule="evenodd" />
                </svg>
                Digital
              `;
            } else if (course.skillType === 'soft') {
              skillBadge.classList.add("soft-badge");
              skillBadge.innerHTML = `
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z" />
                </svg>
                Soft
              `;
            }

            courseItem.appendChild(skillBadge);
          }

          courseLabelContainer.appendChild(courseLabel);
          courseItem.appendChild(courseLabelContainer);

          // Add click event to the entire course item
          courseItem.addEventListener("click", () => {
            // Toggle selected class and checkbox state
            courseItem.classList.toggle("selected");
            courseCheckbox.checked = courseItem.classList.contains("selected");
            updateCourseSelectionButtonState();
          });

          // Store course item for filtering
          allCourseItems.push({
            element: courseItem,
            title: course.title.toLowerCase(),
            description: (course.description || '').toLowerCase(),
            skillType: course.skillType || ''
          });

          coursesContainer.appendChild(courseItem);
        });

        categorySection.appendChild(coursesContainer);
        courseList.appendChild(categorySection);
      });
    }

    //
    // 2) FOLLOW-UP COURSE RECOMMENDATIONS
    //
    if (otherPathRecommendations.length > 0) {
      // Filter other path recommendations based on the user's assessment types
      const filteredOtherPathRecommendations = otherPathRecommendations.filter(rec => {
        const skillType = rec.assessmentType?.toLowerCase();
        if (skillType?.includes('digital') && !hasDigital) {
          return false;
        }
        if (skillType?.includes('soft') && !hasSoft) {
          return false;
        }
        if (skillType?.includes('ai') && !hasAI) {
          return false;
        }
        return true;
      });

      if (filteredOtherPathRecommendations.length > 0) {
        const otherPathSection = document.createElement("div");
        otherPathSection.classList.add("category-section", "other-path-section");
        // Shown by default if AI toggle is checked.

        // Title with info icon
        const sectionTitle = document.createElement("h3");
        sectionTitle.classList.add("category-title");
        sectionTitle.innerHTML = `
          Follow-Up Course Recommendations
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" width="16" height="16" style="display: inline-block; margin-left: 8px; color: #6b7280; cursor: pointer;" title="Builds on core learning path knowledge">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
          </svg>
        `;
        otherPathSection.appendChild(sectionTitle);

        // Add scroll indicator element
        const scrollIndicator = document.createElement("div");
        scrollIndicator.classList.add("scroll-indicator");
        otherPathSection.appendChild(scrollIndicator);

        // Function to check if scrolling is needed and add the indicator class
        function checkScrollNeeded() {
          if (otherPathSection.scrollHeight > otherPathSection.clientHeight) {
            otherPathSection.classList.add("needs-scroll");
          } else {
            otherPathSection.classList.remove("needs-scroll");
          }
        }

        // Initialize and add resize observer to check when content changes
        setTimeout(checkScrollNeeded, 500); // Check after content is likely rendered

        // Add scroll event listener to hide indicator when at bottom
        otherPathSection.addEventListener("scroll", () => {
          // Calculate how close to the bottom we are (in pixels)
          const distanceFromBottom = otherPathSection.scrollHeight - otherPathSection.scrollTop - otherPathSection.clientHeight;

          // If we're very close to the bottom, hide the indicator completely
          if (distanceFromBottom < 10) {
            scrollIndicator.style.opacity = "0";
          }
          // If we're somewhat close, make it partially transparent (gradual fade)
          else if (distanceFromBottom < 30) {
            const opacity = distanceFromBottom / 30; // Creates a value between 0 and 1
            scrollIndicator.style.opacity = opacity.toString();
          }
          // Otherwise show it fully
          else {
            scrollIndicator.style.opacity = "1";
          }
        });

        // Container for the courses
        const coursesContainer = document.createElement("div");
        coursesContainer.classList.add("courses-container");

        filteredOtherPathRecommendations.forEach((rec) => {
          const courseItem = document.createElement("div");
          courseItem.classList.add("course-item", "other-path-item");
          courseItem.dataset.courseTitle = rec.courseName || '';

          // Hidden checkbox
          const courseCheckbox = document.createElement("input");
          courseCheckbox.type = "checkbox";
          courseCheckbox.id = `course-${rec.courseName || ''}`;
          courseCheckbox.value = rec.courseName || '';
          courseCheckbox.classList.add("course-checkbox");
          courseCheckbox.dataset.learningPath = rec.learningPath;
          courseItem.appendChild(courseCheckbox);

          const courseLabelContainer = document.createElement("div");
          courseLabelContainer.classList.add("course-label-container");

          const courseLabel = document.createElement("div");
          courseLabel.classList.add("course-label");

          const courseName = document.createElement("span");
          courseName.textContent = rec.courseName || 'Unknown Course';
          courseLabel.appendChild(courseName);

          const pathBadge = document.createElement("span");
          pathBadge.textContent = rec.learningPath;
          pathBadge.classList.add("path-badge");
          courseLabel.appendChild(pathBadge);

          courseLabelContainer.appendChild(courseLabel);

          const skillType = rec.assessmentType?.toLowerCase().includes('soft') ? 'soft' : 'digital';
          const skillBadge = document.createElement("span");
          skillBadge.classList.add("skill-badge");

          if (skillType === 'digital') {
            skillBadge.classList.add("digital-badge");
            skillBadge.innerHTML = `
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M3 5a2 2 0 012-2h10a2 2 0 012 2v8a2 2 0 01-2 2h-2.22l.123.489.804.804A1 1 0 0113 18H7a1 1 0 01-.707-1.707l.804-.804L7.22 15H5a2 2 0 01-2-2V5zm5.771 7H5V5h10v7H8.771z" clip-rule="evenodd" />
              </svg>
              Digital
            `;
          } else {
            skillBadge.classList.add("soft-badge");
            skillBadge.innerHTML = `
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z" />
              </svg>
              Soft
            `;
          }

          courseItem.appendChild(skillBadge);

          // Add expandable recommended tag
          const recommendedTag = document.createElement("span");
          recommendedTag.classList.add("recommended-tag", "expandable");
          recommendedTag.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
            </svg>
            Recommended
            <svg class="expand-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" width="16" height="16">
              <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          `;
          courseLabel.appendChild(recommendedTag);

          // Create hidden details element
          const reasonDetails = document.createElement("div");
          reasonDetails.classList.add("reason-details");
          reasonDetails.textContent = rec.justification;
          reasonDetails.style.display = "none";
          courseLabelContainer.appendChild(reasonDetails);

          // Add click event to toggle details
          recommendedTag.addEventListener("click", (e) => {
            e.stopPropagation(); // Prevent course selection
            const details = courseLabelContainer.querySelector(".reason-details");
            const isExpanded = details.style.display !== "none";

            // Close any other open reason details first
            document.querySelectorAll(".reason-details").forEach(detail => {
              if (detail !== details) {
                detail.style.display = "none";
              }
            });

            // Remove expanded class from all other items
            document.querySelectorAll(".course-item").forEach(item => {
              if (item !== courseItem) {
                item.classList.remove("expanded");
              }
            });

            // Reset all other expand icons
            document.querySelectorAll(".expand-icon").forEach(icon => {
              if (icon !== recommendedTag.querySelector(".expand-icon")) {
                icon.style.transform = "rotate(0)";
              }
            });

            // If we're about to show the details, check if we should place them above or below
            if (!isExpanded) {
              // Intelligent positioning - determine if card is near the bottom of the overlay
              const courseRect = courseItem.getBoundingClientRect();
              const overlayRect = document.querySelector('.overlay-content').getBoundingClientRect();
              const spaceBelow = overlayRect.bottom - courseRect.bottom;
              const detailsHeight = 80; // Estimate height, or could measure actual height

              // Remove any existing positioning classes
              details.classList.remove('position-below', 'position-above');

              // If not enough space below, position above
              if (spaceBelow < detailsHeight) {
                details.classList.add('position-above');
                // Make sure the overlay scrolls to reveal the details if they go above the visible area
                if (courseRect.top < overlayRect.top + 60) {
                  document.querySelector('.overlay-content').scrollBy({
                    top: -(detailsHeight + 20),
                    behavior: 'smooth'
                  });
                }
              } else {
                // Enough space below, use default position
                details.classList.add('position-below');
                // Check if we need to scroll down to ensure visibility
                if (spaceBelow < detailsHeight * 1.5) {
                  document.querySelector('.overlay-content').scrollBy({
                    top: detailsHeight,
                    behavior: 'smooth'
                  });
                }
              }
            }

            // Toggle this detail
            details.style.display = isExpanded ? "none" : "block";

            // Toggle expanded class on the parent course item
            if (isExpanded) {
              courseItem.classList.remove("expanded");
            } else {
              courseItem.classList.add("expanded");
            }

            // Update only this icon
            const icon = recommendedTag.querySelector(".expand-icon");
            icon.style.transform = isExpanded ? "rotate(0)" : "rotate(180deg)";

            // If this is inside the follow-up recommendations section, scroll to ensure visibility
            if (!isExpanded && courseItem.closest('.other-path-section')) {
              // Get the container and the item's position within it
              const container = courseItem.closest('.other-path-section');
              const itemRect = courseItem.getBoundingClientRect();
              const containerRect = container.getBoundingClientRect();

              // Check if the expanded item is close to the bottom of the visible area
              const distanceFromBottom = containerRect.bottom - (itemRect.bottom + details.offsetHeight);

              // If it's near the bottom, scroll to show the full details
              if (distanceFromBottom < 20) {
                const scrollAmount = Math.min(
                  container.scrollTop + Math.abs(distanceFromBottom) + 40,
                  container.scrollHeight - container.clientHeight
                );
                container.scrollTo({
                  top: scrollAmount,
                  behavior: 'smooth'
                });
              }
            }
          });

          courseItem.appendChild(courseLabelContainer);

          // Add click event to the entire item
          courseItem.addEventListener("click", () => {
            // Toggle selected class and checkbox state
            courseItem.classList.toggle("selected");
            courseCheckbox.checked = courseItem.classList.contains("selected");
            updateCourseSelectionButtonState();
          });

          // Store course item for filtering
          allCourseItems.push({
            element: courseItem,
            title: (rec.courseName || '').toLowerCase(),
            description: (rec.justification || '').toLowerCase(),
            skillType: skillType
          });

          coursesContainer.appendChild(courseItem);
        });

        otherPathSection.appendChild(coursesContainer);
        courseList.appendChild(otherPathSection);
      }
    }

    content.appendChild(courseList);

    // Function to filter courses based on search text and selected filter
    function filterCourses() {
      const searchText = searchInput.value.toLowerCase().trim();

      // Get the active filter type
      const activeFilterEl = document.querySelector('.filter-button.active');
      let activeFilter = 'all';

      if (activeFilterEl.classList.contains('filter-digital')) {
        activeFilter = 'digital';
      } else if (activeFilterEl.classList.contains('filter-soft')) {
        activeFilter = 'soft';
      } else if (activeFilterEl.classList.contains('filter-ai')) {
        activeFilter = 'ai';
      } else if (activeFilterEl.classList.contains('filter-recommended')) {
        activeFilter = 'recommended';
      }

      // Filter course items
      allCourseItems.forEach(item => {
        let visible = true;

        // Apply skill type filter
        if (activeFilter === 'digital' && item.skillType !== 'digital') {
          visible = false;
        } else if (activeFilter === 'soft' && item.skillType !== 'soft') {
          visible = false;
        } else if (activeFilter === 'ai' && item.skillType !== 'ai') {
          visible = false;
        } else if (activeFilter === 'recommended') {
          // Check if this is a recommended course (either core recommendation or follow-up recommendation)
          const isRecommended = item.element.querySelector('.recommended-tag') !== null ||
                               item.element.classList.contains('other-path-item');
          visible = isRecommended;
        }

        // Apply search filter
        if (visible && searchText) {
          visible = item.title.includes(searchText) || item.description.includes(searchText);
        }

        // Update visibility
        if (visible) {
          item.element.style.display = '';
        } else {
          item.element.style.display = 'none';
        }
      });

      // Check if any category is empty and hide it
      const categories = document.querySelectorAll('.category-section');
      categories.forEach(category => {
        const courseItems = category.querySelectorAll('.course-item');
        const visibleItems = Array.from(courseItems).filter(item => item.style.display !== 'none');

        if (visibleItems.length === 0) {
          category.style.display = 'none';
        } else {
          category.style.display = '';
        }
      });

      // Show a message if no courses match
      const noResultsMessage = document.getElementById('no-results-message');
      const hasVisibleCourses = allCourseItems.some(item => item.element.style.display !== 'none');

      if (noResultsMessage) {
        noResultsMessage.remove();
      }

      if (!hasVisibleCourses) {
        const message = document.createElement('div');
        message.id = 'no-results-message';
        message.style.textAlign = 'center';
        message.style.padding = '20px';
        message.style.color = '#6b7280';
        message.innerHTML = `
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" style="margin: 0 auto; height: 48px; width: 48px; color: #9ca3af;">
            <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
          </svg>
          <p style="margin-top: 16px; font-size: 14px;">No courses found matching your criteria</p>
        `;
        courseList.appendChild(message);
      }
    }

    // Add event listeners for search and filter
    searchInput.addEventListener('input', filterCourses);

    filterAll.addEventListener('click', () => {
      document.querySelectorAll('.filter-button').forEach(btn => btn.classList.remove('active'));
      filterAll.classList.add('active');
      filterCourses();
    });

    // Only add event listeners for filters that exist
    if (hasDigital && filterDigital) {
      filterDigital.addEventListener('click', () => {
        document.querySelectorAll('.filter-button').forEach(btn => btn.classList.remove('active'));
        filterDigital.classList.add('active');
        filterCourses();
      });
    }

    if (hasSoft && filterSoft) {
      filterSoft.addEventListener('click', () => {
        document.querySelectorAll('.filter-button').forEach(btn => btn.classList.remove('active'));
        filterSoft.classList.add('active');
        filterCourses();
      });
    }

    if (hasAI && filterAI) {
      filterAI.addEventListener('click', () => {
        document.querySelectorAll('.filter-button').forEach(btn => btn.classList.remove('active'));
        filterAI.classList.add('active');
        filterCourses();
      });
    }

    filterRecommended.addEventListener('click', () => {
      document.querySelectorAll('.filter-button').forEach(btn => btn.classList.remove('active'));
      filterRecommended.classList.add('active');
      filterCourses();
    });

    // Action Buttons
    const actionButtonContainer = document.createElement("div");
    actionButtonContainer.classList.add("action-button-container");

    const cancelButton = document.createElement("button");
    cancelButton.textContent = "Cancel";
    cancelButton.classList.add("action-button", "cancel-button");
    cancelButton.addEventListener("click", () => {
      overlay.remove();
      const learnerCard = document.querySelector(
        `.learner-card[data-learner-name="${learner.name}"]`
      );
      if (learnerCard) {
        const checkbox = learnerCard.querySelector(".enroll-checkbox");
        checkbox.checked = false;
        updateEnrollment(checkbox, learner, pathwayId);
      }

      // Resolve the promise when the user cancels
      resolve();
    });

    const confirmButton = document.createElement("button");
    confirmButton.textContent = "Confirm Selection";
    confirmButton.classList.add("action-button", "confirm-button");
    confirmButton.addEventListener("click", () => {
      const selectedCoursesForLearner = Array.from(
        courseList.querySelectorAll('input[type="checkbox"]:checked')
      ).map((cb) => cb.value);

      selectedCourses.set(learner.name, selectedCoursesForLearner);
      overlay.remove();
      updateTotalCost();

      // Resolve the promise when the user confirms their selection
      resolve();
    });

    confirmButton.disabled = true;
    confirmButton.classList.add("disabled");

    actionButtonContainer.appendChild(cancelButton);
    actionButtonContainer.appendChild(confirmButton);
    content.appendChild(actionButtonContainer);

    // Add a close button to the overlay content
    const closeButton = document.createElement("button");
    closeButton.classList.add("close-button");
    closeButton.innerHTML = `
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
      </svg>
    `;
    closeButton.addEventListener("click", () => {
      overlay.remove();
      const learnerCard = document.querySelector(
        `.learner-card[data-learner-name="${learner.name}"]`
      );
      if (learnerCard) {
        const checkbox = learnerCard.querySelector(".enroll-checkbox");
        checkbox.checked = false;
        updateEnrollment(checkbox, learner, pathwayId);
      }

      // Resolve the promise when the user cancels
      resolve();
    });
    content.appendChild(closeButton);

    overlay.appendChild(content);
    document.body.appendChild(overlay);

    // Helper function for the recommendation selection modal
    function openRecommendationSelectionModal() {
      // Create modal overlay
      const modalOverlay = document.createElement("div");
      modalOverlay.classList.add("recommendation-modal-overlay");

      // Create modal itself
      const modal = document.createElement("div");
      modal.classList.add("recommendation-modal");

      // Title
      const modalTitle = document.createElement("h3");
      modalTitle.textContent = "Select Recommended Courses";
      modal.appendChild(modalTitle);

      // Description
      const modalDesc = document.createElement("p");
      modalDesc.textContent =
        "Choose whether to select only the core recommended courses or both core and follow-up course recommendations.";
      modal.appendChild(modalDesc);

      // Radio group
      const radioGroup = document.createElement("div");
      radioGroup.classList.add("modal-radio-group");

      // Radio 1: Core Only
      const radioCoreWrapper = document.createElement("label");
      radioCoreWrapper.classList.add("modal-radio-item");
      radioCoreWrapper.innerHTML = `
        <input type="radio" name="recommendationChoice" value="core" checked />
        <span>Core Recommendations Only</span>
      `;

      // Radio 2: Core + Follow-Up
      const radioCoreOtherWrapper = document.createElement("label");
      radioCoreOtherWrapper.classList.add("modal-radio-item");
      radioCoreOtherWrapper.innerHTML = `
        <input type="radio" name="recommendationChoice" value="coreOther" />
        <span>Core + Follow-Up Courses (Builds on core knowledge)</span>
      `;

      radioGroup.appendChild(radioCoreWrapper);
      radioGroup.appendChild(radioCoreOtherWrapper);
      modal.appendChild(radioGroup);

      // Buttons
      const modalButtons = document.createElement("div");
      modalButtons.classList.add("modal-buttons");

      // Cancel button
      const cancelButton = document.createElement("button");
      cancelButton.textContent = "Cancel";
      cancelButton.addEventListener("click", () => {
        // Just remove the modal without doing anything
        modalOverlay.remove();
      });

      // Apply button
      const applyButton = document.createElement("button");
      applyButton.textContent = "Apply";
      applyButton.addEventListener("click", () => {
        // Determine which radio is selected
        const choice = modal.querySelector(
          'input[name="recommendationChoice"]:checked'
        ).value;

        // Apply whichever choice
        applyRecommendationChoice(choice);

        // Remove the modal
        modalOverlay.remove();
      });

      modalButtons.appendChild(cancelButton);
      modalButtons.appendChild(applyButton);
      modal.appendChild(modalButtons);

      // Append modal to overlay
      modalOverlay.appendChild(modal);
      document.body.appendChild(modalOverlay);
    }

    // Helper function to apply recommendation choice
    function applyRecommendationChoice(choice) {
      const checkboxes = courseList.querySelectorAll("input[type='checkbox']");
      const courseItems = courseList.querySelectorAll('.course-item');

      // Gather core recommended courses
      const recommendedCore = courseRecommendations.map((rec) => rec.course).filter(Boolean);

      // Potentially gather other path recommended courses
      let allRecommendedTitles = recommendedCore;
      if (choice === "coreOther") {
        const recommendedOther = otherPathRecommendations.map((rec) => rec.courseName).filter(Boolean);
        allRecommendedTitles = [...recommendedCore, ...recommendedOther];
      }

      // Determine if all chosen recommended courses are currently checked
      const allRecommendedChecked = Array.from(checkboxes)
        .filter((cb) => allRecommendedTitles.includes(cb.value))
        .every((cb) => cb.checked);

      // Check/uncheck recommended courses
      courseItems.forEach((item) => {
        const courseTitle = item.dataset.courseTitle;
        if (allRecommendedTitles.includes(courseTitle)) {
          if (allRecommendedChecked) {
            item.classList.remove('selected');
          } else {
            item.classList.add('selected');
          }
        }
      });

      // Update checkbox states to match
      checkboxes.forEach((checkbox) => {
        if (allRecommendedTitles.includes(checkbox.value)) {
          checkbox.checked = !allRecommendedChecked;
        }
      });

      // Update button text accordingly
      selectAllRecommendedButton.innerHTML = allRecommendedChecked
        ? `
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
          </svg>
          Select All Recommended
        `
        : `
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
          </svg>
          Unselect All Recommended
        `;

      updateCourseSelectionButtonState();
    }
    // No additional functions needed for the expandable tag approach

      // Resolve the promise when the overlay is fully created and displayed
      // Add a small delay to ensure animations have started
      setTimeout(() => {
        resolve();
      }, 100);
    });
  }

  function updateCourseSelectionButtonState() {
    const overlay = document.getElementById('courseSelectionOverlay');
    if (!overlay) return;

    const checkboxes = overlay.querySelectorAll('input[type="checkbox"]');
    const confirmButton = overlay.querySelector('.confirm-button');

    const anySelected = Array.from(checkboxes).some(cb => cb.checked);

    if (confirmButton) {
      confirmButton.disabled = !anySelected;
      if (anySelected) {
        confirmButton.classList.remove('disabled');
      } else {
        confirmButton.classList.add('disabled');
      }
    }
  }

  global.createCourseSelectionOverlay = createCourseSelectionOverlay;
})(typeof window !== "undefined" ? window : global);
