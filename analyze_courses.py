#!/usr/bin/env python3
"""
Script to analyze learning path JSON files for data integrity issues.
Checks for duplicate course names across different skill categories.
"""

import json
import sys
from collections import defaultdict

def load_json_file(filepath):
    """Load and parse a JSON file."""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading {filepath}: {e}")
        return None

def extract_course_titles(data, skill_type):
    """Extract all course titles from a learning path data structure."""
    courses = []
    
    for pathway_name, pathway_data in data.items():
        if isinstance(pathway_data, dict) and 'courseCategories' in pathway_data:
            for category in pathway_data['courseCategories']:
                if 'courses' in category:
                    for course in category['courses']:
                        if 'title' in course:
                            courses.append({
                                'title': course['title'],
                                'skill_type': skill_type,
                                'pathway': pathway_name,
                                'category': category.get('category', 'Unknown'),
                                'level': course.get('level', 'Unknown')
                            })
    
    return courses

def analyze_courses():
    """Main analysis function."""
    print("=== Learning Path JSON File Audit ===\n")
    
    # Load all three JSON files
    files = {
        'digital': 'public/learning-path-data.json',
        'soft': 'public/learning-path-data_softskills.json',
        'ai': 'public/learning-path-data_ai.json'
    }
    
    all_courses = []
    file_stats = {}
    
    # Load and extract courses from each file
    for skill_type, filepath in files.items():
        print(f"Loading {skill_type} skills from {filepath}...")
        data = load_json_file(filepath)
        
        if data is None:
            continue
            
        courses = extract_course_titles(data, skill_type)
        all_courses.extend(courses)
        
        file_stats[skill_type] = {
            'total_courses': len(courses),
            'pathways': list(data.keys()) if data else []
        }
        
        print(f"  - Found {len(courses)} courses")
        print(f"  - Pathways: {', '.join(file_stats[skill_type]['pathways'])}")
        print()
    
    # Analyze for duplicates
    print("=== Duplicate Analysis ===")
    title_to_courses = defaultdict(list)
    
    for course in all_courses:
        title_to_courses[course['title']].append(course)
    
    duplicates = {title: courses for title, courses in title_to_courses.items() if len(courses) > 1}
    
    if duplicates:
        print(f"Found {len(duplicates)} duplicate course titles:")
        print()
        
        for title, courses in duplicates.items():
            print(f"DUPLICATE: '{title}'")
            for course in courses:
                print(f"  - {course['skill_type']} skills ({course['pathway']}/{course['category']})")
            print()
    else:
        print("✅ No duplicate course titles found across skill categories!")
        print()
    
    # Check for misplaced courses (courses that might belong in different categories)
    print("=== Potential Misplacement Analysis ===")
    
    # Keywords that suggest a course might belong to a specific skill type
    digital_keywords = ['word', 'excel', 'powerpoint', 'outlook', 'teams', 'sharepoint', 'office', 'microsoft', 'cyber', 'security', 'power bi', 'planner', 'onedrive', 'onenote']
    soft_keywords = ['communication', 'leadership', 'customer', 'sales', 'telephone', 'meeting', 'team', 'conflict', 'negotiation', 'coaching', 'training']
    ai_keywords = ['ai', 'artificial intelligence', 'copilot', 'machine learning', 'automation']
    
    misplaced = []
    
    for course in all_courses:
        title_lower = course['title'].lower()
        
        # Check if AI course contains digital/soft keywords
        if course['skill_type'] == 'ai':
            if any(keyword in title_lower for keyword in digital_keywords if keyword not in ['teams', 'office']):
                misplaced.append((course, 'Contains digital keywords but in AI category'))
            elif any(keyword in title_lower for keyword in soft_keywords):
                misplaced.append((course, 'Contains soft skills keywords but in AI category'))
        
        # Check if digital course contains AI/soft keywords
        elif course['skill_type'] == 'digital':
            if any(keyword in title_lower for keyword in ai_keywords if keyword != 'ai'):
                misplaced.append((course, 'Contains AI keywords but in digital category'))
            elif any(keyword in title_lower for keyword in soft_keywords if keyword not in ['teams', 'meeting']):
                misplaced.append((course, 'Contains soft skills keywords but in digital category'))
        
        # Check if soft course contains digital/AI keywords
        elif course['skill_type'] == 'soft':
            if any(keyword in title_lower for keyword in digital_keywords if keyword not in ['teams', 'meeting']):
                misplaced.append((course, 'Contains digital keywords but in soft skills category'))
            elif any(keyword in title_lower for keyword in ai_keywords if keyword != 'ai'):
                misplaced.append((course, 'Contains AI keywords but in soft skills category'))
    
    if misplaced:
        print(f"Found {len(misplaced)} potentially misplaced courses:")
        print()
        
        for course, reason in misplaced:
            print(f"POTENTIAL ISSUE: '{course['title']}'")
            print(f"  - Currently in: {course['skill_type']} skills")
            print(f"  - Reason: {reason}")
            print()
    else:
        print("✅ No obviously misplaced courses detected!")
        print()
    
    # Summary statistics
    print("=== Summary Statistics ===")
    total_courses = len(all_courses)
    print(f"Total courses across all files: {total_courses}")
    
    for skill_type, stats in file_stats.items():
        percentage = (stats['total_courses'] / total_courses * 100) if total_courses > 0 else 0
        print(f"{skill_type.capitalize()} skills: {stats['total_courses']} courses ({percentage:.1f}%)")
    
    print()
    print("=== Recommendations ===")
    
    if duplicates:
        print("1. CRITICAL: Remove duplicate course titles or ensure they are intentionally different versions")
        print("   - Consider adding version numbers or skill-type prefixes to distinguish courses")
        print("   - Review if duplicates represent the same content or different approaches")
    
    if misplaced:
        print("2. REVIEW: Examine potentially misplaced courses")
        print("   - Verify course content matches the assigned skill category")
        print("   - Consider moving courses to more appropriate categories")
        print("   - Update course titles to be more specific about their focus")
    
    if not duplicates and not misplaced:
        print("✅ Data integrity looks good!")
        print("1. All course titles are unique across skill categories")
        print("2. No obvious misplacements detected")
        print("3. Course distribution appears reasonable")
    
    return len(duplicates), len(misplaced)

if __name__ == "__main__":
    duplicates_count, misplaced_count = analyze_courses()
    
    # Exit with error code if issues found
    if duplicates_count > 0 or misplaced_count > 0:
        sys.exit(1)
    else:
        sys.exit(0)
