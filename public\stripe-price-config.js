/**
 * Stripe Price Configuration
 *
 * This file contains the price IDs for both test and live environments.
 * The appropriate price IDs will be selected based on the current Stripe mode.
 */

(function(global) {
  // Get the current Stripe mode from the window object
  // This is set by the server in stripe-config.js
  const stripeMode = window.STRIPE_MODE || 'test';
  console.log(`Using Stripe in ${stripeMode.toUpperCase()} mode`);

  // Test mode price IDs
  const testPriceIds = {
    // Subscription plans
    assess1: 'price_1RNpW7PqOZsaOO5knOoIbBb0',
    assess100: 'price_1RMP2WPqOZsaOO5kEXSQnBS0',
    assess250: 'price_1RMP2ZPqOZsaOO5kpAcmM2Ur',
    assess500: 'price_1RMP2cPqOZsaOO5kOzAGzqc7',
    freeTrial: 'price_1REmsePqOZsaOO5kgjo38qgp',

    // Top-up packages
    topup100: 'price_1RMP2fPqOZsaOO5kk3cePRnn',
    topup250: 'price_1RMP2hPqOZsaOO5k8YEHnxu4',
    topup500: 'price_1RMP2kPqOZsaOO5kRLmLuPwu'
  };

  // Live mode price IDs
  const livePriceIds = {
    // Subscription plans
    assess1: 'price_1RNo7DL8F65CEkir2BjRHcAh',
    assess100: 'price_1RMjI6L8F65CEkirK8t2V3DG',
    assess250: 'price_1RMjI4L8F65CEkirnaFfwST4',
    assess500: 'price_1RMjI2L8F65CEkirqOJxuD42',

    // Top-up packages
    topup100: 'price_1RMjI0L8F65CEkirmgGw2jDw',
    topup250: 'price_1RMjHuL8F65CEkirXNMZ4UnJ',
    topup500: 'price_1RMjHpL8F65CEkirTkzlPtEB'
  };

  // Select the appropriate price IDs based on the current mode
  const stripePriceIds = stripeMode === 'live' ? livePriceIds : testPriceIds;

  // Function to get a price ID by plan name
  function getPriceId(planName) {
    return stripePriceIds[planName] || null;
  }

  // Update the price IDs in the credit packages
  if (window.creditPackages) {
    if (window.creditPackages[100]) {
      window.creditPackages[100].priceId = stripePriceIds.assess100;
    }
    if (window.creditPackages[250]) {
      window.creditPackages[250].priceId = stripePriceIds.assess250;
    }
    if (window.creditPackages[500]) {
      window.creditPackages[500].priceId = stripePriceIds.assess500;
    }
  }

  // Update the price IDs in the topup packages
  if (window.topupPackages) {
    if (window.topupPackages.topup100) {
      window.topupPackages.topup100.priceId = stripePriceIds.topup100;
    }
    if (window.topupPackages.topup250) {
      window.topupPackages.topup250.priceId = stripePriceIds.topup250;
    }
    if (window.topupPackages.topup500) {
      window.topupPackages.topup500.priceId = stripePriceIds.topup500;
    }
  }

  // Update the price IDs in the stripe products
  if (window.stripeProducts) {
    if (window.stripeProducts.freeTrial) {
      window.stripeProducts.freeTrial.priceId = stripePriceIds.freeTrial;
    }
    if (window.stripeProducts.assess1) {
      window.stripeProducts.assess1.priceId = stripePriceIds.assess1;
    }
    if (window.stripeProducts.assess100) {
      window.stripeProducts.assess100.priceId = stripePriceIds.assess100;
    }
    if (window.stripeProducts.assess250) {
      window.stripeProducts.assess250.priceId = stripePriceIds.assess250;
    }
    if (window.stripeProducts.assess500) {
      window.stripeProducts.assess500.priceId = stripePriceIds.assess500;
    }
  }

  // Export the price IDs and helper function
  global.stripePriceIds = stripePriceIds;
  global.getStripePriceId = getPriceId;

  console.log('Stripe price configuration loaded');

})(typeof window !== 'undefined' ? window : global);
