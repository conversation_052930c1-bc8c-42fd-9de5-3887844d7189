# Stripe Setup Instructions

This document provides instructions for setting up the Stripe products and prices for the Skills Assess subscription system.

## Prerequisites

1. Install the Stripe CLI: https://stripe.com/docs/stripe-cli
2. Authenticate with Stripe: `stripe login`

## Running the Setup Script

1. Make the script executable:
   ```bash
   chmod +x setup-stripe-products.sh
   ```

2. Run the script:
   ```bash
   ./setup-stripe-products.sh
   ```

3. The script will create the following products and prices:
   - Free Trial (£0, 5 assessments, 14 days)
   - Assess100 (£99/month or £1,188/year, 100 assessments)
   - Assess250 (£199/month or £2,388/year, 250 assessments)
   - Assess500 (£299/month or £3,499/year, 500 assessments)

4. The script will output the price IDs for each product. Make note of these IDs.

## Updating the Code

After running the script, you need to update the price IDs in the following files:

### 1. public/subscription-modal.js

Update the `priceId` and `yearlyPriceId` properties for each product:

```javascript
const stripeProducts = {
  freeTrial: {
    id: 'free_trial',
    priceId: 'price_free_trial',
    // ...
  },
  assess100: {
    id: 'assess100',
    priceId: 'price_XXXXXXXXXX', // Replace with the new price ID
    yearlyPriceId: 'price_XXXXXXXXXX', // Replace with the yearly price ID
    // ...
  },
  assess250: {
    id: 'assess250',
    priceId: 'price_XXXXXXXXXX', // Replace with the new price ID
    yearlyPriceId: 'price_XXXXXXXXXX', // Replace with the yearly price ID
    // ...
  },
  assess500: {
    id: 'assess500',
    priceId: 'price_XXXXXXXXXX', // Replace with the new price ID
    yearlyPriceId: 'price_XXXXXXXXXX', // Replace with the yearly price ID
    // ...
  }
};
```

### 2. public/stripe-handler.js

Update the `priceId` and `yearlyPriceId` properties for each package:

```javascript
const creditPackages = {
  100: {
    credits: 100,
    priceId: 'price_XXXXXXXXXX', // Replace with the new price ID
    yearlyPriceId: 'price_XXXXXXXXXX', // Replace with the yearly price ID
    // ...
  },
  250: {
    credits: 250,
    priceId: 'price_XXXXXXXXXX', // Replace with the new price ID
    yearlyPriceId: 'price_XXXXXXXXXX', // Replace with the yearly price ID
    // ...
  },
  500: {
    credits: 500,
    priceId: 'price_XXXXXXXXXX', // Replace with the new price ID
    yearlyPriceId: 'price_XXXXXXXXXX', // Replace with the yearly price ID
    // ...
  }
};
```

## Verifying the Setup

You can verify that the products and prices were created correctly by running:

```bash
stripe products list
stripe prices list
```

## Setting Up Webhook

To handle subscription events, you need to set up a webhook in the Stripe dashboard:

1. Go to the Stripe Dashboard > Developers > Webhooks
2. Click "Add endpoint"
3. Enter your webhook URL (e.g., `https://your-domain.com/webhook`)
4. Select the following events:
   - `checkout.session.completed`
   - `customer.subscription.created`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`
   - `invoice.payment_succeeded`
5. Click "Add endpoint"
6. Copy the signing secret and set it as the `STRIPE_WEBHOOK_SECRET` environment variable in your server

## Subscription Renewal Tracking

The system tracks subscription renewals using the `invoice.payment_succeeded` event from Stripe. When a subscription renews:

1. The system detects the renewal through the `invoice.payment_succeeded` event with `billing_reason` set to `subscription_cycle`
2. The subscription end date is updated by adding exactly one year to the current end date for yearly subscriptions
3. The renewal count is incremented and stored in the `subscriptionRenewalCount` field
4. New credits are added based on the subscription plan
5. The renewal is recorded in the subscription history with details about the previous and new end dates

This ensures accurate tracking of subscription renewals and proper credit allocation.

## Subscription Changes (Upgrades/Downgrades)

When a user upgrades or downgrades their subscription:

1. The change is scheduled to take effect at the next billing cycle
2. For yearly subscriptions, the next billing date is determined by the current subscription end date
3. The change is recorded in the database with a `pendingSubscriptionChange` field
4. When the subscription renews, the pending change takes effect
5. The user receives the appropriate credits for their new plan
6. The `pendingSubscriptionChange` flag is removed from the database
7. The subscription type is updated to reflect the new plan

The system uses multiple methods to detect and process subscription changes:

1. It checks for the `scheduledChange` flag in the subscription metadata
2. It detects price changes by comparing the previous and current plan IDs
3. It looks for pending changes in the user's database record
4. It updates the database with the new subscription type and credits
5. It removes the pending change flag when the change takes effect

When processing subscription renewals with scheduled changes:

1. The system first checks the invoice line items for the current plan details
2. It extracts plan information from the price ID, metadata, and line item description
3. It detects if a scheduled change is taking effect based on metadata
4. It updates the subscription type to the new plan
5. It adds the appropriate credits based on the new plan
6. It removes the pending change flag from the database

This ensures that subscription changes are properly scheduled, detected, and applied at the correct time.

## Environment Variables

Make sure the following environment variables are set in your server:

- `STRIPE_SECRET_KEY`: Your Stripe secret key
- `STRIPE_PUBLISHABLE_KEY`: Your Stripe publishable key
- `STRIPE_WEBHOOK_SECRET`: Your Stripe webhook signing secret
