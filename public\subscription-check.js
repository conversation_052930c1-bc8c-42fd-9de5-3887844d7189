/**
 * Subscription Check Utility
 * Provides functions to check if a user has an active subscription
 */

(function(global) {
  'use strict';

  // Cache for subscription status to avoid repeated database queries
  let subscriptionStatusCache = null;
  let lastCheckTime = 0;
  const CACHE_DURATION = 60000; // 1 minute cache duration

  /**
   * Checks if the current user has an active subscription
   * @returns {Promise<boolean>} - Resolves to true if the user has an active subscription
   */
  async function checkSubscriptionAccess() {
    const currentTime = Date.now();

    // Return cached result if available and not expired
    if (subscriptionStatusCache !== null && (currentTime - lastCheckTime) < CACHE_DURATION) {
      console.log('Using cached subscription status:', subscriptionStatusCache);
      return subscriptionStatusCache;
    }

    // Get current user
    const user = firebase.auth().currentUser;
    if (!user) {
      console.log('No user logged in');
      subscriptionStatusCache = false;
      lastCheckTime = currentTime;
      return false;
    }

    try {
      // Get user data from Firestore
      const doc = await firebase.firestore().collection('Admins').doc(user.email).get();
      if (!doc.exists) {
        console.log('User document not found');
        subscriptionStatusCache = false;
        lastCheckTime = currentTime;
        return false;
      }

      const userData = doc.data();

      // Check if subscription is active
      const hasActiveSubscription = userData.subscriptionActive === true;

      // Cache the result
      subscriptionStatusCache = hasActiveSubscription;
      lastCheckTime = currentTime;

      console.log('Subscription status checked:', hasActiveSubscription);
      return hasActiveSubscription;
    } catch (error) {
      console.error('Error checking subscription status:', error);
      // Don't cache errors
      return false;
    }
  }

  /**
   * Clears the subscription status cache
   */
  function clearSubscriptionCache() {
    subscriptionStatusCache = null;
    lastCheckTime = 0;
    console.log('Subscription status cache cleared');
  }

  /**
   * Shows the feature access modal if the user doesn't have an active subscription
   * @param {string} featureName - The name of the feature being accessed
   * @returns {Promise<boolean>} - Resolves to true if the user has access or got a subscription
   */
  async function checkFeatureAccess(featureName) {
    // Hide loading overlay if it's visible
    if (typeof hideLoadingOverlay === 'function') {
      hideLoadingOverlay();
    } else {
      const loadingOverlay = document.getElementById('loading-overlay');
      if (loadingOverlay) {
        loadingOverlay.style.display = 'none';
      }
    }

    const hasAccess = await checkSubscriptionAccess();

    if (!hasAccess) {
      // Show the feature access modal
      if (window.FeatureAccessModal) {
        const confirmed = await window.FeatureAccessModal.show({
          title: 'Subscription Required',
          message: `Access to ${featureName} requires an active subscription. Would you like to select a subscription plan now?`,
          confirmText: 'Select Subscription',
          cancelText: 'Not Now',
          featureName: featureName,
          redirectToInvite: true
        });

        // If user confirmed, they should have been shown the subscription modal
        // Check subscription status again after modal is closed
        if (confirmed) {
          // Clear cache to force a fresh check
          clearSubscriptionCache();
          return await checkSubscriptionAccess();
        }

        return false;
      } else {
        console.error('FeatureAccessModal not available');
        return false;
      }
    }

    return true;
  }

  // Public API
  global.SubscriptionCheck = {
    checkAccess: checkSubscriptionAccess,
    checkFeatureAccess: checkFeatureAccess,
    clearCache: clearSubscriptionCache
  };
})(typeof window !== 'undefined' ? window : global);
