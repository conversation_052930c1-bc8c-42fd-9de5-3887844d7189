// pageLoader.js
(function(global) {
    function loadWelcomePage(mainContent) {
        return fetch('welcome.html')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.text();
            })
            .then(data => {
                mainContent.innerHTML = data;
                return 'welcome';
            });
    }

    function loadAssessmentsPage(mainContent, userCompany) {
        if (typeof cleanupAssessmentsListener === 'function') {
            cleanupAssessmentsListener();
        }
        showLoadingOverlay();

        // Ensure we use the right company name when in demo mode
        const currentCompany = window.isDemoMode ? 'Barefoot eLearning' : userCompany;

        return fetch('assessments.html')
            .then(response => response.text())
            .then(data => {
                mainContent.innerHTML = data;
                return 'assessments';
            })
            .then(contentType => {
                const scriptElements = document.querySelectorAll('script[src="assessments.js"]');
                scriptElements.forEach(script => script.remove());

                return new Promise((resolve, reject) => {
                    const assessmentScript = document.createElement('script');
                    assessmentScript.src = 'assessments.js';
                    assessmentScript.onload = () => {
                        if (typeof initializeAssessments === 'function' &&
                            typeof updateAssessmentData === 'function' &&
                            typeof addEventListenersToSendReminderIcons === 'function' &&
                            typeof addEmailEventListeners === 'function') {
                            initializeAssessments(currentCompany)
                                .then(() => {
                                    updateAssessmentData(currentCompany);
                                    addEventListenersToSendReminderIcons();
                                    addEmailEventListeners();
                                    const searchInput = document.getElementById('search-input');
                                    if (searchInput && typeof applyAllFilters === 'function') {
                                        searchInput.addEventListener('input', debounce(applyAllFilters, 300));
                                    }
                                    resolve(contentType);
                                })
                                .catch(reject);
                        } else {
                            reject(new Error('Required functions not found in assessments.js'));
                        }
                    };
                    assessmentScript.onerror = () => reject(new Error('Failed to load assessments.js'));
                    document.body.appendChild(assessmentScript);
                });
            })
            .finally(() => {
                hideLoadingOverlay();
            });
    }

    function loadDashboardPage(mainContent, userCompany, updateActiveLink) {
        showLoadingOverlay();

        // Ensure we use the right company name when in demo mode
        const currentCompany = window.isDemoMode ? 'Barefoot eLearning' : userCompany;

        return fetch('dashboard.html')
            .then(response => response.text())
            .then(data => {
                mainContent.innerHTML = data;
                return 'dashboard';
            })
            .then(contentType => {
                const scriptElements = document.querySelectorAll('script[src="dashboard.js"]');
                scriptElements.forEach(script => script.remove());

                return new Promise((resolve, reject) => {
                    const dashboardScript = document.createElement('script');
                    dashboardScript.src = 'dashboard.js';
                    dashboardScript.onload = () => {
                        if (typeof updateDashboardData === 'function' &&
                            typeof initializeTable === 'function' &&
                            typeof setupDashboardEventListeners === 'function' &&
                            typeof handleScrollEvent === 'function') {

                            // Clear any cached data when loading with possibly different company
                            if (window.isDemoMode) {
                                sessionStorage.removeItem('dashboardData');
                                sessionStorage.removeItem('dashboardLastFetchTime');
                                sessionStorage.removeItem('tableData');
                                sessionStorage.removeItem('tableLastFetchTime');
                            }

                            Promise.all([
                                updateDashboardData(currentCompany),
                                initializeTable(currentCompany)
                            ]).then(() => {
                                setupDashboardEventListeners();
                                handleScrollEvent();
                                const allAssessmentsBtn = document.getElementById('all-assessments-btn');
                                if (allAssessmentsBtn && typeof handleAllAssessmentsClick === 'function') {
                                    allAssessmentsBtn.addEventListener('click', () => handleAllAssessmentsClick(mainContent));
                                }
                                if (typeof updateActiveLink === 'function') {
                                    updateActiveLink(contentType);
                                }
                                resolve(contentType);
                            }).catch(reject);
                        } else {
                            reject(new Error('Required functions not found in dashboard.js'));
                        }
                    };
                    dashboardScript.onerror = () => reject(new Error('Failed to load dashboard.js'));
                    document.body.appendChild(dashboardScript);
                });
            })
            .finally(() => {
                hideLoadingOverlay();
            });
    }

    function loadLearningPathsPage(mainContent) {
        console.log('Loading learningpaths.html');
        showLoadingOverlay();

        return fetch('learningpathui.html')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.text();
            })
            .then(data => {
                mainContent.innerHTML = data;
                return 'learningpaths';
            })
            .then(contentType => {
                const oldScriptElements = document.querySelectorAll('script[src="learningpaths.js"]');
                oldScriptElements.forEach(script => script.remove());

                return new Promise((resolve, reject) => {
                    const script = document.createElement('script');
                    script.src = 'learningpathui.js';
                    script.onload = function() {
                        console.log('learningpathui.js loaded and executed');
                        if (typeof initializeCardClickEvent === 'function') {
                            initializeCardClickEvent();
                            resolve(contentType);
                        } else {
                            reject(new Error('initializeCardClickEvent function not found'));
                        }
                    };
                    script.onerror = () => reject(new Error('Failed to load learningpathui.js'));
                    document.body.appendChild(script);
                });
            })
            .finally(() => {
                hideLoadingOverlay();
            });
    }

    function loadInvitePage(mainContent, userCompany) {
        console.log('Loading invite page...');
        showLoadingOverlay();

        // Reset any existing payment handler state
        if (window.PaymentHandler && window.PaymentHandler.resetPaymentHandler) {
            window.PaymentHandler.resetPaymentHandler();
        }

        return fetch('invite.html')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.text();
            })
            .then(data => {
                mainContent.innerHTML = data;
                return 'invite';
            })
            .then(contentType => {
                // Remove any existing scripts to prevent duplicate listeners
                const oldScriptElements = document.querySelectorAll('script[src="invite.js"], script[src*="papaparse"]');
                oldScriptElements.forEach(script => script.remove());

                return new Promise((resolve, reject) => {
                    // First load Papa Parse
                    const papaScript = document.createElement('script');
                    papaScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/PapaParse/5.3.0/papaparse.min.js';
                    papaScript.onload = function() {
                        console.log('Papa Parse loaded');

                        // Then load invite.js
                        const inviteScript = document.createElement('script');
                        inviteScript.src = 'invite.js';
                        inviteScript.onload = function() {
                            console.log('invite.js loaded');

                            // Check if required functions are available
                            if (typeof initializeInviteUsers === 'function') {
                                // Initialize the invite page
                                Promise.resolve()
                                    .then(async () => {
                                        try {
                                            // Initialize invite users - this will set up all necessary event listeners
                                            await initializeInviteUsers(userCompany);
                                            
                                            // Track invitations page access using enhanced tracker
                                            if (window.UserJourneyTracker) {
                                                window.UserJourneyTracker.trackInvitationsFeature('accessed');
                                            }
                                            
                                            resolve(contentType);
                                        } catch (error) {
                                            console.error('Error in invite page initialization:', error);
                                            reject(error);
                                        }
                                    })
                                    .catch(error => {
                                        console.error('Error in promise chain:', error);
                                        reject(error);
                                    });
                            } else {
                                reject(new Error('Required functions not found in invite.js'));
                            }
                        };
                        inviteScript.onerror = () => reject(new Error('Failed to load invite.js'));
                        document.body.appendChild(inviteScript);
                    };
                    papaScript.onerror = () => reject(new Error('Failed to load Papa Parse'));
                    document.head.appendChild(papaScript);
                });
            })
            .finally(() => {
                hideLoadingOverlay();
            });
    }


    function loadDetailContent(mainContent, category, userCompany) {
        console.log('Loading detail content for category:', category);
        sessionStorage.setItem('currentCategory', category);
        showLoadingOverlay();

        // Ensure we use the right company name when in demo mode
        const currentCompany = window.isDemoMode ? 'Barefoot eLearning' : userCompany;

        // Store the current company in window for detail.js to access
        window.currentDetailCompany = currentCompany;

        return fetch('detail.html')
            .then(response => response.text())
            .then(html => {
                mainContent.innerHTML = html;
                return 'detail';
            })
            .then(contentType => {
                // Remove any existing detail.js script
                const existingScript = document.querySelector('script[src="detail.js"]');
                if (existingScript) {
                    existingScript.remove();
                }

                return new Promise((resolve, reject) => {
                    const script = document.createElement('script');
                    script.src = 'detail.js';
                    script.onload = function() {
                        console.log('detail.js loaded');
                        if (typeof window.initializeDetail === 'function') {
                            window.initializeDetail(category);
                            resolve(contentType);
                        } else {
                            reject(new Error('initializeDetail function not found'));
                        }
                    };
                    script.onerror = () => reject(new Error('Failed to load detail.js'));
                    document.body.appendChild(script);
                });
            })
            .finally(() => {
                hideLoadingOverlay();
            });
    }

    function loadReportsPage(mainContent, userCompany) {
        showLoadingOverlay();

        // Ensure we use the right company name when in demo mode
        const currentCompany = window.isDemoMode ? 'Barefoot eLearning' : userCompany;

        return fetch('reports.html')
            .then(response => response.text())
            .then(data => {
                mainContent.innerHTML = data;
                return 'reports';
            })
            .then(contentType => {
                const scriptElements = document.querySelectorAll('script[src="reports.js"]');
                scriptElements.forEach(script => script.remove());

                return new Promise((resolve, reject) => {
                    const reportsScript = document.createElement('script');
                    reportsScript.src = 'reports.js';
                    reportsScript.onload = () => {
                        if (typeof window.ReportsManager !== 'undefined') {
                            // Pass userCompany to initialize
                            window.ReportsManager.initialize(currentCompany);
                            resolve(contentType);
                        } else {
                            reject(new Error('ReportsManager not found in reports.js'));
                        }
                    };
                    reportsScript.onerror = () => reject(new Error('Failed to load reports.js'));
                    document.body.appendChild(reportsScript);
                });
            })
            .finally(() => {
                hideLoadingOverlay();
            });
    }

    function loadEnrollmentPage(mainContent, pathway, userCompany, params = {}) {
        showLoadingOverlay();
        console.log('Loading enrollment page with params:', { pathway, params });

        // Ensure we use the right company name when in demo mode
        const currentCompany = window.isDemoMode ? 'Barefoot eLearning' : userCompany;

        return fetch('enrollment.html')
            .then(response => response.text())
            .then(data => {
                mainContent.innerHTML = data;
                return 'enrollment';
            })
            .then(contentType => {
                // Remove any existing enrollment scripts to prevent duplicates
                const oldScriptElements = document.querySelectorAll('script[src="enrollment.js"]');
                oldScriptElements.forEach(script => script.remove());

                return new Promise((resolve, reject) => {
                    const script = document.createElement('script');
                    script.src = 'enrollment.js';
                    script.onload = function() {
                        if (typeof initializeEnrollment === 'function') {
                            // Initialize with the pathway and company
                            initializeEnrollment(pathway, currentCompany, params);
                            resolve(contentType);
                        } else {
                            reject(new Error('initializeEnrollment function not found'));
                        }
                    };
                    script.onerror = () => reject(new Error('Failed to load enrollment.js'));
                    document.body.appendChild(script);
                });
            })
            .finally(() => {
                hideLoadingOverlay();
            });
    }

    // Helper functions
    function showLoadingOverlay() {
        const loadingOverlay = document.getElementById('loading-overlay');
        if (loadingOverlay) {
            loadingOverlay.style.display = 'flex';
        }
    }

    function hideLoadingOverlay() {
        const loadingOverlay = document.getElementById('loading-overlay');
        if (loadingOverlay) {
            loadingOverlay.style.display = 'none';
        }
    }

    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Expose the functions globally
    window.PageLoader = {
        loadWelcomePage,
        loadAssessmentsPage,
        loadDashboardPage,
        loadLearningPathsPage,
        loadInvitePage,
        loadDetailContent,
        loadReportsPage,
        loadEnrollmentPage  // Add this to the exposed API
    };

})(typeof window !== 'undefined' ? window : global);