#!/bin/bash
# This script sets up Stripe products for one-time top-up purchases
# You will need to install the Stripe CLI: https://stripe.com/docs/stripe-cli
# And jq for JSON parsing

# Check if stripe CLI is installed and authenticated
if ! command -v stripe &> /dev/null; then
    echo "Stripe CLI is not installed. Please install it first."
    exit 1
fi

# Check if jq is installed
if ! command -v jq &> /dev/null; then
    echo "jq is not installed. Please install it first."
    exit 1
fi

# 1. TopUp100
echo "Creating TopUp100 product"
TOPUP100_PRODUCT_OUTPUT=$(stripe products create \
  --name="TopUp100" \
  --description="One-time purchase of 100 additional assessments")

# Extract the ID using jq
TOPUP100_PRODUCT_ID=$(echo "$TOPUP100_PRODUCT_OUTPUT" | jq -r '.id')
echo "TopUp100 Product ID: $TOPUP100_PRODUCT_ID"

echo "Creating TopUp100 price"
TOPUP100_PRICE_OUTPUT=$(stripe prices create \
  --product="$TOPUP100_PRODUCT_ID" \
  --unit-amount=9900 \
  --currency=gbp \
  --nickname="topup100" \
  -d "metadata[plan]=TopUp100" \
  -d "metadata[credits]=100")

# Extract the ID using jq
TOPUP100_PRICE_ID=$(echo "$TOPUP100_PRICE_OUTPUT" | jq -r '.id')
echo "TopUp100 Price ID: $TOPUP100_PRICE_ID"

# 2. TopUp250
echo "Creating TopUp250 product"
TOPUP250_PRODUCT_OUTPUT=$(stripe products create \
  --name="TopUp250" \
  --description="One-time purchase of 250 additional assessments")

TOPUP250_PRODUCT_ID=$(echo "$TOPUP250_PRODUCT_OUTPUT" | jq -r '.id')
echo "TopUp250 Product ID: $TOPUP250_PRODUCT_ID"

echo "Creating TopUp250 price"
TOPUP250_PRICE_OUTPUT=$(stripe prices create \
  --product="$TOPUP250_PRODUCT_ID" \
  --unit-amount=19900 \
  --currency=gbp \
  --nickname="topup250" \
  -d "metadata[plan]=TopUp250" \
  -d "metadata[credits]=250")

TOPUP250_PRICE_ID=$(echo "$TOPUP250_PRICE_OUTPUT" | jq -r '.id')
echo "TopUp250 Price ID: $TOPUP250_PRICE_ID"

# 3. TopUp500
echo "Creating TopUp500 product"
TOPUP500_PRODUCT_OUTPUT=$(stripe products create \
  --name="TopUp500" \
  --description="One-time purchase of 500 additional assessments")

TOPUP500_PRODUCT_ID=$(echo "$TOPUP500_PRODUCT_OUTPUT" | jq -r '.id')
echo "TopUp500 Product ID: $TOPUP500_PRODUCT_ID"

echo "Creating TopUp500 price"
TOPUP500_PRICE_OUTPUT=$(stripe prices create \
  --product="$TOPUP500_PRODUCT_ID" \
  --unit-amount=29900 \
  --currency=gbp \
  --nickname="topup500" \
  -d "metadata[plan]=TopUp500" \
  -d "metadata[credits]=500")

TOPUP500_PRICE_ID=$(echo "$TOPUP500_PRICE_OUTPUT" | jq -r '.id')
echo "TopUp500 Price ID: $TOPUP500_PRICE_ID"

echo "Stripe top-up products and prices created successfully!"
echo ""
echo "Please update the following price IDs in your code:"
echo "TopUp100 Price ID: $TOPUP100_PRICE_ID"
echo "TopUp250 Price ID: $TOPUP250_PRICE_ID"
echo "TopUp500 Price ID: $TOPUP500_PRICE_ID"
echo ""
echo "Update these in topup-modal.js and server.js"
