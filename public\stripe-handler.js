(function(global) {
  // Credit package definitions matching the pricing tiers
  const creditPackages = {
    100: {
      credits: 100,
      // Price ID will be set dynamically based on mode in stripe-price-config.js
      priceId: window.STRIPE_MODE === 'live'
        ? 'price_1RMjI6L8F65CEkirK8t2V3DG'  // Live mode price ID
        : 'price_1RMP2WPqOZsaOO5kEXSQnBS0',  // Test mode price ID
      totalPrice: 1000,
      period: 'year',
      highlight: "Popular Choice!",
      description: "Includes 100 assessments",
      popular: true
    },
    250: {
      credits: 250,
      // Price ID will be set dynamically based on mode in stripe-price-config.js
      priceId: window.STRIPE_MODE === 'live'
        ? 'price_1RMjI4L8F65CEkirnaFfwST4'  // Live mode price ID
        : 'price_1RMP2ZPqOZsaOO5kpAcmM2Ur',  // Test mode price ID
      totalPrice: 2000,
      period: 'year',
      highlight: "Best value for medium teams",
      description: "Includes 250 assessments",
    },
    500: {
      credits: 500,
      // Price ID will be set dynamically based on mode in stripe-price-config.js
      priceId: window.STRIPE_MODE === 'live'
        ? 'price_1RMjI2L8F65CEkirqOJxuD42'  // Live mode price ID
        : 'price_1RMP2cPqOZsaOO5kOzAGzqc7',  // Test mode price ID
      totalPrice: 3000,
      period: 'year',
      highlight: "For larger organizations",
      description: "Includes 500 assessments",
    }
  };

  // Make the credit packages available globally
  window.creditPackages = creditPackages;

  // State management
  let isPaymentUIInitialized = false;
  let selectedPackage = null;
  let isClosing = false;
  let stripeInstance = null;
  let isYearlyBilling = true; // Always use yearly billing

  // Track purchase state for better feedback
  let purchaseState = {
    inProgress: false,
    lastAttempt: null,
    lastResult: null,
    sessionId: null,
    redirecting: false
  };

  async function loadStripeJs() {
    if (window.Stripe) {
      return window.Stripe;
    }

    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = 'https://js.stripe.com/v3/';
      script.onload = () => {
        if (window.Stripe) {
          resolve(window.Stripe);
        } else {
          reject(new Error('Stripe.js failed to load'));
        }
      };
      script.onerror = () => reject(new Error('Failed to load Stripe.js'));
      document.head.appendChild(script);
    });
  }

  async function initializeStripe() {
    if (stripeInstance) {
      return stripeInstance;
    }

    try {
      const Stripe = await loadStripeJs();
      // Use the publishable key from the server-generated config
      stripeInstance = Stripe(window.STRIPE_PUBLISHABLE_KEY);
      return stripeInstance;
    } catch (error) {
      console.error('Failed to initialize Stripe:', error);
      throw error;
    }
  }

  async function showCreditPurchaseModal() {
    if (isPaymentUIInitialized) {
      // If the modal is already initialized, just make it visible
      const overlay = document.getElementById('credit-purchase-overlay');
      if (overlay) {
        overlay.style.opacity = '1';
        const modalContent = overlay.querySelector('.modal-content');
        modalContent.style.opacity = '1';
        modalContent.style.transform = 'scale(1)';
      }
      return;
    }

    isClosing = false;
  }

  async function handleCreditPurchase() {
    isClosing = false;

    if (isPaymentUIInitialized) {
      showCreditPurchaseModal();
      return;
    }

    const overlay = document.createElement('div');
    overlay.id = 'credit-purchase-overlay';
    overlay.className = 'modal-overlay';
    overlay.innerHTML = `
      <div class="modal-content">
        <div class="modal-header">
          <h2 class="modal-title">Purchase Subscription</h2>
          <button id="close-credit-modal" class="close-modal-button">
            <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
            </svg>
          </button>
        </div>
        <div id="credit-packages-grid" class="packages-grid">
          <div class="yearly-billing-info">
            <span class="yearly-label">Annual Billing</span>
          </div>
          ${Object.values(creditPackages).map(pkg => {
            // Get yearly price
            const displayPrice = pkg.totalPrice;

            return `
            <div class="credit-package" data-credits="${pkg.credits}">
              ${pkg.popular ? '<div class="popular-badge">MOST POPULAR</div>' : ''}
              <h3 class="package-title">Assess${pkg.credits}</h3>
              <p class="package-highlight">${pkg.highlight}</p>
              <p class="package-description">${pkg.description}</p>
              <div class="package-price">
                <span class="total-price">£${displayPrice}</span>
                <span class="price-per-credit">per year</span>
              </div>
              <div class="monthly-equivalent">(equivalent to £${Math.round(displayPrice / 12)} per month)</div>
              <button class="select-package-btn">Select</button>
            </div>
          `;
          }).join('')}
        </div>
        <div id="checkout-button-container" class="hidden">
          <button id="stripe-checkout-btn" class="stripe-checkout-button">
            <span>Proceed to Checkout</span>
          </button>
        </div>
        <div id="payment-status" class="hidden"></div>
      </div>
    `;

    document.body.appendChild(overlay);
    isPaymentUIInitialized = true;

    // Reset purchase state
    purchaseState = {
      inProgress: false,
      lastAttempt: null,
      lastResult: null,
      sessionId: null,
      redirecting: false
    };

    // Animate modal appearance
    setTimeout(() => {
      if (!isClosing) {
        overlay.style.opacity = '1';
        const modalContent = overlay.querySelector('.modal-content');
        modalContent.style.opacity = '1';
        modalContent.style.transform = 'scale(1)';
      }
    }, 10);

    // Initialize event listeners
    initializeEventListeners(overlay);

    // Initialize Stripe
    try {
      await initializeStripe();
    } catch (error) {
      console.error('Error initializing Stripe:', error);
      showNotification('Could not initialize payment system. Please try again later.', 'error');
    }
  }

  function initializeEventListeners(overlay) {
    const packages = overlay.querySelectorAll('.credit-package');
    const closeButton = overlay.querySelector('#close-credit-modal');
    const checkoutContainer = overlay.querySelector('#checkout-button-container');
    const checkoutButton = overlay.querySelector('#stripe-checkout-btn');

    // No billing toggle needed - always yearly

    // Package selection event
    packages.forEach(pkg => {
      pkg.addEventListener('click', (event) => {
        const packageElement = event.currentTarget;
        const credits = parseInt(packageElement.dataset.credits);
        selectedPackage = creditPackages[credits];
        updatePackageSelection(packageElement);

        // Show checkout button
        checkoutContainer.classList.remove('hidden');
      });
    });

    // Close modal event
    closeButton.addEventListener('click', () => {
      hideCreditPurchaseModal();
    });

    // Checkout button event
    checkoutButton.addEventListener('click', handleStripeCheckout);

    // Close when clicking outside
    overlay.addEventListener('click', (event) => {
      if (event.target === overlay) {
        hideCreditPurchaseModal();
      }
    });

    // Add keyboard event listener for accessibility
    overlay.addEventListener('keydown', (event) => {
      if (event.key === 'Escape') {
        hideCreditPurchaseModal();
      }
    });
  }

  function updatePackageSelection(selectedPackageElement) {
    // Reset all packages
    document.querySelectorAll('.credit-package').forEach(pkg => {
      pkg.classList.remove('selected');
      pkg.style.borderColor = '#E5E7EB';
      pkg.style.background = '#fff';
      const btn = pkg.querySelector('.select-package-btn');
      btn.textContent = 'Select';
      btn.classList.remove('selected');
    });

    // Update selected package
    selectedPackageElement.classList.add('selected');
    selectedPackageElement.style.borderColor = '#1E3A8A';
    selectedPackageElement.style.background = '#EFF6FF';
    const btn = selectedPackageElement.querySelector('.select-package-btn');
    btn.textContent = 'Selected';
    btn.classList.add('selected');

    // Show checkout button
    const checkoutContainer = document.getElementById('checkout-button-container');
    checkoutContainer.classList.remove('hidden');
  }

  async function handleStripeCheckout() {
    if (purchaseState.inProgress) {
      showNotification('Purchase already in progress. Please wait...', 'info');
      return;
    }

    if (!selectedPackage) {
      showNotification('Please select a package first', 'error');
      return;
    }

    const user = firebase.auth().currentUser;
    if (!user) {
      showNotification('You must be logged in to purchase credits', 'error');
      return;
    }

    try {
      // Update purchase state
      purchaseState.inProgress = true;
      purchaseState.lastAttempt = new Date().toISOString();

      // Initialize Stripe
      await initializeStripe();

      // Create checkout session based on subscription status
      let response;

      // Determine if this is a top-up or new subscription
      const db = firebase.firestore();
      const userDoc = await db.collection('Admins').doc(user.email).get();

      if (userDoc.exists) {
        const userData = userDoc.data();
        const isActiveSubscriber = userData.subscriptionActive &&
                                  userData.subscriptionType !== 'freeTrial';

        // Show appropriate notification
        showNotification(
          isActiveSubscriber ? 'Preparing top-up purchase...' : 'Preparing subscription checkout...',
          'info'
        );

        // Use the appropriate endpoint based on subscription status
        if (isActiveSubscriber) {
          // Create a top-up checkout for active subscribers
          response = await fetch('/create-topup-checkout-session', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              priceId: selectedPackage.priceId,
              userId: user.email,
              credits: selectedPackage.credits
            })
          });
        } else {
          // Create a subscription checkout for new/free trial users
          response = await fetch('/create-checkout-session', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              priceId: selectedPackage.priceId,
              userId: user.email,
              credits: selectedPackage.credits
            })
          });
        }
      } else {
        // Fallback if user data not found
        showNotification('Preparing checkout...', 'info');

        response = await fetch('/create-checkout-session', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            priceId: selectedPackage.priceId,
            userId: user.email,
            credits: selectedPackage.credits
          })
        });
      }

      const session = await response.json();

      if (session.error) {
        throw new Error(session.error);
      }

      // Store the session ID for reference
      purchaseState.sessionId = session.id;

      // Hide the modal before redirecting to Stripe
      hideCreditPurchaseModal(false); // Don't reset state

      // Update purchase state
      purchaseState.redirecting = true;

      // Redirect to Stripe Checkout
      showNotification('Redirecting to secure checkout...', 'info');
      const result = await stripeInstance.redirectToCheckout({
        sessionId: session.id
      });

      if (result.error) {
        throw new Error(result.error.message);
      }

      // Store result for tracking
      purchaseState.lastResult = {
        success: true,
        sessionId: session.id,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error in handleStripeCheckout:', error);

      // Update purchase state
      purchaseState.inProgress = false;
      purchaseState.lastResult = {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      };

      // Show error notification
      showNotification('Could not process your request. Please try again later.', 'error');
    }
  }

  async function hideCreditPurchaseModal(resetState = true) {
    // Handle purchase state when closing
    if (resetState && !purchaseState.redirecting) {
      purchaseState = {
        inProgress: false,
        lastAttempt: purchaseState.lastAttempt,
        lastResult: purchaseState.lastResult,
        sessionId: null,
        redirecting: false
      };
    }

    isClosing = true;
    const overlay = document.getElementById('credit-purchase-overlay');
    if (!overlay) return;

    overlay.style.opacity = '0';
    const modalContent = overlay.querySelector('.modal-content');
    modalContent.style.opacity = '0';
    modalContent.style.transform = 'scale(0.95)';

    await new Promise(resolve => setTimeout(resolve, 300));

    if (document.body.contains(overlay)) {
      document.body.removeChild(overlay);
    }

    isPaymentUIInitialized = false;
    if (resetState) {
      selectedPackage = null;
    }
  }

  function showNotification(message, type = 'success') {
    const existingNotifications = document.querySelectorAll('.stripe-notification');
    existingNotifications.forEach(notification => notification.remove());

    const notification = document.createElement('div');
    notification.className = `stripe-notification ${type}`;
    notification.textContent = message;
    document.body.appendChild(notification);

    // Add styles if not already present
    if (!document.getElementById('stripe-notification-styles')) {
      const style = document.createElement('style');
      style.id = 'stripe-notification-styles';
      style.textContent = `
        .stripe-notification {
          position: fixed;
          top: 20px;
          right: 20px;
          padding: 16px 24px;
          border-radius: 8px;
          font-size: 14px;
          font-weight: 500;
          z-index: 1010;
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
          transform: translateX(120%);
          transition: transform 0.3s ease;
          max-width: 350px;
        }

        .stripe-notification.show {
          transform: translateX(0);
        }

        .stripe-notification.success {
          background-color: #1547BB;
          color: white;
        }

        .stripe-notification.error {
          background-color: #FEE2E2;
          color: #991B1B;
          border: 1px solid #FCA5A5;
        }

        .stripe-notification.info {
          background-color: #E0F2FE;
          color: #0C4A6E;
          border: 1px solid #BAE6FD;
        }

        .stripe-notification.warning {
          background-color: #FEF3C7;
          color: #92400E;
          border: 1px solid #FCD34D;
        }

        /* Yearly billing info styles */
        .yearly-billing-info {
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 24px;
          gap: 12px;
        }

        .yearly-label {
          font-size: 18px;
          color: #121C41;
          font-weight: 600;
          text-align: center;
          padding: 8px 16px;
          background-color: #EFF6FF;
          border-radius: 8px;
          border: 1px solid #1547BB;
        }

        .annual-savings {
          font-size: 14px;
          color: #10B981;
          font-weight: 500;
          text-align: center;
          margin-bottom: 12px;
        }

        .monthly-equivalent {
          font-size: 14px;
          color: #6B7280;
          text-align: center;
          margin-bottom: 12px;
          font-style: italic;
        }
      `;
      document.head.appendChild(style);
    }

    // Trigger fade in
    setTimeout(() => {
      notification.classList.add('show');
    }, 10);

    // Remove after delay
    setTimeout(() => {
      notification.classList.remove('show');
      setTimeout(() => {
        if (document.body.contains(notification)) {
          notification.remove();
        }
      }, 300);
    }, 3000);
  }

  // Public API
  global.handleCreditPurchase = handleCreditPurchase;
  global.showCreditPurchaseModal = handleCreditPurchase;
  global.hideCreditPurchaseModal = hideCreditPurchaseModal;

  // Add method to check previous purchase state
  global.getLastPurchaseAttempt = function() {
    return purchaseState;
  };

})(typeof window !== 'undefined' ? window : global);
