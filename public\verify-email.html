<!-- verify-email.html -->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Verification</title>
    <!-- Update Firebase SDK imports -->
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-firestore-compat.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background-color: #f3f4f6;
        }

        .verification-container {
            background: white;
            padding: 2.5rem;
            border-radius: 1rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            max-width: 28rem;
            width: 90%;
            text-align: center;
        }

        .status-icon {
            width: 4rem;
            height: 4rem;
            margin-bottom: 1.5rem;
        }

        .status-icon.success {
            color: #34D399;
        }

        .status-icon.error {
            color: #EF4444;
        }

        .status-icon.loading {
            color: #6366F1;
        }

        h1 {
            color: #111827;
            font-size: 1.5rem;
            margin-bottom: 1rem;
        }

        p {
            color: #6B7280;
            line-height: 1.5;
            margin-bottom: 1.5rem;
        }

        .button {
            background-color: #6366F1;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            border: none;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: background-color 0.2s;
        }

        .button:hover {
            background-color: #4F46E5;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .loading-spinner {
            animation: spin 1s linear infinite;
        }
    </style>
</head>
<body>
    <div class="verification-container">
        <div id="loadingState">
            <svg class="status-icon loading loading-spinner" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            <h1>Verifying Email</h1>
            <p>Please wait while we verify your email address...</p>
        </div>

        <div id="successState" style="display: none;">
            <svg class="status-icon success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h1>Email Verified Successfully!</h1>
            <p>Your email has been successfully verified. The change will be completed within 24 hours.</p>
            <a href="/" class="button">Return to Dashboard</a>
        </div>

        <div id="errorState" style="display: none;">
            <svg class="status-icon error" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h1>Verification Failed</h1>
            <p id="errorMessage">There was an error verifying your email. Please try again or contact support.</p>
            <a href="/" class="button">Return to Dashboard</a>
        </div>
    </div>

    <script>
        // Initialize Firebase with correct config
        const firebaseConfig = {
            apiKey: "AIzaSyDMUQCWXAprahuhrogEYFDEiMxwKALPXxc",
            authDomain: "barefoot-elearning-app.firebaseapp.com",
            projectId: "barefoot-elearning-app",
            databaseURL: "https://barefoot-elearning-app-default-rtdb.firebaseio.com/",
            storageBucket: "barefoot-elearning-app.appspot.com",
            messagingSenderId: "170819735788",
            appId: "1:170819735788:web:223af318437eb5d947d5c9"
        };
        
        // Initialize Firebase
        if (!firebase.apps.length) {
            firebase.initializeApp(firebaseConfig);
        }

        // Get Firestore instance
        const db = firebase.firestore();

        async function verifyEmail() {
    try {
        const urlParams = new URLSearchParams(window.location.search);
        const email = urlParams.get('email');
        
        if (!email) {
            throw new Error('No email provided in URL');
        }

        if (firebase.auth().isSignInWithEmailLink(window.location.href)) {
            // Complete sign-in using the link
            const result = await firebase.auth().signInWithEmailLink(email, window.location.href);
            
            if (result.user) {
                // The user successfully signed in with the link for the new email
                const db = firebase.firestore();
                
                // Look for the Admin doc with a pending emailChange to this email
                const querySnapshot = await db.collection('Admins')
                    .where('emailChange.newEmail', '==', email)
                    .where('emailChange.status', '==', 'pending')
                    .get();

                if (!querySnapshot.empty) {
                    const doc = querySnapshot.docs[0];

                    // 1. **Actually update the Firebase Auth user’s email.** 
                    //    Now that we have reauthenticated with the email link, 
                    //    we can finalize the email change in Auth.
                    await result.user.updateEmail(email);

                    // 2. Mark the Firestore doc as verified
                    await doc.ref.update({
                        'emailChange.status': 'verified',
                        'emailChange.verifiedAt': firebase.firestore.FieldValue.serverTimestamp()
                    });

                    // 3. You could redirect or show success here
                    // showSuccess(); // If you want to display the success screen
                    window.location.href = '/';
                    return;
                }
            }
        }
        throw new Error('Invalid or expired verification link');
    } catch (error) {
        console.error('Verification error:', error);
        showError(error.message);
    }
}

        function showSuccess() {
            document.getElementById('loadingState').style.display = 'none';
            document.getElementById('successState').style.display = 'block';
        }

        function showError(message) {
            document.getElementById('loadingState').style.display = 'none';
            document.getElementById('errorState').style.display = 'block';
            document.getElementById('errorMessage').textContent = message;
        }

        // Start verification process when page loads
        verifyEmail();
    </script>
</body>
</html>