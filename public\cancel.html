<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Payment Cancelled - Skills Assess</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <script src="https://cdnjs.cloudflare.com/ajax/libs/lottie-web/5.7.13/lottie.min.js"></script>
  <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-app.js"></script>
  <script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-auth.js"></script>
  <style>
    body {
      background-color: #f9fafb;
      color: #121c41;
    }
    .cancel-container {
      max-width: 600px;
    }
    .animation-container {
      width: 150px;
      height: 150px;
      margin: 0 auto;
    }
    .button-primary {
      background-color: #1547bb;
      color: white;
      padding: 12px 24px;
      border-radius: 8px;
      font-weight: 500;
      transition: background-color 0.2s;
    }
    .button-primary:hover {
      background-color: #0d338d;
    }
    .button-secondary {
      border: 1px solid #1547bb;
      color: #1547bb;
      padding: 12px 24px;
      border-radius: 8px;
      font-weight: 500;
      transition: all 0.2s;
    }
    .button-secondary:hover {
      background-color: #edf2ff;
      border-color: #0d338d;
    }
  </style>
</head>
<body>
  <div class="min-h-screen flex flex-col items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="cancel-container bg-white shadow-lg rounded-lg p-8 md:p-10">
      <div class="text-center">
        <div class="animation-container" id="cancel-animation"></div>
        <h1 class="mt-4 text-2xl font-bold text-gray-900">Payment Cancelled</h1>
        <p class="mt-2 text-lg text-gray-600">
          Your payment was not completed and you have not been charged.
        </p>
        <div class="mt-8 space-y-4">
          <p class="text-sm text-gray-500">
            Your payment was not processed. You can try again with a different payment method
            or return to the dashboard.
          </p>
          <div class="flex flex-col sm:flex-row gap-4 mt-6">
            <button id="try-again-button" class="button-primary flex-1">
              Try Again
            </button>
            <button id="back-button" class="button-secondary flex-1">
              Back to Dashboard
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    // Initialize Firebase
    const firebaseConfig = {
      apiKey: "AIzaSyDMUQCWXAprahuhrogEYFDEiMxwKALPXxc",
      authDomain: "barefoot-elearning-app.firebaseapp.com",
      projectId: "barefoot-elearning-app",
      databaseURL: "https://barefoot-elearning-app-default-rtdb.firebaseio.com/",
      storageBucket: "barefoot-elearning-app.appspot.com",
      messagingSenderId: "170819735788",
      appId: "1:170819735788:web:223af318437eb5d947d5c9"
    };

    firebase.initializeApp(firebaseConfig);

    // Initialize animation
    const animation = lottie.loadAnimation({
      container: document.getElementById('cancel-animation'),
      renderer: 'svg',
      loop: true,
      autoplay: true,
      path: 'cancel_animation.json' // Make sure this file exists, or replace with another Lottie file
    });

    // Free trial button has been removed

    // Handle try again button
    document.getElementById('try-again-button').addEventListener('click', () => {
      // Redirect to main page where they can access subscription modal again
      window.location.href = 'main.html?showSubscription=true';
    });

    // Handle back button
    document.getElementById('back-button').addEventListener('click', () => {
      window.location.href = 'main.html';
    });

    // Check authentication state on page load
    document.addEventListener('DOMContentLoaded', () => {
      firebase.auth().onAuthStateChanged(async user => {
        if (!user) {
          // If not logged in, redirect to login
          window.location.href = 'index.html?redirect=cancel';
          return;
        }
      });
    });
  </script>
</body>
</html>
