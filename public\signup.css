body {
    background-image: url('BG.png');
    background-size: cover;
    font-family: 'Montserrat', sans-serif;
    background-position: center;
    background-repeat: no-repeat;
    background-attachment: fixed;
    background-color: rgba(255, 255, 255, 0.8);
    margin: 0;
    padding: 0;
    min-height: 100vh;
}

.main-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 2rem;
}

.signup-container {
    width: 100%;
    max-width: 430px;
    padding: 2.5rem;
    background-color: rgba(255, 255, 255, 0.95);
    border-radius: 1.1rem;
    backdrop-filter: blur(12px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.363), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.signup-heading {
    text-align: center;
    margin-bottom: 2.5rem;
}

.signup-heading h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

.signup-heading p {
    margin-top: 0.5rem;
    font-size: 0.875rem;
    color: #4b5563;
    margin-bottom: 0;
}

form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.input-group {
    position: relative;
    width: 100%;
}

.password-group {
    position: relative;
}

.input-group input {
    width: 100%;
    padding: 1rem;
    border: 1px solid #e5e7eb;
    border-radius: 0.75rem;
    outline: none;
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.input-group input[type="password"]::-ms-reveal,
.input-group input[type="password"]::-ms-clear {
    display: none;
}

/* For Webkit browsers (Chrome, Safari, etc.) */
.input-group input[type="password"]::-webkit-contacts-auto-fill-button,
.input-group input[type="password"]::-webkit-credentials-auto-fill-button {
    visibility: hidden;
    display: none !important;
    pointer-events: none;
    position: absolute;
    right: 0;
}

.input-group input:focus {
    border-color: #6366f1;
    background-color: #ffffff;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.password-toggle {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    padding: 0;
    cursor: pointer;
    color: #6b7280;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: color 0.2s ease;
}

.password-toggle:hover {
    color: #4b5563;
}

.password-toggle svg {
    width: 0.8rem;
    height: 0.8rem;
}

.hidden {
    display: none;
}

.error-message {
    color: #ef4444;
    font-size: 0.75rem;
    margin-top: 0.25rem;
    display: none;
}

.terms-privacy {
    font-size: 0.875rem;
    color: #4b5563;
}

.checkbox-container {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
}

.checkbox-container input[type="checkbox"] {
    width: 1rem;
    height: 1rem;
    accent-color: #6366f1;
}

.terms-link, .privacy-link {
    color: #6366f1;
    text-decoration: none;
}

.terms-link:hover, .privacy-link:hover {
    text-decoration: underline;
}

.signup-button button {
    width: 100%;
    padding: 0.75rem 1.5rem;
    background-color: #1547bb;
    color: #fff;
    font-size: 0.875rem;
    font-weight: 500;
    border: none;
    border-radius: 0.75rem;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.signup-button button:hover {
    background-color: #0f3a9c;
}

.signup-button button:disabled {
    background-color: #c7d2fe;
    cursor: not-allowed;
}

.login-link {
    text-align: center;
    margin-top: 1.5rem;
    font-size: 0.875rem;
    color: #4b5563;
}

.login-link a {
    color: #6366f1;
    text-decoration: none;
}

.login-link a:hover {
    text-decoration: underline;
}

#loading-overlay {
    position: fixed;
    z-index: 9999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    display: none;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.loading-animation {
    width: 45px;  /* Reduced from 80px to match login page */
    height: 45px; /* Reduced from 80px to match login page */
}

.loading-text {
    color: white;
    margin-top: 0.75rem;
    font-size: 0.65rem; /* Reduced size for more minimalistic look */
    letter-spacing: 1px;
    opacity: 0.9;
    font-weight: 400;
    text-align: center;
}

.success-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    text-align: center;
}

.success-overlay.hidden {
    display: none;
}

.success-icon {
    width: 80px;
    height: 80px;
    color: #10B981; /* Pleasant green color */
    margin-bottom: 1rem;
}

.success-overlay p {
    font-size: 1.25rem;
    color: #fff; /* White text for contrast */
    margin: 0;
}

.success-message {
    color: #10B981;
    background-color: rgba(16, 185, 129, 0.1);
    border: 1px solid #10B981;
    border-radius: 0.5rem;
    padding: 0.75rem;
    margin-top: 1rem;
    text-align: center;
    font-size: 0.875rem;
}

/* Logo footer styling */
#logo-footer {
    transition: all 0.3s ease;
    z-index: 5; /* Ensure it's below form elements and overlays */
    pointer-events: none; /* Prevent it from intercepting clicks */
}

#logo-footer .logo-img {
    transition: all 0.3s ease;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
    opacity: 0.9; /* Slightly transparent by default */
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    20%, 60% { transform: translateX(-10px); }
    40%, 80% { transform: translateX(10px); }
}

.shake {
    animation: shake 0.5s ease-in-out;
}

@media (max-width: 640px) {
    .main-container {
        padding: 1rem;
    }

    .signup-container {
        padding: 1.5rem;
    }

    /* Adjust logo size for small screens */
    #logo-footer .logo-img {
        max-width: 100px;
    }
}

/* Handle very small screens */
@media (max-width: 480px) {
    #logo-footer .logo-img {
        max-width: 80px;
        opacity: 0.7; /* Make it slightly transparent */
    }
}

/* Hide logo on extremely small screens to prevent overlap */
@media (max-width: 380px) {
    #logo-footer {
        display: none;
    }
}
.signup-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.error-input {
    border-color: #EF4444;
}

.valid-input {
    border-color: #10B981;
}

.hidden {
    opacity: 0;
    transition: opacity 0.2s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.shake {
    animation: shake 0.3s ease-in-out;
}

.notification {
    animation: slideInDown 0.3s ease forwards;
}

@keyframes slideInDown {
    from {
        transform: translate(-50%, -20px);
        opacity: 0;
    }
    to {
        transform: translate(-50%, 0);
        opacity: 1;
    }
}
