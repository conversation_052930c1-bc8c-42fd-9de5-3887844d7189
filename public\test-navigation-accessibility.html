<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Navigation Accessibility Test with Demo Banner</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f8fafc;
        }
        
        /* Mock navigation bar */
        .mock-nav {
            background: white;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            padding: 0.75rem 1rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            transition: top 0.3s ease;
        }
        
        .mock-nav.with-banner {
            top: 0;
        }
        
        .nav-links {
            display: flex;
            gap: 1.5rem;
            align-items: center;
        }
        
        .nav-link {
            color: #374151;
            text-decoration: none;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            transition: all 0.2s;
            cursor: pointer;
            border: 2px solid transparent;
        }
        
        .nav-link:hover {
            background-color: #f3f4f6;
            color: #1f2937;
        }
        
        .nav-link.clicked {
            background-color: #dbeafe;
            border-color: #3b82f6;
            color: #1e40af;
        }
        
        .logo {
            font-weight: 700;
            font-size: 1.25rem;
            color: #1f2937;
        }
        
        .test-content {
            margin-top: 6rem;
            padding: 2rem;
            max-width: 1200px;
            margin-left: auto;
            margin-right: auto;
        }
        
        .test-content.with-banner {
            margin-top: 6rem;
            padding-bottom: 4rem;
        }
        
        .test-section {
            background: white;
            padding: 2rem;
            border-radius: 0.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }
        
        .test-button {
            background: linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            cursor: pointer;
            margin: 0.5rem;
            font-weight: 600;
            transition: all 0.2s;
        }
        
        .test-button:hover {
            background: linear-gradient(135deg, #2563EB 0%, #1E40AF 100%);
            transform: translateY(-1px);
        }
        
        .test-button.danger {
            background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%);
        }
        
        .test-button.danger:hover {
            background: linear-gradient(135deg, #DC2626 0%, #B91C1C 100%);
        }
        
        .click-log {
            background-color: #f1f5f9;
            border: 1px solid #cbd5e1;
            border-radius: 0.375rem;
            padding: 1rem;
            margin-top: 1rem;
            font-family: monospace;
            font-size: 0.875rem;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .status-indicator {
            display: inline-block;
            width: 0.75rem;
            height: 0.75rem;
            border-radius: 50%;
            margin-right: 0.5rem;
        }
        
        .status-indicator.accessible {
            background-color: #10b981;
        }
        
        .status-indicator.blocked {
            background-color: #ef4444;
        }
        
        @media (max-width: 768px) {
            .nav-links {
                gap: 0.75rem;
            }
            
            .nav-link {
                padding: 0.375rem 0.75rem;
                font-size: 0.875rem;
            }
            
            .mock-nav.with-banner {
                top: 0;
            }

            .test-content.with-banner {
                margin-top: 6rem;
                padding-bottom: 3.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Mock Navigation Bar -->
    <nav class="mock-nav" id="mock-nav">
        <div class="logo">SkillsAssess</div>
        <div class="nav-links">
            <a href="#" class="nav-link" data-nav="dashboard">Dashboard</a>
            <a href="#" class="nav-link" data-nav="assessments">Assessments</a>
            <a href="#" class="nav-link" data-nav="reports">Reports</a>
            <a href="#" class="nav-link" data-nav="invitations">Invitations</a>
            <a href="#" class="nav-link" data-nav="learning">Learning Paths</a>
        </div>
    </nav>

    <!-- Test Content -->
    <div class="test-content" id="test-content">
        <div class="test-section">
            <h1>Navigation Accessibility Test with Demo Banner</h1>
            <p>This test verifies that the demo mode banner doesn't interfere with navigation link accessibility.</p>
            
            <div style="margin: 1rem 0;">
                <button class="test-button" onclick="showDemoBanner()">
                    Show Demo Banner
                </button>
                
                <button class="test-button danger" onclick="hideDemoBanner()">
                    Hide Demo Banner
                </button>
                
                <button class="test-button" onclick="clearClickLog()">
                    Clear Click Log
                </button>
            </div>
            
            <div style="margin: 1rem 0;">
                <h3>Navigation Accessibility Status:</h3>
                <div id="accessibility-status">
                    <div><span class="status-indicator accessible"></span>All navigation links are accessible</div>
                </div>
            </div>
            
            <div class="click-log" id="click-log">
                <strong>Navigation Click Log:</strong><br>
                Click on navigation links above to test accessibility...
            </div>
        </div>
        
        <div class="test-section">
            <h2>Test Instructions:</h2>
            <ol style="line-height: 1.8;">
                <li><strong>Show Demo Banner:</strong> Click "Show Demo Banner" to display the demo mode banner</li>
                <li><strong>Test Navigation:</strong> Try clicking on each navigation link (Dashboard, Assessments, Reports, etc.)</li>
                <li><strong>Verify Accessibility:</strong> Ensure all links are clickable and responsive</li>
                <li><strong>Check Mobile:</strong> Resize the browser window to test mobile responsiveness</li>
                <li><strong>Test Exit:</strong> Click the "Exit Demo" button in the banner to test functionality</li>
                <li><strong>Hide Banner:</strong> Use "Hide Demo Banner" to remove the banner and test normal navigation</li>
            </ol>
            
            <h3>Expected Results:</h3>
            <ul style="line-height: 1.6;">
                <li>✅ All navigation links remain fully clickable when demo banner is shown</li>
                <li>✅ Demo banner is clearly visible but doesn't obstruct navigation</li>
                <li>✅ "Exit Demo" button in banner works properly</li>
                <li>✅ Layout adjusts properly on mobile devices</li>
                <li>✅ No UI conflicts or overlapping elements</li>
            </ul>
        </div>
    </div>

    <script>
        let clickCount = 0;
        let bannerVisible = false;
        
        // Demo banner functions (simplified version from main implementation)
        function showDemoBanner() {
            if (bannerVisible) return;
            
            const banner = document.createElement('div');
            banner.id = 'demo-mode-banner';
            banner.style.cssText = `
                position: fixed;
                bottom: 0;
                left: 0;
                right: 0;
                background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
                color: white;
                z-index: 2000;
                box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
                animation: slideUp 0.3s ease-out;
            `;
            
            banner.innerHTML = `
                <div style="
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    padding: 0.5rem 1rem;
                    max-width: 1200px;
                    margin: 0 auto;
                    min-height: 2.5rem;
                ">
                    <div style="display: flex; align-items: center; gap: 0.5rem;">
                        <div style="width: 1rem; height: 1rem; flex-shrink: 0;">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" style="width: 100%; height: 100%;">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" />
                                <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                        </div>
                        <div style="display: flex; align-items: center; gap: 0.5rem;">
                            <span style="font-weight: 600; font-size: 0.8rem; line-height: 1.2; white-space: nowrap;">Demo Mode Active</span>
                            <span style="font-size: 0.7rem; opacity: 0.9; line-height: 1.2; white-space: nowrap;">Viewing sample data</span>
                        </div>
                    </div>
                    <button onclick="hideDemoBanner()" style="
                        display: flex;
                        align-items: center;
                        gap: 0.375rem;
                        background: rgba(255, 255, 255, 0.2);
                        border: 1px solid rgba(255, 255, 255, 0.3);
                        color: white;
                        padding: 0.375rem 0.625rem;
                        border-radius: 0.25rem;
                        font-size: 0.7rem;
                        font-weight: 600;
                        cursor: pointer;
                        transition: all 0.2s ease;
                        white-space: nowrap;
                        flex-shrink: 0;
                    ">
                        <span>Exit Demo</span>
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" style="width: 0.75rem; height: 0.75rem;">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
            `;
            
            // Add CSS animation
            if (!document.getElementById('banner-animations')) {
                const style = document.createElement('style');
                style.id = 'banner-animations';
                style.textContent = `
                    @keyframes slideUp {
                        from { transform: translateY(100%); opacity: 0; }
                        to { transform: translateY(0); opacity: 1; }
                    }
                    @keyframes slideDown {
                        from { transform: translateY(0); opacity: 1; }
                        to { transform: translateY(100%); opacity: 0; }
                    }
                `;
                document.head.appendChild(style);
            }

            document.body.appendChild(banner);

            // Adjust layout
            document.getElementById('mock-nav').classList.add('with-banner');
            document.getElementById('test-content').classList.add('with-banner');
            
            bannerVisible = true;
            logClick('Demo banner shown - navigation should remain accessible');
            updateAccessibilityStatus();
        }
        
        function hideDemoBanner() {
            const banner = document.getElementById('demo-mode-banner');
            if (banner) {
                banner.style.animation = 'slideDown 0.3s ease-out';
                setTimeout(() => {
                    if (banner.parentNode) {
                        banner.parentNode.removeChild(banner);
                    }

                    // Restore layout
                    document.getElementById('mock-nav').classList.remove('with-banner');
                    document.getElementById('test-content').classList.remove('with-banner');

                    bannerVisible = false;
                    logClick('Demo banner hidden - navigation restored to normal');
                    updateAccessibilityStatus();
                }, 300);
            }
        }
        
        function logClick(message) {
            clickCount++;
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('click-log');
            logElement.innerHTML += `<br>[${timestamp}] ${clickCount}. ${message}`;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function clearClickLog() {
            clickCount = 0;
            document.getElementById('click-log').innerHTML = '<strong>Navigation Click Log:</strong><br>Click on navigation links above to test accessibility...';
        }
        
        function updateAccessibilityStatus() {
            const statusElement = document.getElementById('accessibility-status');
            if (bannerVisible) {
                statusElement.innerHTML = `
                    <div><span class="status-indicator accessible"></span>Demo banner active at bottom - navigation fully accessible</div>
                    <div style="font-size: 0.875rem; color: #6b7280; margin-top: 0.25rem;">Banner position: Bottom | Navigation: Unobstructed</div>
                `;
            } else {
                statusElement.innerHTML = `
                    <div><span class="status-indicator accessible"></span>Normal mode - all navigation links accessible</div>
                `;
            }
        }
        
        // Add click handlers to navigation links
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                
                // Remove previous clicked state
                document.querySelectorAll('.nav-link').forEach(l => l.classList.remove('clicked'));
                
                // Add clicked state to current link
                this.classList.add('clicked');
                
                const navItem = this.getAttribute('data-nav');
                logClick(`Navigation link clicked: ${navItem} (${bannerVisible ? 'with banner' : 'without banner'})`);
                
                // Remove clicked state after 2 seconds
                setTimeout(() => {
                    this.classList.remove('clicked');
                }, 2000);
            });
        });
        
        // Initialize
        updateAccessibilityStatus();
    </script>
</body>
</html>
